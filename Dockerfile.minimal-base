# 文件名: Dockerfile.minimal-base
# docker build -t registry.cn-shanghai.aliyuncs.com/chos/hi-ideagen-python3.11-base:7.0 .

FROM python:3.11-slim

# # 配置阿里巴巴源
# 删除官方源文件，写入阿里云源
RUN rm -rf /etc/apt/sources.list.d/* && \
    echo "deb http://mirrors.aliyun.com/debian bookworm main contrib non-free" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian bookworm-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian-security bookworm-security main contrib non-free" >> /etc/apt/sources.list && \
    apt-get clean && \
    apt-get update -o Acquire::Check-Valid-Until=false

# 安装系统级依赖（编译工具和PostgreSQL客户端库）
# 安装系统级依赖（编译工具、PostgreSQL客户端库、libmagic、wget）
RUN apt-get install -y \
    gcc \
    libpq-dev \
    python3-dev \
    libmagic1 \
    ffmpeg \
    wget \
    texlive-full \
    fonts-noto-cjk \
    libreoffice \
    fonts-noto-cjk-extra \
    && fc-cache -f -v \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装最新版 pandoc
ENV PANDOC_VERSION=********
RUN wget https://sun-ih-dev-public.oss-cn-shanghai.aliyuncs.com/poc/pandoc-********-1-amd64.deb && \
    dpkg -i pandoc-${PANDOC_VERSION}-1-amd64.deb && \
    rm pandoc-${PANDOC_VERSION}-1-amd64.deb

# 预安装pip最新版本并配置pip源为阿里云
RUN pip install --no-cache-dir --upgrade pip && \
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com