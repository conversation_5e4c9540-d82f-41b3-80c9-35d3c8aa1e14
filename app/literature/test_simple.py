#!/usr/bin/env python3
"""
简化的GROBID测试脚本
"""

import asyncio
import sys
from pathlib import Path

from app.api.repository import grobid_client

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))



async def simple_test():
    """简单测试"""
    print("🔍 检查GROBID服务...")

    # 1. 健康检查
    is_healthy = await grobid_client.health_check()
    print(f"服务状态: {'✅ 正常' if is_healthy else '❌ 异常'}")

    if not is_healthy:
        print("请确保GROBID服务运行在端口8070")
        return

    # 2. 测试PDF解析
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]

        if not Path(pdf_path).exists():
            print(f"❌ 文件不存在: {pdf_path}")
            return

        print(f"\n📄 正在解析: {pdf_path}")
        print("⏳ 请等待...")

        try:
            # 只测试头部解析（更快）
            result = await grobid_client.parse_pdf_header(pdf_path)

            if result:
                print("✅ 解析成功!")
                print(f"标题: {result.get('title', '未识别')}")
                print(f"作者数量: {len(result.get('authors', []))}")
                print(f"DOI: {result.get('doi', '未识别')}")
                print(f"摘要长度: {len(result.get('abstract', ''))}")
            else:
                print("❌ 解析失败")

        except Exception as e:
            print(f"❌ 解析出错: {str(e)}")
    else:
        print("用法: python test_simple.py <pdf_path>")


if __name__ == "__main__":
    asyncio.run(simple_test())