#!/usr/bin/env python3
"""简化的全文解析测试"""

import asyncio
import sys
from pathlib import Path

from app.api.repository import grobid_client

sys.path.append(str(Path(__file__).parent.parent.parent))



async def test_fulltext():
    pdf_path = "/Users/<USER>/Documents/file.pdf"

    print("📖 测试GROBID全文解析...")

    try:
        # 全文解析
        result = await grobid_client.parse_pdf_fulltext(pdf_path)

        if result:
            print("✅ 全文解析成功!")

            # 显示基础信息
            print(f"标题: {result.get('title', '未识别')}")

            # 显示章节信息
            sections = result.get('sections', [])
            print(f"章节数量: {len(sections)}")

            if sections:
                print("\n前3个章节:")
                for i, section in enumerate(sections[:3], 1):
                    heading = section.get('heading', '无标题')
                    content = section.get('content', '').strip()
                    content_preview = content[:100] + "..." if len(content) > 100 else content

                    print(f"\n章节 {i}: {heading}")
                    print(f"内容长度: {len(content)} 字符")
                    print(f"内容预览: {content_preview}")

                # 统计总文本量
                total_text = ""
                for section in sections:
                    content = section.get('content', '')
                    if content:
                        total_text += content + " "

                print(f"\n📊 总统计:")
                print(f"总文本长度: {len(total_text)} 字符")
                print(f"总单词数: {len(total_text.split())} 个")

                # 保存完整正文
                if total_text.strip():
                    with open("fulltext_extracted.txt", "w", encoding="utf-8") as f:
                        f.write(f"PDF文件: {pdf_path}\n")
                        f.write(f"标题: {result.get('title', '未识别')}\n")
                        f.write("=" * 50 + "\n\n")

                        for section in sections:
                            heading = section.get('heading', '无标题')
                            content = section.get('content', '').strip()
                            if content:
                                f.write(f"## {heading}\n\n")
                                f.write(content + "\n\n")

                    print("✅ 完整正文已保存到 fulltext_extracted.txt")

            # 参考文献
            references = result.get('references', [])
            if references:
                print(f"\n参考文献数量: {len(references)}")

        else:
            print("❌ 全文解析失败")

    except Exception as e:
        print(f"❌ 解析出错: {str(e)}")


if __name__ == "__main__":
    asyncio.run(test_fulltext())