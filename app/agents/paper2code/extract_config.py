import json
import re
import os
import shutil
from app.agents.paper2code.utils import extract_planning, content_to_json, format_json_data

from app.core.logging import get_logger

logger = get_logger(__name__)

async def run_extract_config(output_dir):
    planning_traj_path = f'{output_dir}/planning_trajectories.json'

    try:
        if not os.path.exists(planning_traj_path):
            raise FileNotFoundError(f'Paper2Code 在 {planning_traj_path} 未找到planning_trajectories.json')

        with open(planning_traj_path, encoding='utf8') as f:
            traj = json.load(f)

        yaml_raw_content = ""
        for turn_idx, turn in enumerate(traj):
            if turn_idx == 8:
                yaml_raw_content = turn['content']

        if "</think>" in yaml_raw_content:
            yaml_raw_content = yaml_raw_content.split("</think>")[-1]

        pattern = r"```yaml\r?\n(.*?)\r?\n```"
        match = re.search(pattern, yaml_raw_content, re.DOTALL)
        if match:
            yaml_content = match.group(1)
            with open(f'{output_dir}/planning_config.yaml', 'w', encoding='utf8') as f:
                f.write(yaml_content)
        else:
            print("No YAML content found.")

        artifact_output_dir = f"{output_dir}/planning_artifacts"
        os.makedirs(artifact_output_dir, exist_ok=True)

        context_lst = extract_planning(planning_traj_path)

        arch_design = content_to_json(context_lst[1])
        logic_design = content_to_json(context_lst[2])

        formatted_arch_design = format_json_data(arch_design)
        formatted_logic_design = format_json_data(logic_design)

        with open(f"{artifact_output_dir}/1.1_overall_plan.txt", "w", encoding="utf-8") as f:
            f.write(context_lst[0])

        with open(f"{artifact_output_dir}/1.2_arch_design.txt", "w", encoding="utf-8") as f:
            f.write(formatted_arch_design)

        with open(f"{artifact_output_dir}/1.3_logic_design.txt", "w", encoding="utf-8") as f:
            f.write(formatted_logic_design)

        shutil.copy(f"{output_dir}/planning_config.yaml", f"{artifact_output_dir}/1.4_config.yaml")

    except Exception as e:
        logger.error(f"Paper2Code Extract Error: {e}")
        raise
