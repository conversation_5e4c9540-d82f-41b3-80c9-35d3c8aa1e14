import json
import os
from tqdm import tqdm

from app.services.llm_service import call_llm
from app.services.system_config_service import system_config_service
from app.agents.paper2code.utils import extract_planning, content_to_json, print_response
from app.agents.paper2code.prompts import ANALYSIS_PROMPT
import copy
from app.core.logging import get_logger

logger = get_logger(__name__)


def get_write_msg(todo_file_name, todo_file_desc, paper_content, context_lst, config_yaml):
    draft_desc = f"Write the logic analysis in '{todo_file_name}', which is intended for '{todo_file_desc}'."
    if len(todo_file_desc.strip()) == 0:
        draft_desc = f"Write the logic analysis in '{todo_file_name}'."

    write_msg = [{'role': 'user', "content": f"""## Paper
{paper_content}

-----

## Overview of the plan
{context_lst[0]}

-----

## Design
{context_lst[1]}

-----

## Task
{context_lst[2]}

-----

## Configuration file
```yaml
{config_yaml}
```
-----

## Instruction
Conduct a Logic Analysis to assist in writing the code, based on the paper, the plan, the design, the task and the previously specified configuration file (config.yaml). 
You DON'T need to provide the actual code yet; focus on a thorough, clear analysis.

{draft_desc}

-----

## Logic Analysis: {todo_file_name}"""}]
    return write_msg

async def run_analyzing(pdf_json_path, output_dir):
    """
    Wrapper function to run the analyzing stage.
    """
    print("Running analyzing stage...")

    try:
        if not os.path.exists(pdf_json_path):
            raise FileNotFoundError(f'Paper2Code 在 {pdf_json_path} 未找到PDF JSON')

        with open(pdf_json_path, encoding='utf-8') as f:
            paper_content = json.load(f)

        with open(f'{output_dir}/planning_config.yaml', encoding='utf-8') as f:
            config_yaml = f.read()

        context_lst = extract_planning(f'{output_dir}/planning_trajectories.json')

        if os.path.exists(f'{output_dir}/task_list.json'):
            with open(f'{output_dir}/task_list.json', encoding='utf-8') as f:
                task_list = json.load(f)
        else:
            task_list = content_to_json(context_lst[2])
        if 'Task list' in task_list:
            todo_file_lst = task_list['Task list']
        elif 'task_list' in task_list:
            todo_file_lst = task_list['task_list']
        elif 'task list' in task_list:
            todo_file_lst = task_list['task list']
        else:
            # raise TODO
            print(f"[ERROR] 'Task list' does not exist. Please re-generate the planning.")

        if 'Logic Analysis' in task_list:
            logic_analysis = task_list['Logic Analysis']
        elif 'logic_analysis' in task_list:
            logic_analysis = task_list['logic_analysis']
        elif 'logic analysis' in task_list:
            logic_analysis = task_list['logic analysis']
        else:
            # raise TODO
            print(f"[ERROR] 'Logic Analysis' does not exist. Please re-generate the planning.")

        done_file_lst = ['config.yaml']
        logic_analysis_dict = {}
        for desc in task_list['Logic Analysis']:
            logic_analysis_dict[desc[0]] = desc[1]

        analysis_msg = [{"role": "system", "content": ANALYSIS_PROMPT}]

        artifact_output_dir = f'{output_dir}/analyzing_artifacts'
        os.makedirs(artifact_output_dir, exist_ok=True)

        for todo_file_name in tqdm(todo_file_lst):
            responses = []
            trajectories = copy.deepcopy(analysis_msg)

            current_stage = f"[ANALYSIS] {todo_file_name}"
            print(current_stage)
            if todo_file_name == "config.yaml":
                continue

            if todo_file_name not in logic_analysis_dict:
                logic_analysis_dict[todo_file_name] = ""

            instruction_msg = get_write_msg(todo_file_name, logic_analysis_dict[todo_file_name], paper_content, context_lst, config_yaml)
            trajectories.extend(instruction_msg)

            api_url = await system_config_service.get_config("AGENT_PAPER2CODE_API_URL")
            api_key = await system_config_service.get_config("AGENT_PAPER2CODE_API_KEY")
            model = await system_config_service.get_config("AGENT_PAPER2CODE_MODEL")

            completion = await call_llm(
                messages=trajectories,
                apiKey=api_key,
                apiUrl=api_url,
                model=model,
                stream=False,
                flag="paper2code"
            )

            if completion is None:
                logger.error(f"LLM调用失败，文件: {todo_file_name}")
                raise Exception(f"LLM调用失败，无法继续分析文件: {todo_file_name}")

            # response
            completion_json = json.loads(completion.model_dump_json())
            responses.append(completion_json)

            # trajectories
            message = completion.choices[0].message
            trajectories.append({'role': message.role, 'content': message.content})

            # print and logging
            print_response(completion_json)

            # save
            with open(f'{artifact_output_dir}/{todo_file_name}_simple_analysis.txt', 'w',  encoding='utf-8') as f:
                f.write(completion_json['choices'][0]['message']['content'])

            done_file_lst.append(todo_file_name)

            # save for next stage(coding)
            todo_file_name = todo_file_name.replace("/", "_")
            with open(f'{output_dir}/{todo_file_name}_simple_analysis_response.json', 'w', encoding='utf-8') as f:
                json.dump(responses, f, ensure_ascii=False, indent=2)

            with open(f'{output_dir}/{todo_file_name}_simple_analysis_trajectories.json', 'w', encoding='utf-8') as f:
                json.dump(trajectories, f, ensure_ascii=False, indent=2)

        logger.info("Analyzing completed successfully.")
    except Exception as e:
        logger.error(f"Error during analyzing: {e}")
        raise