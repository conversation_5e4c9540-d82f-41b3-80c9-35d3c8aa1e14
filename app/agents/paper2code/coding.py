import json
import os
from tqdm import tqdm
import copy

from app.services.llm_service import call_llm
from app.services.system_config_service import system_config_service
from app.agents.paper2code.utils import extract_planning, content_to_json, extract_code_from_content, print_response
from app.agents.paper2code.prompts import CODE_PROMPT, get_write_msg
from app.core.logging import get_logger

logger = get_logger(__name__)

async def run_coding(pdf_json_path, output_dir, output_repo_dir):
    print("Running coding stage...")

    try:
        if not os.path.exists(pdf_json_path):
            raise FileNotFoundError(f'Paper2Code 在 {pdf_json_path} 未找到PDF JSON')

        with open(pdf_json_path, encoding='utf-8') as f:
            paper_content = json.load(f)

        with open(f'{output_dir}/planning_config.yaml', encoding='utf-8') as f:
            config_yaml = f.read()
        context_lst = extract_planning(f'{output_dir}/planning_trajectories.json')
        task_list = content_to_json(context_lst[2])

        todo_file_lst = task_list['Task list']
        done_file_lst = ['config.yaml']
        done_file_dict = {}
        code_msg = [{"role": "system", "content": CODE_PROMPT}]

        detailed_logic_analysis_dict = {}
        for todo_file_name in todo_file_lst:
            save_todo_file_name = todo_file_name.replace("/", "_")

            if todo_file_name == "config.yaml":
                continue

            with open(f"{output_dir}/{save_todo_file_name}_simple_analysis_response.json", encoding='utf-8') as f:
                detailed_logic_analysis_response = json.load(f)
            detailed_logic_analysis_dict[todo_file_name] = detailed_logic_analysis_response[0]['choices'][0]['message'][
                'content']

        artifact_output_dir = f'{output_dir}/coding_artifacts'
        os.makedirs(artifact_output_dir, exist_ok=True)

        for todo_idx, todo_file_name in enumerate(tqdm(todo_file_lst)):
            responses = []
            trajectories = copy.deepcopy(code_msg)

            current_stage = f"[CODING] {todo_file_name}"
            print(current_stage)

            if todo_file_name == "config.yaml":
                continue

            instruction_msg = get_write_msg(todo_file_name, detailed_logic_analysis_dict[todo_file_name], done_file_lst, done_file_dict, paper_content, context_lst, config_yaml)
            trajectories.extend(instruction_msg)

            api_url = await system_config_service.get_config("AGENT_PAPER2CODE_API_URL")
            api_key = await system_config_service.get_config("AGENT_PAPER2CODE_API_KEY")
            model = await system_config_service.get_config("AGENT_PAPER2CODE_MODEL")

            completion = await call_llm(
                messages=trajectories,
                apiKey=api_key,
                apiUrl=api_url,
                model=model,
                stream=False,
                flag="paper2code"
            )

            if completion is None:
                logger.error(f"LLM调用失败，文件: {todo_file_name}")
                raise Exception(f"LLM调用失败，无法继续编码文件: {todo_file_name}")

            completion_json = json.loads(completion.model_dump_json())
            responses.append(completion_json)

            message = completion.choices[0].message
            trajectories.append({'role': message.role, 'content': message.content})

            done_file_lst.append(todo_file_name)

            os.makedirs(f'{output_repo_dir}', exist_ok=True)
            save_todo_file_name = todo_file_name.replace("/", "_")

            # print and logging
            print_response(completion_json)

            with open(f'{artifact_output_dir}/{save_todo_file_name}_coding.txt', 'w', encoding='utf-8') as f:
                f.write(completion_json['choices'][0]['message']['content'])

            code = extract_code_from_content(message.content)
            if len(code) == 0:
                code = message.content

            done_file_dict[todo_file_name] = code
            if save_todo_file_name != todo_file_name:
                todo_file_dir = '/'.join(todo_file_name.split("/")[:-1])
                os.makedirs(f"{output_repo_dir}/{todo_file_dir}", exist_ok=True)

            with open(f"{output_repo_dir}/{todo_file_name}", 'w', encoding='utf-8') as f:
                f.write(code)
    except Exception as e:
        logger.error(f"Error during coding: {e}")
        raise

