import os
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

from pdf_process import process_pdf_json
from planning import run_planning
from extract_config import run_extract_config
from analyzing import run_analyzing
from coding import run_coding

app = FastAPI()


class RunScriptRequest(BaseModel):
    gpt_version: str
    paper_name: str
    pdf_path: str
    pdf_json_path: str
    pdf_json_cleaned_path: str
    output_dir: str
    output_repo_dir: str


@app.post("/run-script")
async def run_script():
    """
    Runs the entire PaperCoder workflow as a single service endpoint.
    """
    # 分析
    output_dir = "./output"
    # 结果
    output_repo_dir = "./output_repo_dir"

    pdf_json_path = "./Transformer.json"

    pdf_json_cleaned_path = "./Transformer_clean.json"

    try:
        # Step 1: Create directories
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(output_repo_dir, exist_ok=True)

        print("Starting workflow...")

        # Step 2: Preprocess PDF
        print("--- Preprocess ---")
        await process_pdf_json(
            input_json_path=pdf_json_path,
            output_json_path=pdf_json_cleaned_path
        )

        # Step 3: Planning
        print("--- Planning ---")
        await run_planning(
            pdf_json_path=pdf_json_cleaned_path,
            output_dir=output_dir
        )

        # Step 4: Extract Config
        print("--- Extracting Config ---")
        await run_extract_config(
            output_dir=output_dir,
        )

        # Step 5: Analyzing
        print("--- Analyzing ---")
        await run_analyzing(
            pdf_json_path=pdf_json_cleaned_path,
            output_dir=output_dir
        )

        # Step 6: Coding
        print("--- Coding ---")
        await run_coding(
            pdf_json_path=pdf_json_cleaned_path,
            output_dir=output_dir,
            output_repo_dir=output_repo_dir
        )

        return {"status": "success", "message": "Workflow completed successfully."}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
