import json
import os

from app.services.llm_service import call_llm_full_response
from app.services.system_config_service import system_config_service
from app.agents.paper2code.utils import print_response
from app.agents.paper2code.prompts import SYSTEM_PLAN_PROMPT, USER_PLAN_PROMPT, FILE_LIST_PROMPT, TASK_LIST_PROMPT, CONFIG_PROMPT
from app.core.logging import get_logger

logger = get_logger(__name__)

async def run_planning(pdf_json_path, output_dir):
    logger.info(f"Paper2Code running planning stage...")

    try:
        if not os.path.exists(pdf_json_path):
            raise FileNotFoundError(f'Paper2Code 在 {pdf_json_path} 未找到PDF JSON')

        with open(pdf_json_path, encoding='utf-8') as f:
            paper_content = json.load(f)

        plan_msg = [{"role": "system", "content": SYSTEM_PLAN_PROMPT}, {"role": "user", "content": USER_PLAN_PROMPT.format(paper_content=paper_content)}]

        file_list_msg = [{"role": "user", "content": FILE_LIST_PROMPT}]

        task_list_msg = [{"role": "user", "content": TASK_LIST_PROMPT}]

        config_msg = [{"role": "user", "content": CONFIG_PROMPT}]

        responses = []
        trajectories = []

        for idx, instruction_msg in enumerate([plan_msg, file_list_msg, task_list_msg, config_msg]):
            current_stage = ""
            if idx == 0:
                current_stage = "[Planning] Overall plan"
            elif idx == 1:
                current_stage = "[Planning] Architecture design"
            elif idx == 2:
                current_stage = "[Planning] Logic design"
            elif idx == 3:
                current_stage = "[Planning] Configuration file generation"
            print(f"Current stage: {current_stage}")

            trajectories.extend(instruction_msg)

            api_url = await system_config_service.get_config("AGENT_PAPER2CODE_API_URL")
            api_key = await system_config_service.get_config("AGENT_PAPER2CODE_API_KEY")
            model = await system_config_service.get_config("AGENT_PAPER2CODE_MODEL")

            completion = await call_llm_full_response(
                messages=trajectories,
                apiKey=api_key,
                apiUrl=api_url,
                model=model,
                stream=False,
                flag="paper2code"
            )

            if completion is None:
                logger.error(f"LLM调用失败，阶段: {current_stage}")
                raise Exception(f"LLM调用失败，无法继续执行规划阶段: {current_stage}")

            print_response(completion)

            responses.append(completion)

            # 从原始响应中提取消息信息
            if 'choices' in completion and len(completion['choices']) > 0:
                message_data = completion['choices'][0]['message']
                trajectories.append({'role': message_data['role'], 'content': message_data['content']})

        os.makedirs(output_dir, exist_ok=True)

        with open(os.path.join(output_dir, 'planning_response.json'), 'w', encoding='utf-8') as f:
            json.dump(responses, f, ensure_ascii=False, indent=2)

        with open(os.path.join(output_dir, 'planning_trajectories.json'), 'w', encoding='utf-8') as f:
            json.dump(trajectories, f, ensure_ascii=False, indent=2)
            
        logger.info(f"Paper2Code planning completed successfully.")
    except Exception as e:
        logger.error(f"Paper2Code planning failed. {e}")
        raise
