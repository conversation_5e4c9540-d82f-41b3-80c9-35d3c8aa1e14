from typing import (
    Generic,
    TypeVar,
    Optional,
    AsyncGenerator,
    List,
    Callable,
    Dict
)
from app.api.schemas.literatures import LiteratureResponse
# 從系統配置中動態獲取搜索引擎配置
from app.services.system_config_service import system_config_service    
import time
import tempfile
# 从独立模块导入上下文变量避免循环依赖
from app.core.context import request_id_var, request_start_var
import pypandoc
from docx import Document
import docx
from docx.shared import RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
# 给请求增加唯一的requestID
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from fastapi.responses import FileResponse
from starlette.background import BackgroundTask
from uuid import uuid4
from pydantic.generics import GenericModel
import os
import json
# import asyncio
from pydantic import BaseModel
from fastapi import Query
from app.api.schemas.literatures import LiteratureResponse
from app.services.search_service import (
    fetch_webpage_text_async,
    perform_serpapi_search
)
from enum import Enum
from app.utils.enum import ErrorType
from app.services.llm_service import call_llm
from app.core.config import settings
from app.services.prompts import (
    REFERENCE_VALID_SYSTEM_PROMPT,
    REFERENCE_VALID_USER_PROMPT,
    REFERENCE_RELATED_SYSTEM_PROMPT,
    REFERENCE_RELATED_USER_PROMPT
)
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from uuid import uuid4
import re
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
from app.core.config import settings

logger = get_logger(__name__)

# 正文所有标题的纯文字最大匹配长度
PURE_TITLE_MAX_LENGTH=200

class SingleLiterature(BaseModel):
    reference: str
    url: str
class LiteratureTextAndOtherTitle(BaseModel):
    literature_text: str
    other_text: str
class ContentStatus(str, Enum):
    """内容状态"""
    ERROR = "error"  # 错误状态
    NORMAL = "normal"  # 正常文本
    HEART_BEAT = "heart_beat"
class CiteItem(BaseModel):
    origin: str
    nums: List[int]
    text: str
    remark: Optional[List[str]] = None
class SplitReference(BaseModel):
    text_before_citation_title: str
    citation_paragraph_without_title: str
    citation_title: str
    paragraph_after_citation: str
class Reference(BaseModel):
  index: int
  authors: str
  title: str
  paper_type: Optional[str]
  journal: str|None
  year: str
  volume: str|None
  issue: str|None
  pages: str|None
  full_text: str
  url: str
  remark: Optional[List[str]] = None

T = TypeVar("T")
# 正文中引用的正则匹配
CITATION_NUM_RE = r'(.+?)(?<!>)(\[(\d+(?:-\d+)?(?:,\s*\d+(?:-\d+)?)*)\])'
# 一条参考文献的正则匹配
REFERENCE_RE = r'(?=\[\d+\]).+?\[(M|J|D|C|N|R|S|P|EB/OL|OL)\].+?[\d{3,4}].*?'

class FAULT(str, Enum):
    FAKE_REFERENCE = "参考文献可能不存在"
    NOT_REFERENCED = "未在正文引用"
    OPINION_UNFIT="观点表述可能有误"
    TYPE_ERROR="参考文献引用格式不标准"
    NOT_IN_REFERENCE="参考文献缺失"
def fault_normal(index: int, data: FAULT):
    return f"&#91;{index}&#93;{data.value}"
class ResponseModel(GenericModel, Generic[T]):
  success: bool
  code: int
  data: Optional[T] = None
  error: Optional[str] = ""# from fastapi.responses import JSONResponse

class PageInfo(GenericModel, Generic[T]):
    """分页信息"""
    total: int
    page: int
    size: int
    items: List[T] = []

class ResponsePageModel(GenericModel, Generic[T]):
    """分页响应模型"""
    success: bool
    code: int
    data: PageInfo[T]
    error: Optional[str] = ""
class PageQuery(BaseModel):
  page: Optional[int] = Query(1, ge=1, description="页码")
  size: Optional[int] = Query(10, ge=1, description="每页大小")

## 参考文献的标题识别函数
def citation_head_match(text: str, no_prefix: bool = False):
    # 匹配格式：### 1.参考文献 或 ## 1、参考文献
    pattern = re.compile(
        # r'(\s*(?:\d+|[一二三四五六七八九十])?.*?[、\.]\**?参考文献\**?)' if no_prefix else r'(#*\s*(?:\d+\.)?.*?[、\.]\**?参考文献\**?)',
        r'(\s{0,4}(?:\d+|[一二三四五六七八九十])?\s{0,2}[、\.]\s{0,2}\*{0,2}参考文献\*{0,2})' if no_prefix else r'(#*\s{0,4}(?:\d+|[一二三四五六七八九十])?\s{0,2}[、\.]\s{0,2}\*{0,2}参考文献\*{0,2})',
        re.IGNORECASE
    )
    match = pattern.search(text)
    if not match:
        # 匹配格式：## 参考文献
        pattern1 = re.compile(
            r'(\s{0,2}\*{0,2}参考文献\*{0,2})' if no_prefix else r'(#+\s{0,2}\*{0,2}参考文献\*{0,2})',
            re.IGNORECASE
        )
        match = pattern1.search(text)
    if match:
        logger.info(f"参考文献的标题为：{match.group(1)}")
    else:
        logger.info(f"没有参考文献的标题")
    return match
## markdown格式的标题识别函数
def title_head_match(text: str):
    # 匹配格式：### 1.标题 或 ## 1、标题
    pattern = re.compile(
        rf'(#{{1,5}}\s{{0,2}}(?:\d+)?+\s{{0,2}}[、\.]\**.{{1,{PURE_TITLE_MAX_LENGTH}}}\**\s*[^\n\r]{1,3})',
        re.IGNORECASE
    )
    match = pattern.search(text)

    if not match:
        # 匹配格式：## 标题
        pattern1 = re.compile(
            rf'(#{{1,5}}\s{{0,2}}\**.{{1,{PURE_TITLE_MAX_LENGTH}}}\**[^\n\r]{{1,3}})',
            re.IGNORECASE
        )
        match = pattern1.search(text)
    return match
def send_data(is_success: bool, data: Optional[T], error: str = "") -> ResponseModel[T]: 
    result = ResponseModel(
        success=is_success,
        code=0 if is_success else 500,
        data=data if is_success else None,
        error=(data or error) if not is_success else ""
    )
    logger.info(f"响应数据：{str(dict(result))[:200]}")
    return result

async def reference_related(
    content: str,
    expression: str,
    api_key: str,
    api_url: str,
    model: str
) -> bool:
    messages = [
        {
            "role": "system",
            "content": REFERENCE_RELATED_SYSTEM_PROMPT
        },
        {
            "role": "user",
            "content": REFERENCE_RELATED_USER_PROMPT.format(
                content=content,
                expression=expression
            )
        }
    ]
    response = await call_llm(
        messages=messages,
        flag="网页的提取内容和引用文本进行比对",
        apiKey=api_key,
        apiUrl=api_url,
        model=model
    )
    return response == 'Yes'

async def reference_valid(
    data: Reference,
    api_key: str,
    api_url: str,
    model: str,
    limit: int = settings.MAX_HALLUCINATION_DETECTION_SEARCH,
    complete_callback: Optional[Callable[[Dict[str, str]], None]] = None
) -> str:
    response = "No"
    
    # 檢查 JINA_DIRECT_CONTENT 配置
    jina_direct_content_config = await system_config_service.get_config("JINA_DIRECT_CONTENT")
    jina_direct_content_enabled = jina_direct_content_config and jina_direct_content_config.upper() == "ON"
    
    search_engine_config = await system_config_service.get_config("SEARCH_ENGINE")
    if not search_engine_config:
        search_engine_config = settings.WEB_SEARCH_ENGINE
    
    search_results = await perform_serpapi_search(
        query=data.full_text,
        limit=limit,
        search_engine=search_engine_config
    )
    search_results = search_results[:limit]
    
    if jina_direct_content_enabled and search_engine_config == "jina":
        # Jina 直接內容模式：直接使用搜索結果作為內容
        for content in search_results:
            try:
                if content:
                    # 调用LLM
                    messages = [
                        {
                            "role": "system",
                            "content": REFERENCE_VALID_SYSTEM_PROMPT
                        },
                        {
                            "role": "user",
                            "content": REFERENCE_VALID_USER_PROMPT.format(
                                text=content,
                                literature=data.full_text
                            )
                        }
                    ]
                    response = await call_llm(
                        messages=messages,
                        flag="提取网页内容和参考文献进行比对",
                        apiKey=api_key,
                        apiUrl=api_url,
                        model=model,
                        complete_callback=complete_callback
                    )
                    if response and response != 'No':
                        break
            except Exception as e:
                logger.error(f"内容比对报错: {str(e)}")
    else:
        # URL 模式：需要抓取網頁內容
        for url in search_results:
            try:
                page_content = await fetch_webpage_text_async(url)
                if not page_content:
                    return response
                else:
                    # 调用LLM
                    messages = [
                        {
                            "role": "system",
                            "content": REFERENCE_VALID_SYSTEM_PROMPT
                        },
                        {
                            "role": "user",
                            "content": REFERENCE_VALID_USER_PROMPT.format(
                                text=page_content,
                                literature=data.full_text
                            )
                        }
                    ]
                    response = await call_llm(
                        messages=messages,
                        flag="提取网页内容和参考文献进行比对",
                        apiKey=api_key,
                        apiUrl=api_url,
                        model=model,
                        complete_callback=complete_callback
                    )
                    if response and response != 'No':
                        break
            except Exception as e:
                logger.error(f"网页：{url}内容比对报错")
    
    return response

def send_page_data(
    is_success: bool,
    data: Optional[PageInfo[T]] = None,
    error: str = ""
) -> ResponsePageModel[T]:
    """
    构建分页响应数据
        
    Returns:
        ResponsePageModel: 分页响应数据
    """
    # 如果失败且没有提供data，创建一个空的PageInfo对象
    if not is_success and data is None:
        data = PageInfo(items=[], total=0, page=1, size=10)
    
    result = ResponsePageModel(
        success=is_success,
        code=0 if is_success else 500,
        data=data,
        error=error if not is_success else ""
    )
    logger.info(f"响应数据：{str(dict(result))[:200]}")
    return result

def save_text_to_file(
    content: str,
    file_path: str,
    mode: Optional[str] = "w" 
) -> str:
  """
  将字符串内容保存或追加到指定文件中
  
  Args:
      content: 要保存的文本内容
      file_path: 文件的相对路径
      
  Returns:
      保存的文件相对路径
  """
  # 获取绝对路径
  abs_path = os.path.join(os.getcwd(), file_path)
  
  # 确保目录存在
  directory = os.path.dirname(abs_path)
  if directory and not os.path.exists(directory):
    os.makedirs(directory)
    
  # 追加内容到文件（如果文件不存在则创建）
  with open(abs_path, mode, encoding="utf-8") as f:
    f.write(content)
    
  # 返回相对路径
  return file_path
# 是不是 【### 文字标题】这种形式
def is_markdown_title(text: str) -> bool:
    pattern = r'^#+\s[^\n]+$'
    return re.match(pattern, text.strip()) is not None
# 将正文的引用角标改成小的 text形如[1]
# flag为caret 或 sup
def convert_citations_to_sup(text: str, flag:Optional[str] = "sup"):
    # 前端的文本编辑器不支持^所以替换为 <sup>[1]</sup> 形式
    if flag == 'sup':
        return re.sub(CITATION_NUM_RE, r'\1<sup>\2</sup>', text) 
    else:
        return re.sub(CITATION_NUM_RE, r'\1^\2^', text)
# 正文里面可能有^，所以为了正确的展示需要进行转译
def escape_caret(text: str) -> str:
    """
    转义文本中的未转义 ^。
    - ^ -> \^
    - 已经是 \^ 的保持不变
    """
    # (?<!\\)\^ 匹配前面不是 \ 的 ^
    return re.sub(r'(?<!\\)\^', r'\^', text)
# 辅助函数：流式读取文件内容（SSE格式）
async def stream_file_content_sse(
    file_path: str,
    current_user: UserResponse,
    is_cut: Optional[bool] = False
) -> AsyncGenerator[str, None]:
    """
    流式读取文件内容并以SSE格式返回
    
    Args:
        file_path: 文件的相对路径或绝对路径
        
    Yields:
        SSE格式的文件内容
    """
    # 处理文件路径
    if os.path.isabs(file_path):
        abs_file_path = file_path
    else:
        abs_file_path = os.path.join(os.getcwd(), file_path)
    # 检查文件是否存在
    if not os.path.exists(abs_file_path):
        yield f"data: {json.dumps({'error': f'{ErrorType.FILE_NOT_EXIST.value}: {file_path}'})}\n\n"
        return
    try:
        with open(abs_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            content = ""
            for line in lines:
                if len(content) >= settings.TRIAL_USER_MAX_TEXT and current_user.is_trial and is_cut:
                    break
                else:
                    content += line
            content = content.strip()
            chunk = stream_handle_before_send(content)
            if chunk:
                yield f"data: {json.dumps({'content': chunk, 'status': ContentStatus.NORMAL})}\n\n"

        # 发送完成事件
        yield f"data: {json.dumps({'status': 'completed'})}\n\n"
    
    except Exception as e:
        yield f"data: {json.dumps({'error': f'读取文件时出错: {str(e)}'})}\n\n"
# 将参考文献的形如 ****.http://和****.DOI:的格式改成markdown的链接形式
def format_to_markdown_link(text: str) -> str:
    try:
        result = split_by_citation_section(text)
        if not result:
            return text
        literature_section = result.citation_paragraph_without_title
        # 现将参考文献一条一条拆出来
        segments = re.split(r'\n\s*(?=\[\d+\]|\d+\.)', literature_section.strip())
        format_list: List[str] = []
        for i, item in enumerate(segments):
            pure_text = item.strip()
            doi_match = re.search(r'^(.*?)DOI:\s*([^\s]+)', pure_text)
            url_match = re.search(r'(^\[\d+\]\s.+?)url:\s{0,2}(http[s]?://[^\s]+)$', pure_text)
            if doi_match:
                label = doi_match.group(1).strip()
                doi_part = doi_match.group(2).strip()
                url=f"https://doi.org/{doi_part}"
                full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', label)
                format_list.append(f"[{i + 1}] [{full_text}]({url})")
            elif url_match:
                label = url_match.group(1).strip()
                url = url_match.group(2).strip()
                full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', label)
                format_list.append(f"[{i + 1}] [{full_text}]({url})")
            else:
                full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', pure_text)
                format_list.append(f"[{i + 1}] {full_text}")
        # 这里使用两个换行符的目的是为了前端的md编辑器可以把每一条文献识别为一个段落。
        reference = ("\n\n").join(format_list)
        res = [
            result.text_before_citation_title,
            result.citation_title,
            reference
        ]
        if result.paragraph_after_citation:
            res.append(result.paragraph_after_citation)
        # 这里使用两个换行符的目的是为了每一部份都被识别为一个段落。
        return ("\n\n").join(res)  
    except Exception as e:
        logger.error(f"整理参考文献成markdown链接格式时发生错误：{str(e)}")
        return text
      
# 将markdown的链接形式的文档进行提取和以DOI:结尾的文献进行提取
def normal_single_literature(text: str) -> SingleLiterature:
    pattern = r'(\[\d+\])\s*\[(.*?)\]\((.*?)(?:\s+".*?")?\)'
    match = re.search(pattern, text)
    doi_match = re.search(r'^(.*?)DOI:\s*([^\s]+)', text)
    label = text
    url = ""
    if match:
        num = match.group(1).strip()
        reference = match.group(2).strip()
        label = f"{num} {reference}"
        url = match.group(3).strip()
    elif doi_match:
        label = doi_match.group(1).strip()
        doi_part = doi_match.group(2).strip()
        url=f"https://doi.org/{doi_part}"
    return SingleLiterature(
        reference=label,
        url=url
    )
def extract_reference_item_list(text: str):
    segments = re.split(r'\n\s*(?=\[\d+\]|\d+\.)', text.strip())
    result_list: List[Reference] = []
    for i, item in enumerate(segments):
        temp = normal_single_literature(item)
        seg = temp.reference
        pattern = re.compile(r"""
            ^\s*(?:\[(?P<index_bracket>\d+)\]|\s*(?P<index>\d+)\.)\s+   # [1] 或 1.
            (?P<authors>.+?)\.\s+                                 # 作者部分（第一个完整句号）
            (?P<title>.+?)
            [\\]{0,1}\[(?P<paper_type>M|J|D|C|N|R|S|P|EB|OL|EB/OL)[\\]{0,1}\]\.\s+                                      # 标题部分（第一个完整句号）
            (?:(?P<journal>.+?)[\.,]\s+)?                                    # 期刊名（第一个完整句号）
            (?P<year>\d{4})[,]{0,1}\s*                                            # 年份
            [;]{0,1}\s*(?:(?P<volume>\d+))?  #可选卷号                                    # 卷号
            # (?:\((?P<issue>S{0,1}.{0,5}\s{0,5}.{0,5}\d{0,8})\))?   # 可选期号
            (?:\((?P<issue>[^\)]+)\))?
            :{0,1}\s*
            (?:(?P<pages>[A-Za-z\d\-–—\.e]+))?  #可选页码 
            \.\s*
            $                                          # 结尾句号
        """, re.VERBOSE)
        # seg不能用full_text替换，因为full_text没有前面的序号了
        match = pattern.match(seg)
        full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', seg)
        # 去掉前后的序号、和前后的换行符
        if match:
            groups = match.groupdict()
            index = groups.get("index") or groups.get("index_bracket")
            result = Reference(**{
                "index": int(index),
                "authors": groups["authors"],
                "title": groups["title"],
                "journal": groups["journal"],
                "year": groups["year"],
                "volume": groups["volume"] if groups["volume"] else None,
                "issue": groups["issue"] if groups["issue"] else None,
                "pages": groups["pages"],
                "full_text": full_text,
                "url": temp.url,
                "paper_type": groups.get("paper_type")
            })
            result_list.append(result)
        else:
            logger.info(seg)
            result = Reference(**{
                "index": i + 1,
                "authors": "",
                "title": "",
                "journal": "",
                "year": "",
                "paper_type": None,
                "volume": None,
                "issue": None,
                "pages": "",
                "full_text": full_text,
                "url": temp.url,
                "remark": [FAULT.TYPE_ERROR.value]
            })
            result_list.append(result)
    return result_list


def demote_headings(text: str):
    # 判断是否存在一级标题
    has_h1 = any(re.match(r'^#\s', line) for line in text.split('\n'))
    
    if not has_h1:
        return text  # 如果没有一级标题，直接返回原文
    
    # 将所有标题降一级（增加一个#）
    lines = []
    for line in text.split('\n'):
        if re.match(r'^#+\s', line):
            line = '#' + line  # 增加一个#
        lines.append(line)
    
    return '\n\n'.join(lines)
# 去除正文的标题标题
def remove_markdown_h1_and_text(
    all_text: str,
    title: str,
    eng_title: Optional[str] = None,
    combine_title: Optional[bool] = True
) -> str:
    """
    在文本开头指定长度内，移除包含title的整个段落
    
    Args:
        all_text: 原始文本
        title: 要查找和移除的标题文本
        combine_title: 是否在结果前拼接标题
        
    Returns:
        处理后的文本
    """
    if not title:
        return all_text
    
    # 截取开头指定长度的文本进行处理
    split_index = len(title) * 2 + 20
    front_text = all_text[:split_index]
    logger.info(f"截取的内容为：{front_text}")
    remaining_text = all_text[split_index:]
    
    # 按段落分割（支持多种换行符组合）
    paragraphs = re.split(r'\n+', front_text)
    
    # 过滤掉包含title的段落
    filtered_paragraphs = []
    
    for paragraph in paragraphs:
        if title in paragraph:
            continue
        filtered_paragraphs.append(paragraph)
    
    # 重新组合过滤后的段落
    filtered_front_text = '\n\n'.join(filtered_paragraphs)
    # 拼接处理后的前半部分和原始的后半部分
    # 因为有的时候返回的内容段落标题会是一级标题。所以改一下
    without_title = demote_headings(filtered_front_text + remaining_text)
    

    def get_first_line(text: str) -> str:
        """返回文本中的第一行"""
        return text.strip().splitlines()[0] if text.strip() else ''
    if eng_title:    
        eng_title = get_first_line(eng_title)
    if combine_title:
        return f"# {title}\n\n# {eng_title}\n\n{without_title}" if eng_title else f"# {title}\n\n{without_title}"
    else:
        return without_title

def read_file_content(file_path: str) -> str:
    """
    读取文件内容并返回
    
    Args:
        file_path: 文件的相对路径或者绝对路径
        
    Returns:
        str: 文件的内容
        
    Raises:
        FileNotFoundError: 当文件不存在时
        IOError: 当读取文件出错时
    """
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    # 检查文件是否存在
    if not os.path.exists(abs_path):
      error_msg = f"{ErrorType.FILE_NOT_EXIST.value}: {file_path}"
      logger.error(error_msg)
      raise Exception(error_msg)
    
    try:
        # 读取文件内容
        with open(abs_path, "r", encoding="utf-8") as f:
            content = f.read()
        result = content.strip()
        return result
    except IOError as e:
        raise Exception(f"读取文件时出错: {str(e)}")
# 将markdown文档按照标题加正文或者标题的的组合切分
def split_markdown_text_by_paragraph(md_text: str) -> List[str]:
    pattern = r'^(#+\s+.*)$'
    lines = md_text.splitlines()
    sections = []
    current_section = []

    for line in lines:
        if re.match(pattern, line):
            # 遇到新标题时，保存当前段落
            if current_section:
                sections.append('\n\n'.join(current_section).strip())
            current_section = [line]  # 新段落从标题开始
        else:
            current_section.append(line)

    # 保存最后一个段落
    if current_section:
        sections.append('\n\n'.join(current_section).strip())

    return sections

def extract_section_after_citation(literature_text: str):
    try:
        match = title_head_match(literature_text)
        if match:
            split_text = match.group(1).strip()
            start, end = match.span()

            pure_literature = literature_text[:start].strip()
            other_text = f"{split_text}\n{literature_text[end:].strip()}"
            return LiteratureTextAndOtherTitle(
                literature_text=pure_literature,
                other_text=other_text
            )
        else:
            return LiteratureTextAndOtherTitle(
                literature_text=literature_text,
                other_text=''
            )
    except Exception as e:
        msg = f"从参考文献后续文本提取段落失败：str{e}"
        logger.error(msg)
        raise ValueError(msg)

def split_by_citation_section(text: str) -> SplitReference|None:
    """
    根据参考文献的标题将文本分割，并保留分隔符。
    返回一个对象。
    """
    logger.info("准备执行split_by_citation_section函数")
    match = citation_head_match(text)
    logger.info("citation_head_match函数执行完毕了")
    if not match:
        return None

    split_text = match.group(1).strip()
    start, end = match.span()
    right = text[end:].strip()
    logger.info("准备执行extract_section_after_citation函数")
    extract_data = extract_section_after_citation(right)
    logger.info("extract_section_after_citation函数执行完毕")
    logger.info(f"参考文献的标题是: {split_text}")
    return SplitReference(**{
        # 参考文献标题前面的文本
        'text_before_citation_title': text[:start].strip(),
        # 参考文献内容段落
        'citation_paragraph_without_title': extract_data.literature_text,
        # 参考文献那几个标题文字
        'citation_title': split_text,
        # 参考文献段落后面的段落
        'paragraph_after_citation': extract_data.other_text
    })
# 将数学公式转换为占位符，因为数学公式可能包含分段的分隔符，导致数学公式被分为两个段落里面。
def extract_math_blocks(text: str):
    # 支持匹配 $$...$$ 或 $$$$...$$$$，非贪婪，保留包裹的符号
    pattern = re.compile(r'(\${1,2})(.+?)\1', re.DOTALL)

    formulas = {}
    index = 0

    def replacer(match):
        nonlocal index
        full_expr = match.group(0)  # 包含 $$ 或 $$$$
        logger.info(f"匹配到的公式{full_expr}")
        key = f"MATH_{index}"
        formulas[key] = full_expr
        placeholder = f"{{{key}}}"
        index += 1
        return placeholder

    replaced_text = pattern.sub(replacer, text)

    logger.info(f"修改后的文章: {replaced_text}")
    return replaced_text, formulas

# text是一段文本，data是包含text里面占位key的字典数据，当然可能也包含不存在text中的key
def safe_replace(text: str, data: dict[str, str]):
    def replacer(match):
        key = match.group(1)
        return str(data.get(key, match.group(0)))
    
    return re.sub(r'\{(\w+)\}', replacer, text)

def split_text_by_sentences(text: str, regexp=r'(。|\n|\?|\||!|？|！)'):
    replaced_text, formulas = extract_math_blocks(text)

    parts = re.split(regexp, replaced_text)
    sentences = []

    # 避免最后落单的部分
    i = 0
    while i < len(parts) - 1:
        left = parts[i].strip()
        right = parts[i + 1].strip()
        if left or right:
            split_text = left + right
            split_text = safe_replace(split_text, formulas)
            sentences.append(split_text)
        i += 2

    # 如果还有剩余的无标点部分（可能是最后落单）
    if i < len(parts):
        last_part = parts[i].strip()
        if last_part:
            split_text = last_part
            split_text = safe_replace(split_text, formulas)
            sentences.append(last_part)
    
    return sentences
def extract_citations_in_paper_by_sentence(text: str) -> List[CiteItem]:
    sentences = split_text_by_sentences(text)
    full_sentences: List[CiteItem] = []
    for sentence in sentences:
        cite_pattern = re.compile(r'(?P<origin>.+?)(?P<cite>\[(\d+(?:-\d+)?(?:,\s*\d+(?:-\d+)?)*)\])')
        match = cite_pattern.match(sentence)
        if match:
            dict_data = match.groupdict()
            num: List[int] = []
            cite = re.sub(r'^\[(.*)\]$', r'\1', dict_data["cite"])
            if ',' in cite:
                num = [int(item) for item in re.split(r',\s*', cite)]
            elif '-' in cite:
                start, end = map(int, cite.split('-'))
                num.extend(range(start, end + 1))
            else:
                num.append(int(cite))
            text = dict_data["origin"]
            sentence = CiteItem(**{
                "origin": sentence,
                "nums": num,
                "text": text
            })
            full_sentences.append(sentence)
    return full_sentences
def replace_markdown_headings_simple(
    markdown_text: str,
    tree_data: List[dict[str, any]]
):
    """
    简单版本：直接字符串替换（适用于不需要考虑复杂情况的场景）
    
    Args:
        markdown_text (str): 原始markdown文本
        tree_data (list): 树状数据结构
        
    Returns:
        str: 替换后的markdown文本
    """
    
    def collect_and_replace(nodes, text):
        """递归收集标题并替换"""
        result_text = text
        for node in nodes:
            markdown_heading = '#' * node['origin_level'] + ' ' + node['title']
            new_markdown_heading = '#' * node['level'] + ' ' + node['title']
            result_text = result_text.replace(markdown_heading, new_markdown_heading)
            # 递归处理子节点
            result_text = collect_and_replace(node['children'], result_text)
        return result_text
    
    return collect_and_replace(tree_data, markdown_text)
def adjust_tree_levels(
    tree_data: List[dict[str, any]],
    top_level: int
):
    """
    调整树结构中节点的层级
    
    如果父节点的level不小于top_level，则该父节点及其所有子节点的level都减1
    
    Args:
        tree_data (list): 树状数据结构
        top_level (int): 基准层级值
        
    Returns:
        list: 调整后的树状数据结构
    """
    def adjust_node(node, std_level):
        """递归调整单个节点及其子节点"""
        # 如果当前节点的level小于等于给定的level。那么进行标题降级
        if node['level'] <= std_level:
            node['level'] += std_level - node['level']
        
        # 递归处理所有子节点
        for child in node['children']:
            adjust_node(child, node["level"] + 1)
    
    # 遍历树的根节点
    for root_node in tree_data:
        adjust_node(root_node, top_level)
    
    return tree_data
def parse_markdown_to_tree(markdown_text: str):
    """
    将Markdown文本解析为标题树结构
    
    Args:
        markdown_text (str): Markdown文本
        
    Returns:
        list: 标题树结构，每个节点包含title、level、children、origin_level四个属性
    """
    lines = markdown_text.split('\n')
    headings = []
    
    # 提取所有标题
    for line in lines:
        line = line.strip()
        match = re.match(r'^(#{1,6})\s+(.+)$', line)
        if match:
            level = len(match.group(1))
            title = match.group(2).strip()
            headings.append({
                'title': title,
                'level': level,
                'children': [],
                'origin_level': level
            })
    
    # 构建树结构
    def build_tree(headings):
        if not headings:
            return []
        
        result = []
        stack = []
        
        for heading in headings:
            # 找到正确的父节点
            while stack and stack[-1]['level'] >= heading['level']:
                stack.pop()
            
            # 创建新节点
            node = {
                'title': heading['title'],
                'level': heading['level'],
                'children': [],
                'origin_level': heading['level']
            }
            
            # 添加到适当的位置
            if not stack:
                result.append(node)
            else:
                stack[-1]['children'].append(node)
            
            stack.append(node)
        
        return result
    
    return build_tree(headings)
def adjust_markdown_heading_levels(
    text: str,
    level: int
) -> str:
    """
    将Markdown文本中的所有标题调整为指定的等级。

    Args:
        text (str): 原始Markdown文本
        level (int): 指定的标题等级（1-6）

    Returns:
        str: 标题等级统一后的Markdown文本
    """
    if not 1 <= level <= 6:
        return text

    tree_data = adjust_tree_levels(parse_markdown_to_tree(text), level)
    return replace_markdown_headings_simple(text, tree_data)

def is_ordered_list_item(line: str) -> bool:
    """
    检测是否为markdown有序列表项
    
    Args:
        line: 要检测的行
        
    Returns:
        bool: 如果是有序列表项返回True，否则返回False
    """
    stripped_line = line.lstrip()
    if not stripped_line:
        return False
    
    # 匹配有序列表格式：数字 + 点号或右括号 + 空格
    # 例如：1. 项目、2) 项目、10. 项目等
    pattern = r'^\d+[.)](\s|$)'
    return bool(re.match(pattern, stripped_line))

def change_markdown_paragraph_join_way(text: str):
    lines = text.splitlines()
    logger.info(lines)
    result = []
    skip_join = {'#', '-', '*', '>', '|', '`'}
    for i in range(len(lines)):
        line = lines[i]
        result.append(line)
        # 尝试插入额外空行的条件：
        # 这是加粗的文本。不是单个*
        if line.strip().startswith("**"):
            result.append("")
        elif (is_ordered_list_item(line) and i !=(len(lines) -1) and is_ordered_list_item(lines[i+1])):
            continue
        elif (
            i < len(lines) - 1
            and line.strip()
            and lines[i + 1].strip()
            and line.lstrip()[0] in skip_join
        ):
            continue
        else:
            result.append('')  # 插入空行
    return '\n'.join(result)

def insert_before_last_char(s: str, insert_text: str) -> str:
    if len(s) < 1:
        return ''
    return s[:-1] + insert_text + s[-1]
class RequestIDMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 生成 request_id (支持Spring Boot风格的ID生成)
        request_id = request.headers.get("X-Request-ID")
        if not request_id:
            # 类似Spring Boot的算法：时间戳 + 随机数
            timestamp = int((time.time() * 1000 - 1489111610226))
            random_part = int(time.time() * 1000000) % 1024
            request_id = f"{timestamp:x}{random_part:03x}"
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 设置上下文变量（关键！用于日志追踪）
        request_id_var.set(request_id)
        request_start_var.set(start_time)
        
        # 保持现有兼容性
        request.state.request_id = request_id
        
        try:
            # 调用后续逻辑
            response = await call_next(request)
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            return response
        finally:
            # 清理上下文变量
            request_id_var.set('')
            request_start_var.set(0.0)

def handle_before_save(text: str):
    pipeline = [bold_to_h2_reference, format_to_markdown_link, change_markdown_paragraph_join_way, remove_contain_outline_headings]
    for fn in pipeline:
        text = fn(text)
    return text
def remove_markdown_code_fence(text: str) -> str:
    """
    去除 Markdown 中语言标记为 markdown 的代码块语法（```markdown ... ```），保留代码内容。
    
    Args:
        text (str): 原始 Markdown 文本
        
    Returns:
        str: 去除 markdown 代码块语法后的文本
    """
    # 匹配形如 ```markdown\n...内容...\n``` 的代码块，只去掉包裹的三引号部分
    pattern = re.compile(r'^```markdown\s*\n([\s\S]*?)^```\s*$', re.MULTILINE)
    return pattern.sub(r'\1', text)

def bold_to_h2_reference(md_text: str) -> str:
    """
    将 Markdown 中的加粗 '参考文献' 转换为二级标题 '## 参考文献'
    """
    # 匹配 **参考文献** 或 __参考文献__ 两种加粗写法
    logger.info("bold_to_h2_reference")
    pattern = r"^[ \t]*(\*\*|__)\s*参考文献\s*(\*\*|__)[ \t]*$"
    return re.sub(pattern, "## 参考文献 \n\n", md_text, flags=re.MULTILINE)
def stream_handle_before_send(text: str):
    pipeline = [convert_citations_to_sup, modify_series_num]
    for fn in pipeline:
        text = fn(text)
    return text
# 将文本转换成可以作为文件名称的文本
def sanitize_filename(filename, replace_with="_"):
    """
    替换文件名中的非法字符
    :param filename: 原始文件名（不含路径）
    :param replace_with: 替换非法字符的字符（默认 "_"）
    :return: 安全的文件名
    """
    # 定义非法字符的正则表达式（包括空格、控制字符、Windows/Unix非法字符）
    illegal_chars = r'[<>:"/\\|?*\x00-\x1f]'  # \x00-\x1f 是控制字符
    # 替换非法字符
    safe_name = re.sub(illegal_chars, replace_with, filename)
    # 去除首尾空格和点（避免 ".filename" 或 "filename."）
    safe_name = safe_name.strip().strip('.')
    # 合并连续替换字符（如多个 "_" 变成单个 "_"）
    safe_name = re.sub(r'_{2,}', '_', safe_name)
    # Windows 保留名称检查（可选）
    windows_reserved = [
        'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 
        'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 
        'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    ]
    if safe_name.upper() in windows_reserved:
        safe_name = f"_{safe_name}"
    return safe_name
# 获取段落的序号属性
def get_num_props(paragraph:any):
    """
    获取段落的编号属性 (numId, ilvl)，如果存在
    """
    pPr = paragraph._p.pPr
    if pPr is not None and pPr.numPr is not None:
        numId = pPr.numPr.numId.val
        ilvl = pPr.numPr.ilvl.val
        return numId, ilvl
    return None, None
# 给段落设置编号属性
def set_num_props(paragraph, numId, ilvl):
    """
    给段落设置编号属性 (numId, ilvl)
    """
    p = paragraph._p

    # 确保 <w:pPr> 存在
    if p.pPr is None:
        pPr = docx.oxml.OxmlElement('w:pPr')
        p.insert(0, pPr)
    else:
        pPr = p.pPr

    # 构建 <w:numPr>
    numPr = docx.oxml.OxmlElement('w:numPr')

    ilvl_el = docx.oxml.OxmlElement('w:ilvl')
    ilvl_el.set(docx.oxml.ns.qn('w:val'), str(ilvl))
    numId_el = docx.oxml.OxmlElement('w:numId')
    numId_el.set(docx.oxml.ns.qn('w:val'), str(numId))

    numPr.append(ilvl_el)
    numPr.append(numId_el)

    pPr.append(numPr)

def set_hyperlink(
    paragraph: any,
    url: str,
    text: str,
    is_error: Optional[bool] = False
):
    try:
        part = paragraph.part
        r_id = part.relate_to(url, reltype="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink", is_external=True)

        # 创建 <w:hyperlink> 元素
        hyperlink = docx.oxml.OxmlElement('w:hyperlink')
        hyperlink.set(docx.oxml.ns.qn('r:id'), r_id)

        # 创建 run
        new_run = docx.oxml.OxmlElement('w:r')
        rPr = docx.oxml.OxmlElement('w:rPr')

        # 设置颜色
        color = docx.oxml.OxmlElement('w:color')
        color.set(docx.oxml.ns.qn('w:val'), 'FF0000' if is_error else '0000FF')  # 蓝色
        rPr.append(color)

        underline = docx.oxml.OxmlElement('w:u')
        underline.set(docx.oxml.ns.qn('w:val'), 'single')
        rPr.append(underline)

        font = docx.oxml.OxmlElement('w:rFonts')
        font.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
        font.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
        font.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        rPr.append(font)

        sz = docx.oxml.OxmlElement('w:sz')
        sz.set(docx.oxml.ns.qn('w:val'), '24')  # 12pt
        rPr.append(sz)

        new_run.append(rPr)

        # 添加文本
        text_elm = docx.oxml.OxmlElement('w:t')
        text_elm.text = text
        new_run.append(text_elm)

        # 将 run 添加到 hyperlink
        hyperlink.append(new_run)
    except Exception as e:
        logger.error(f"set_hyperlink报错: {str(e)}")
    paragraph._element.append(hyperlink)
# word文档的内容加上下划线
def draw_underline(doc_run, color="FF0000"):
    """
    给 doc_run 添加红色下划线，不影响已有格式（如加粗）
    """
    rPr = doc_run._element.rPr
    u = docx.oxml.OxmlElement('w:u')
    # u.set(docx.oxml.ns.qn('w:val'), 'single')  # 单实线下划线
    u.set(docx.oxml.ns.qn('w:color'), color)  # 设置下划线颜色为红色
    rPr.append(u)

def format_docx_file(doc):
    """
    统一处理Word文档格式化
    
    Args:
        doc: python-docx的Document对象
        
    Returns:
        doc: 格式化后的Document对象
    """
    is_in_citation_paragraph = False
    # 处理所有段落
    for i, paragraph in enumerate(doc.paragraphs):
        para_text: str = paragraph.text.strip()
        # 判断是否为标题段落
        is_heading = paragraph.style.name.startswith('Heading')
        
        # 如果是标题且是否为参考文献段落的标识为True
        if is_heading and is_in_citation_paragraph:
            is_in_citation_paragraph = False
        # 判断是否是参考文献标题
        if citation_head_match(para_text, True) and is_heading:
            is_in_citation_paragraph = True

        # 标题进行处理
        if is_heading:
            # 标题段落保持原样式，但更改字体为宋体
            paragraph.style.font.italic = False
            for run in paragraph.runs:
                # 安全地设置字体
                try:
                    # 直接设置字体名称（中英文）
                    run.font.name = '宋体'
                    # 尝试使用更安全的方式设置东亚字体
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        if hasattr(run._element.rPr, 'rFonts'):
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    # 设置西文字体为Times New Roman
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        if hasattr(run._element.rPr, 'rFonts'):
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                    run.font.bold = True  # 确保标题是粗体
                    # 设置字体颜色为黑色
                    run.font.color.rgb = RGBColor(0, 0, 0)
                    
                    run.font.italic = False  # 移除斜体属性
                    # 确保XML级别也移除斜体
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        for i in run._element.rPr.findall(docx.oxml.ns.qn('w:i')):
                            run._element.rPr.remove(i)
                        for i in run._element.rPr.findall(docx.oxml.ns.qn('w:iCs')):
                            run._element.rPr.remove(i)
                except Exception as e:
                    logger.warning(f"设置标题样式出错: {str(e)}")
        # 非标题进行处理
        else:
            paragraph.style = doc.styles['Normal']
            # 检查每个run，保留原始的加粗样式
            for run in paragraph.runs:
                # 保存原始加粗状态
                original_bold = run.bold
                
                # 直接设置字体名称（中英文）
                run.font.name = 'Times New Roman'
                # 同时设置东亚语言字体（确保中文显示为宋体）
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                # 设置西文字体为Times New Roman
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                
                # 恢复原始加粗状态，而不是强制设为False
                run.font.bold = original_bold
        is_citation_item = re.search(REFERENCE_RE, para_text)
        # is_citation_item = len(re.findall(REFERENCE_RE, para_text)) == 1  
        # 设置段落格式：段落间距、行距等
        try:
            if is_heading:
                # 标题段落设置
                paragraph.paragraph_format.space_before = docx.shared.Pt(12)  # 标题前增加间距
                paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 标题后统一间距
                
                # 一级标题(Heading 1)居中显示
                if paragraph.style.name == 'Heading 1':
                    paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            elif is_in_citation_paragraph:
                pure_text = ""
                url = ""
                for i, child in enumerate(paragraph._element):
                    if child.tag.endswith('hyperlink'):
                        # 获取关系ID r:id
                        r_id = child.get(docx.oxml.ns.qn('r:id'))
                        # 从关系映射中获取 URL
                        if r_id and r_id in doc.part._rels:
                            url = doc.part._rels[r_id].target_ref
                        else:
                            url = None
                        for node in child.iter():
                            if node.tag.endswith('t'):  # 文本节点
                                pure_text += node.text or ''
                    else:
                        for node in child.iter():
                            if node.tag.endswith('t'):  # 文本节点
                                pure_text += node.text or ''
                citation_error_pattern = r'^(\[\d+\].+\.)(\(.+?\))$'
                match = re.search(citation_error_pattern, pure_text)
                if match:
                    paragraph.clear()
                    text = match.group(1).strip()
                    error_text = match.group(2).strip()
                    if not url:
                        main_run = paragraph.add_run(text)
                        main_run.font.underline = True
                        # 直接设置字体名称（中英文）
                        main_run.font.name = '宋体'
                        # 画下划线
                        rPr = main_run._element.rPr
                        # 同时设置东亚语言字体（确保中文显示为宋体）
                        rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                        # 设置西文字体为Times New Roman
                        rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                        rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                        u = docx.oxml.OxmlElement('w:u')
                        u.set(docx.oxml.ns.qn('w:val'), 'single')  # 单实线下划线
                        u.set(docx.oxml.ns.qn('w:color'), "FF0000")  # 设置下划线颜色为红色
                        rPr.append(u)
                    else:
                        set_hyperlink(paragraph, url, text, True)
                    # 幻觉审查错误的文本标记为红色
                    remark_run = paragraph.add_run(error_text)
                    remark_run.font.color.rgb = RGBColor(255, 0, 0)

                    remark_run.font.name = '宋体'
                    remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    # 设置西文字体为Times New Roman
                    remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                    remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                else:
                    if url:
                        paragraph.clear()
                        set_hyperlink(paragraph, url, pure_text, False)
                # 首行悬挂缩进
                paragraph.paragraph_format.first_line_indent = docx.shared.Pt(-21)  # 悬挂缩进
                paragraph.paragraph_format.left_indent = docx.shared.Pt(21)  # 左缩进
                # 段前段后间距
                paragraph.paragraph_format.space_before = docx.shared.Pt(0)  # 移除段前间距
                paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 统一设置段后间距为12磅
                # 使用数值设置行距为1.5倍
                paragraph.paragraph_format.line_spacing = 1.5
            else:
                # 正文段落设置 - 统一所有正文段落的间距
                paragraph.paragraph_format.space_before = docx.shared.Pt(0)  # 移除段前间距
                paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 统一设置段后间距为12磅
                # 使用数值设置行距为1.5倍
                paragraph.paragraph_format.line_spacing = 1.5
                p = paragraph._p  # 获取底层 XML 对象
                pPr = p.pPr
                # 这是所有不带有doc序号（类似html标签的ul/ol li这种）的段落
                if not (pPr is not None and pPr.numPr is not None):
                    # print("wuhu")
                    paragraph.paragraph_format.first_line_indent = docx.shared.Pt(21)  # 首行缩进两个中文字符
                # 这说明是正文里面写了参考文献的格式
                if is_citation_item:
                    logger.info(f"正文里面写了参考文献的格式: {para_text}")
        except Exception as e:
            logger.warning(f"设置段落格式时出错: {str(e)}")
        # 正文里面引用参考文献的处理
        try:
            literature_pattern = r'\S+\[\d+(?:,\s*\d+)*\].*?[。！!?？\n\.]'
            error_text_pattern = r'\(\s*(?:\[\d+\][^、\[\]]+)(?:、\[\d+\][^、\[\]]+)*\s*\)'
            # cite_num_pattern = r'^\[\s*\d+(?:\s*,\s*\d+)*\s*\]$'
            search_match = re.search(literature_pattern, para_text)
            if search_match:
                # last_run = None
                for i, item in enumerate(paragraph.runs):
                    run_text = item.text.strip()
                    if re.search(error_text_pattern, run_text):
                        item.font.color.rgb = RGBColor(255, 0, 0)
                    if item.font.underline:
                        draw_underline(item)
                    # if re.search(cite_num_pattern, run_text.strip()):
                    #     draw_underline(last_run)
                    # last_run = item
        except Exception as e:
            logger.error(f"处理有引用的段落的报错：{str(e)}")
    return doc

# 这是因为YAML的---会导致pandoc转换失败
def remove_dash_lines(text: str) -> str:
    lines = text.splitlines()
    return '\n'.join(line for line in lines if line.strip() != '---')
def bracket_latex_to_dollar(text: str) -> str:
    # 替换块级公式 \[...\]
    def replace_block(match):
        content = match.group(1).replace('\n', ' ')
        return f'$$ {content.strip()} $$'

    text = re.sub(r'\\\[\n\s*(.*?)\n\s*\\\]', replace_block, text, flags=re.DOTALL)

    # 替换行内公式 \(...\)
    def replace_inline(match):
        content = match.group(1).replace('\n', ' ')
        return f'${content.strip()}$'  # 或者改成 f'${content.strip()}$' 保持行内形式

    text = re.sub(r'\\\((.*?)\\\)', replace_inline, text, flags=re.DOTALL)

    def replacer(match):
        content = match.group(1)
        # 去除多余空格和换行符，拼成一行
        single_line = ' '.join(line.strip() for line in content.strip().splitlines())
        return f"$$ {single_line} $$"
    
    text = re.sub(r'\$\$\s*(.*?)\s*\$\$', replacer, text, flags=re.DOTALL)
    return text
def fix_math_formula_format(text: str) -> str:
    """
    修复数学公式格式，确保符合LaTeX规范:
    1. 单行公式：$formula$ (无空格)
    2. 多行公式：$$ formula $$ (有空格)
    """
    logger.info(f"准备执行fix_math_formula_format函数，处理前的文本长度: {len(text)}")
    # 修复多行公式格式：确保 $$ 前后有空格
    def fix_display_math(match):
        content = match.group(1).strip()
        return f'$$ {content} $$'
    
    # 处理 $$...$$
    text = re.sub(r'\$\$\s*(.*?)\s*\$\$', fix_display_math, text, flags=re.DOTALL)
    
    # 修复单行公式格式：确保 $ 前后无空格
    def fix_inline_math(match):
        content = match.group(1).strip()
        return f'${content}$'
    
    # 处理 $...$ 但避免处理 $$...$$
    # 使用负向前瞻和负向后顾来避免匹配 $$
    text = re.sub(r'(?<!\$)\$(?!\$)\s*(.*?)\s*\$(?!\$)', fix_inline_math, text, flags=re.DOTALL)
    logger.info(f"fix_math_formula_format函数执行完毕，处理后的文本长度: {len(text)}")
    return text
def handle_text_before_use(
    text: str,
    fn_list: Optional[List[str]] = [
        "remove_dash_lines",
        "bracket_latex_to_dollar",
        "format_to_markdown_link",
        "change_markdown_paragraph_join_way",
        "escape_caret"
    ]
):
    """
    文本预处理函数
    
    Args:
        text: 原始文本
        fn_list: 要执行的处理函数列表
        heading_adjust_config: 标题级别调整配置，如 {"level": 2, "strategy": "promote_to_next"}
    """
    pipeline = []
    if "remove_dash_lines" in fn_list:
        pipeline.append(remove_dash_lines)
    if "bracket_latex_to_dollar" in fn_list:
        pipeline.append(bracket_latex_to_dollar)
    if "format_to_markdown_link" in fn_list:
        pipeline.append(format_to_markdown_link)
    if "change_markdown_paragraph_join_way" in fn_list:
        pipeline.append(change_markdown_paragraph_join_way)
    if "fix_math_formula_format" in fn_list:
        pipeline.append(fix_math_formula_format)
    if "escape_caret" in fn_list:
        pipeline.append(escape_caret)
    for fn in pipeline:
        text = fn(text)
    return text
# 将正文里面html添加下划线的方式改为[]{.underline}形式
def convert_underline_text(text: str, is_remove:Optional[bool] = False):
    pattern = re.compile(
        r"<span\s+[^>]*?style=['\"]text-decoration\s*:\s*underline;?[^'\"]*['\"][^>]*>(.*?)</span>",
        re.IGNORECASE | re.DOTALL
    )
    return pattern.sub(r"[\1]{.underline}", text) if not is_remove else pattern.sub(r"\1", text)
# 去掉（字数：xxx）这种文字
def remove_word_count_tag(text: str) -> str:
    # 匹配全角括号里的"字数：数字"
    # 按段落分割（以两个换行作为段落分隔）
    paragraphs = text.strip().split('\n\n')

    if not paragraphs:
        return text
    list_data = []
    reg = re.compile(r'^（.*?）$')
    for item in paragraphs:
        if not reg.match(item.strip()):
            list_data.append(item)
    # 重新组合段落
    return '\n\n'.join(list_data)
# 移除分步生成正文的参考文献部分及后面的段落内容
def remove_after_reference(text: str):
    lines = text.strip().splitlines()
    """
    从 markdown 文本末尾往前遍历，丢弃以 [数字] 开头的行或者包含参考文献的标题，
    遇到第一个不是 [数字] 开头的行时停止，并返回剩余文本。
    """
    pattern = re.compile(r'^.{0,7}参考文献.{0,7}$')
    print(f"长度{len(lines)}")

    while lines:
        # 如果是以[数字]形式开头的舍弃
        if re.match(r"^\[\d+\]", lines[-1].strip()):
            lines.pop()
        # 如果是以[^数字]形式开头的舍弃
        elif re.match(r"\[\^\d+\]", lines[-1].strip()):
            lines.pop()
        # 如果是包含参考文献的标题，进行舍弃
        elif pattern.match(lines[-1].strip()):
            lines.pop()
        # 如果是空行就先干掉
        elif not lines[-1].strip():
            lines.pop()
        else:
            break
    return "\n".join(lines)
# 将分段生成里面的标题移除
def remove_markdown_heading(text: str, heading_text: str) -> str:
    """
    从Markdown文本中移除指定的标题行
    
    Args:
        text: 原始Markdown文本
        heading_text: 要移除的标题文本
        
    Returns:
        str: 移除标题后的文本，如果输入参数无效则返回原文本或空字符串
    """
    # 边界检查 - 安全处理，不抛出异常
    if not text or not heading_text:
        logger.debug("text或heading_text为空，直接返回原文本")
        return text or ""
    
    # 去除heading_text的前后空白
    heading = heading_text.strip()
    if not heading:
        logger.debug("heading_text去除空白后为空，直接返回原文本")
        return text
    
    logger.info(f"尝试移除标题: {heading}")
    
    # 使用更安全的正则表达式，避免灾难性回溯
    # 修复语法：正确匹配1-6个#号，支持#后有0个或多个空格的情况
    pattern = re.compile(
        rf'^[ ]{{0,3}}#{{1,6}}[ ]*{re.escape(heading)}[ ]*$',
        re.MULTILINE
    )
    try:
        logger.info("pattern后面了")
        result = pattern.sub('', text).strip()
        logger.info("result后面")
    except Exception as e:
        logger.info(f"直接使用heading_text匹配报错: {str(e)}")

    # 如果heading_text是以-开头的，那就去掉-
    if heading_text.startswith("-"):
        heading_text = re.sub(r'^-\s+', '', heading_text, flags=re.MULTILINE).strip()
        logger.info(heading_text)
        pattern = re.compile(
            rf'^[ ]{{0,3}}#{{1,6}}[ ]*\d+\.[ ]*{re.escape(heading_text)}[ ]*$',
            re.MULTILINE
        )
        result = pattern.sub('', text).strip()
    logger.debug(f"标题移除操作完成，原文本长度: {len(text)}, 处理后长度: {len(result)}")
    return result

# 通用的Markdown转Word处理函数
def convert_markdown_to_docx(
    file_path: str
):
    """
    将Markdown文件转换为格式化的Word文档
    
    Args:
        file_path: Markdown文件的绝对路径或者相对路径
        
    Returns:
        tuple: (临时文件路径, 下载文件名)
    """
    abs_path = ""
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    filename = os.path.basename(abs_path)
    
    # 智能处理文件扩展名
    name_without_ext = os.path.splitext(filename)[0]
    output_filename = f"{name_without_ext}.docx"
    
    # temp_docx_file = os.path.dirname(abs_path) + f"/{output_filename}"
    # 创建临时输出文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as tmp:
        temp_docx_file = tmp.name
    try:
        text = read_file_content(abs_path)
    except Exception as e:
        raise e
    text = handle_text_before_use(
        text,
        [
            "change_markdown_paragraph_join_way",
            "remove_dash_lines",
            "bracket_latex_to_dollar"
        ]
    )
    text = convert_citations_to_sup(text, "caret")
    text = convert_underline_text(text)
    # text, picture_list = transform_image_url(text)
    
    # 转换文件
    pypandoc.convert_text(
        text,
        'docx',
        outputfile=temp_docx_file,
        # markdown改成markdown-smart是为了解决""转为中文的角标时候的问题(会转为””)。
        format='markdown-smart',
        extra_args=[
            '--standalone',
            '--mathml',
            '--shift-heading-level-by=0',  # 保持原始标题级别: # -> 标题1, ## -> 标题2, ### -> 标题3
        ]
    )
    
    # 使用python-docx处理文档格式
    doc = Document(temp_docx_file)
    doc = format_docx_file(doc)
    doc.save(temp_docx_file)
    # return temp_docx_file, output_filename
    # 返回文件下载响应
    def remove_file(temp_docx_file):
        # 移除临时文件
        os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None
    return FileResponse(
        path=temp_docx_file,
        filename=output_filename,
        # media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        background=BackgroundTask(remove_file, temp_docx_file=temp_docx_file)
    )
# def convert_markdown_to_docx(
#     file_path: str
# ):
#     """
#     将Markdown文件转换为格式化的Word文档
    
#     Args:
#         file_path: Markdown文件的绝对路径或者相对路径
        
#     Returns:
#         tuple: (临时文件路径, 下载文件名)
#     """
#     abs_path = ""
#     # 获取绝对路径
#     if os.path.isabs(file_path):
#         abs_path = file_path
#     else:
#         abs_path = os.path.join(os.getcwd(), file_path)
#     filename = os.path.basename(abs_path)
#     output_filename = filename.replace(".txt", ".docx")
#     # 创建临时输出文件
#     with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as tmp:
#         temp_docx_file = tmp.name
#     try:
#         text = read_file_content(abs_path)
#     except Exception as e:
#         raise e
#     # 转换文件
#     pypandoc.convert_text(
#         change_markdown_paragraph_join_way(text),
#         'docx',
#         outputfile=temp_docx_file,
#         format='markdown',
#         extra_args=[
#             '--standalone',
#             '--shift-heading-level-by=0'  # 保持原始标题级别: # -> 标题1, ## -> 标题2, ### -> 标题3
#         ]
#     )
    
#     # 使用python-docx处理文档格式
#     doc = Document(temp_docx_file)
#     doc = format_docx_file(doc)
#     doc.save(temp_docx_file)
    
#     # return temp_docx_file, output_filename
#     # 返回文件下载响应
#     return FileResponse(
#         path=temp_docx_file,
#         filename=output_filename,
#         media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
#         background=BackgroundTask(lambda: os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None)
#     )
# 参考文献幻觉审查时参考文献的拼接方式
def hallucination_combine_citation(
    # 参考文献除url的文本可能包含序号也可能不包含序号，就看index有没有传
    full_text: str,
    # 参考文献的序号
    index: Optional[int] = None,
    # 参考文献的链接
    url: Optional[str] = None,
    # 错误信息
    error_list: List[str] = []
):
    text = f"{[index]} " if index else ""
    if error_list:
        text += f"<span style='text-decoration: underline;text-decoration-color: red;'>"
        if url:
            text += f"[{full_text}]({url})</span><span style='color: red'>&#40;{'、'.join(error_list)}&#41;</span>"
        else:
            text += f"{full_text}</span><span style='color: red'>&#40;{'、'.join(error_list)}&#41;</span>"
    else:
        if url:
            text += f"[{full_text}]({url})"
        else:
            text += f"{full_text}"
    return text

#将word文档转成markdown，这里只是简单的
def docx_file_to_markdown(
    file_path: str
):
    abs_path = ""
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    output = pypandoc.convert_file(abs_path, 'md')
    return output

def remove_paragraph_bold(text: str) -> str:
    """
    处理 Markdown 文本：
    1. 整段加粗去掉加粗标记
    2. 标题长度 > 30 去掉标题标记
    其他内容保持原样，包括原始换行
    """
    # 按段落拆分（一个或多个空行分隔段落，同时保留空行分隔符）
    paragraphs = re.split(r'(\n\s*\n+)', text)
    
    new_paragraphs = []
    for p in paragraphs:
        stripped = p.strip()
        
        # 空段落或分隔符，直接保留
        if not stripped:
            new_paragraphs.append(p)
            continue
        
        # ---- 整段加粗 ----
        if re.fullmatch(r'\*\*(.+?)\*\*', stripped):
            new_paragraphs.append(stripped[2:-2])
            continue
        # ---- 长标题去掉标记 ----
        if len(stripped) > 30:
            # Markdown 标题
            md_match = re.match(r'^(#{1,6})\s*(.+)', stripped)
            if md_match:
                new_paragraphs.append(md_match.group(2))
                continue
        # ---- 其他段落保持原样 ----
        new_paragraphs.append(p)
    
    # 拼回文本
    return ''.join(new_paragraphs)


def split_num_title(text: str):
    raw = text.strip()
    if re.match(r"^\d+(\.\d+)+", raw):  # 跳过 1.1 / 1.1.1
        return None
    m = re.match(r"^(\d+)([\.、])(?!\d)(.*)", raw)
    if m:
        return {"raw": raw, "num": m.group(1), "title": m.group(2) + m.group(3).strip()}
    m = re.match(r"^([一二三四五六七八九十]+)(、)(.*)", raw)
    if m:
        return {"raw": raw, "num": m.group(1), "title": m.group(2) + m.group(3).strip()}
    return None

def markdown_to_outline(md_text):
    """
    把 markdown 标题解析成树状结构
    """
    root = {"title": "root", "num": None, "raw": "root", "level": 0, "children": []}
    stack = [root]

    for line in md_text.splitlines():
        if not line.strip().startswith("#"):
            continue

        # 匹配标题
        match = re.match(r'^(#+)\s*(.*)', line.strip())
        if not match:
            continue

        hashes, content = match.groups()
        level = len(hashes)

        raw = line.strip()
        num, title = None, content

        # 提取序号 (仅匹配 "1." 这种 或 "一、" 这种)
        m = re.match(r'^(\d+)(\.)(.*)', content)
        if m and not re.match(r'^\d+\.\d', content):  # 过滤 1.1 / 1.1.1
            num = m.group(1)
            title = m.group(2) + m.group(3)  # 保留点号
        else:
            m = re.match(r'^([一二三四五六七八九十]+)(、)(.*)', content)
            if m:
                num = m.group(1)
                title = m.group(2) + m.group(3)  # 保留顿号

        node = {
            "raw": raw,
            "level": level,
            "num": num,
            "title": title.strip(),
            "children": []
        }

        # 找父节点
        while stack and stack[-1]["level"] >= level:
            stack.pop()
        stack[-1]["children"].append(node)
        stack.append(node)

    return root

def renumber_tree(node: dict):
    """
    给同一层级重新编号
    """
    children = node.get("children", [])
    if not children:
        return

    # 判断起始是数字还是中文
    if children[0]["num"] and children[0]["num"].isdigit():
        for i, child in enumerate(children, start=1):
            child["num"] = str(i)
    elif children[0]["num"]:
        chinese_nums = "一二三四五六七八九十"
        for i, child in enumerate(children, start=1):
            child["num"] = chinese_nums[i-1]

    for child in children:
        renumber_tree(child)

def collect_replacements(node, replacements):
    """
    收集替换映射 (raw -> new_text)
    """
    if node.get("raw") and node["raw"] != "root":
        new_text = "#" * node["level"] + " " + (node["num"] or "") + (node["title"] or "")
        replacements.append((node["raw"], new_text))
    for child in node.get("children", []):
        collect_replacements(child, replacements)

def replace_markdown(md_text, tree):
    """
    遍历树，利用 raw 字段在原始 markdown 中做替换
    """
    replacements = []
    collect_replacements(tree, replacements)

    # 避免替换冲突，先替换长的
    replacements.sort(key=lambda x: len(x[0]), reverse=True)

    new_text = md_text
    for raw, new in replacements:
        new_text = new_text.replace(raw, new, 1)

    return new_text
def modify_series_num(text: str):
    """将一段markdown文字里面的序号错乱的文字标记正确"""
    return text
    # tree = markdown_to_outline(text)
    # renumber_tree(tree)
    # return replace_markdown(text, tree)
# 用于生成各种报告或者大纲的存储地址
def generate_storage_path(
    project_id: str,
    prefix: Optional[str] = None,
    name: Optional[str] = None
):
    prefix = prefix or "without_name"
    project_folder = f"llm_file/{project_id}"
    timestamp = int(time.time() * 1000)
    file_name = f"{prefix}_{timestamp}.txt"
    if name:
        file_name = sanitize_filename(f"{prefix}_{name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = f"{project_folder}/{file_name}"
    return relative_path

# 将参考文献根据title属性进行去重
def deduplicate_by_title(list_data: List[LiteratureResponse]):
    seen_titles = set()
    result = []
    for item in list_data:
        title = item.title
        if title and title not in seen_titles:
            seen_titles.add(title)
            result.append(item)
    return result

def remove_contain_outline_headings(text: str) -> str:
    """
    移除 markdown 文本末尾最多一个以【大纲】结尾的标题（直接文本替换方式）
    """
    return re.sub(r'(?m)^\s*#{1,6}\s*.*?大纲\s*(?:\r?\n)+', '', text)
