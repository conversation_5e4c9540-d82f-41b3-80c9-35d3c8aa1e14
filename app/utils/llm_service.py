import json
import aiohttp
import asyncio
import traceback
from typing import Dict, List, Optional, Callable
import os
from pydantic import UUID4
from app.core.config import settings
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
from app.utils.constants import ProductType
import base64
from app.api.repository.llm_call_log import create_llm_call_log, update_llm_call
from app.utils.enum import ErrorType


# 获取logger实例
logger = get_logger(__name__)

def extract_and_remove_system(messages: List[dict]):
    """
    从 messages 中移除 role=system 的项，
    返回 (新的 messages, system_contents)
    """
    logger.info(messages)
    system_contents = [m["content"] for m in messages if m.get("role") == "system"]
    new_messages = [m for m in messages if m.get("role") != "system"]
    return new_messages, system_contents[0] if len(system_contents) > 0 else None
def is_anthropic_provider(api_url:str):
    return "api.anthropic.com" in api_url
def get_proxy():
    query = {}
    proxy = settings.PROXY_URL
    proxy_auth_username = settings.PROXY_USERNAME
    proxy_auth_password = settings.PROXY_PASSWORD
    # 如果配置了代理服务器信息
    if proxy and proxy_auth_password and proxy_auth_username:
        # aiohttp 需要 proxy headers 来传递 Basic Auth
        credentials = f"{proxy_auth_username}:{proxy_auth_password}"
        encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
        proxy_headers = {
            "Proxy-Authorization": f"Basic {encoded_credentials}"
        }
        query["proxy"] = proxy
        query["proxy_headers"] = proxy_headers
    return query
def set_auth_data(
    is_anthropic:bool,
    apiKey: str
):
    headers = {}
    if is_anthropic:
        headers["X-Api-Key"] = f"{apiKey}"
        headers["Anthropic-Version"] = "2023-06-01"
        # anthropic的系统提示词写法与众不同
    else:
        headers["Authorization"] = f"Bearer {apiKey}"
    return headers
def set_anthropic_message(
    messages: List[dict]
):
    payload = {}
    messages_list, system_prompt = extract_and_remove_system(messages=messages)
    payload["messages"] = messages_list
    if system_prompt:
        payload["system"] = system_prompt
    payload["max_tokens"] = settings.ANTHROPIC_MAX_TOKEN
    return payload
async def stream_llm_and_save(
    messages: List[Dict[str, str]],
    apiKey,
    apiUrl,
    model,
    user: UserResponse,
    flag: str,
    callback: Callable[[str], None],
    complete_callback: Callable[[str], None],
    error_callback: Callable[[str], None],
    related_id: Optional[UUID4] = None,
    product_type = ProductType.DOCGEN.value
) -> str:
    """
    流式调用LLM，实时保存响应内容到文件，并返回完整响应
    
    Args:
        messages: 聊天消息列表
        model: 模型名称
        callback: 可选的回调函数，每接收到一个chunk就调用一次
        complete_callback: 可选的完成回调函数，生成完成时调用
    
    Returns:
        完整的模型响应文本
    """
    query = {
        "url": apiUrl
    }
    logger.info(f"流式调用LLM并保存到文件，模型: {model}")
    if not apiKey:
        err_msg = "未设置API_KEY环境变量"
        logger.error(err_msg)
        error_callback and await error_callback(err_msg)
        # raise ValueError("未设置API_KEY环境变量")
    logger.info(f"开始请求API: {model}")
    headers = {
        "X-Title": "Hi-IdeaGen",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model,
        "messages": messages,
        "stream": True,
        "temperature": settings.LLM_DEFAULT_TEMPERATURE,
        "top_k": settings.LLM_DEFAULT_TOP_K,
        "top_p": settings.LLM_DEFAULT_TOP_P,
        # "provider": {
        #     "order": [
        #         "anthropic"
        #     ]
        # }
    }
    is_anthropic = is_anthropic_provider(api_url=apiUrl)
    headers = headers|set_auth_data(is_anthropic=is_anthropic, apiKey=apiKey)
    if is_anthropic:
        # anthropic需要配置代理才可以访问
        proxy_data = get_proxy()
        query = query | proxy_data
        payload = payload|set_anthropic_message(messages=messages)
        
    
    logger.info(f"大模型流式请求的参数:{str(payload)}")
    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"流式LLM请求，超时设置: {timeout.total}秒,温度: {settings.LLM_DEFAULT_TEMPERATURE},上采样top_k: {settings.LLM_DEFAULT_TOP_K},上采样top_p: {settings.LLM_DEFAULT_TOP_P}")
    
    try:
        llm_log = await create_llm_call_log(
            current_user=user,
            model_name=model,
            model_api_key=apiKey,
            model_api_url=apiUrl,
            response="",
            messages=messages,
            product_type=product_type,
            related_id=related_id,
            flag=flag
        )
    except Exception as e:
        logger.error(f"创建大模型调用日志失败：{str(e)}")
    query["headers"] = headers
    query["timeout"] = timeout
    query["json"] = payload
    try:
        full_response = ""
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送流式请求: {settings.OPENROUTER_URL}")
            async with session.post(
                **query
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    error_msg = f"模型名称:{model},apiUrl: {apiUrl} 错误: 状态码 {resp.status}, 错误详情: {error_text}"
                    logger.error(error_msg)
                    error_callback and await error_callback(error_msg)
                    return f"API请求错误: {error_msg}"
                
                # 流式响应并写入文件
                logger.debug(f"开始接收流式LLM响应并写入文件")
                chunk_count = 0
                open_router_id: Optional[str] = None
                # 结束原因：
                stop_reason = {
                    "finish_reason": "",
                    "native_finish_reason": ""
                }
                # token消耗
                token_consumed = {
                    "input": 0,
                    "output": 0
                }
                # 使用追加模式写入文件
                try:
                    if not is_anthropic:
                        async for line in resp.content:
                            try:
                                line_str = line.decode('utf-8').strip()
                                if not line_str:
                                    continue
                                
                                # 跳过 "data: " 前缀
                                if line_str.startswith("data: "):
                                    line_str = line_str[6:]
                                
                                # 处理流结束标记
                                if line_str == "[DONE]":
                                    logger.debug("流式响应结束")
                                    break
                                # 解析 JSON 数据
                                try:
                                    data = json.loads(line_str)
                                    open_router_id = data.get("id")
                                    # print(open_router_id)
                                    # 提取内容增量
                                    if 'choices' in data and len(data['choices']) > 0:
                                        delta = data['choices'][0].get('delta', {})
                                        if 'content' in delta and delta['content']:
                                            chunk = delta['content']

                                            # is_not_empty = chunk.strip() and not chunk.isspace()
                                            # 只有当chunk包含非空白字符时，才将其拼接到full_response
                                            if chunk:
                                                full_response += chunk
                                            logger.info(f"LLM返回的文字：{chunk[:100]}")
                                            # 如果有回调函数，则调用
                                            if callback and chunk:
                                                await callback(chunk)
                                            
                                            chunk_count += 1
                                        finish_reason = data['choices'][0].get('finish_reason', "")
                                        native_finish_reason = data['choices'][0].get('native_finish_reason', "")
                                        if finish_reason:
                                            stop_reason["finish_reason"] = finish_reason
                                        if native_finish_reason:
                                            stop_reason["native_finish_reason"] = native_finish_reason
                                    if 'usage' in data and data["usage"]:
                                        token_consumed["input"] = data["usage"]["prompt_tokens"]
                                        token_consumed["output"] = data["usage"]["completion_tokens"]
                                except json.JSONDecodeError as je:
                                    logger.error(f"无法解析 JSON '{line_str[:100]}...': {str(je)}")
                                except Exception as e:
                                    logger.error(f"处理响应数据时出错: {str(e)}, 数据: {line_str[:100]}...")
                            except UnicodeDecodeError as ue:
                                logger.error(f"解码响应时出错: {str(ue)}")
                            except Exception as e:
                                logger.error(f"处理响应行时出错: {str(e)}")
                    else:
                        async for line in resp.content:
                            line_str = line.decode('utf-8').strip()
                            if not line_str:
                                continue
                            # 跳过 "data: " 前缀
                            if line_str.startswith("data: "):
                                line_str = line_str[6:]
                                logger.info(f"流式响应数据：{line_str}")
                            if line_str == "event: content_block_stop":
                                logger.debug("流式响应结束")
                                continue
                            if line_str.startswith('data: {"type":"message_delta"'):
                                logger.debug("流式响应结束")
                                end_data = json.loads(line_str[6:])
                                finish_reason = end_data['delta'].get('stop_reason', "")
                                if finish_reason:
                                    stop_reason["finish_reason"] = finish_reason
                                if finish_reason:
                                    stop_reason["native_finish_reason"] = finish_reason
                                if 'usage' in end_data and end_data["usage"]:
                                    token_consumed["input"] = end_data["usage"]["input_tokens"]
                                    token_consumed["output"] = end_data["usage"]["output_tokens"]
                                break   
                            try:
                                data = json.loads(line_str)
                                # 提取内容增量
                                if 'delta' in data and data['delta']['text']:
                                    delta = data['delta'].get('text', "")
                                    chunk = delta

                                    # is_not_empty = chunk.strip() and not chunk.isspace()
                                    # 只有当chunk包含非空白字符时，才将其拼接到full_response
                                    if chunk:
                                        full_response += chunk
                                    logger.info(f"LLM返回的文字：{chunk[:100]}")
                                    # 如果有回调函数，则调用
                                    if callback and chunk:
                                        await callback(chunk)
                                    
                                    chunk_count += 1
                            except json.JSONDecodeError as je:
                                logger.error(f"无法解析 JSON '{line_str[:100]}...': {str(je)}")
                            except Exception as e:
                                logger.error(f"处理响应数据时出错: {str(e)}, 数据: {line_str[:100]}...")
                                        
                except Exception as e:
                    logger.error(f"读取响应流时出错: {str(e)}")
                
                logger.info(f"流式响应完成，接收了 {chunk_count} 个数据块，总长度: {len(full_response)}")
                
                # 如果没有收到任何内容，记录更多的调试信息
                if not full_response:
                    result = ErrorType.NOT_VALID_TEXT.value
                    logger.warning(result)
                    if error_callback:
                        await error_callback(result)
                    # 写入一些信息到文件，表明请求成功但没有收到内容
                    return f"请求完成，但未收到有效内容"
                
                # 调用完成回调
                if complete_callback:
                    try:
                        logger.info("LLM流式已经完成了。现在调用complete_callback函数")
                        logger.info(f"报告{related_id}的结束原因：{str(stop_reason)},token消耗：{str(token_consumed)}")
                        if not token_consumed.get("input"):
                            logger.error(f"模型：{model}/{apiUrl}报错了，输入输出token消耗为空")
                        await complete_callback(token_consumed, full_response)
                        await update_llm_call(
                            log_id=llm_log.id,
                            response=full_response
                        )
                    except Exception as e:
                        logger.error(f"执行完成回调时出错: {str(e)}")
                
                return full_response
                    
    except asyncio.TimeoutError as te:
        error_msg = f"模型名称:{model},apiUrl: {apiUrl} 请求超时 ({timeout.total} 秒)"
        logger.error(error_msg)
        error_callback and await error_callback(error_msg)
        return f"请求超时: {error_msg}"
    except aiohttp.ClientError as ce:
        error_msg = f"HTTP客户端错误: {str(ce)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"调用OpenRouter时发生错误: {str(e)}"
        logger.error(error_msg)
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        error_callback and await error_callback(error_msg)
        return error_msg 
    except asyncio.CancelledError:
        logger.warning("请求被取消")
        raise