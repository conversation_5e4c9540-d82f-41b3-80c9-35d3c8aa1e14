from pydub import AudioSegment
from app.core.logging import get_logger
from typing import Self, Dict, Optional, List
import os
from app.api.repository.upload_file import UPLOAD_DIR
import json

logger = get_logger(__name__)

class Item():
    def __init__(self, is_end: bool, text: str, starttime: str, endtime: str):
        self.is_end = is_end
        self.text = text
        self.starttime = starttime
        self.endtime = endtime
    def to_dict(self):
        return {
            "text": self.text,
            "starttime": self.starttime,
            "endtime": self.endtime,
        }

class Data():
    def __init__(self, pcm_data: bytearray, text: List[Item]):
        self.pcm_data = pcm_data
        self.text = text
    def add_text(self, data: Item):
        if self.text and not self.text[-1].is_end:
            self.text.pop()
        self.text.append(data)
    def add_pcm(self, pcm: bytearray):
        self.pcm_data.extend(pcm)

class PcmHandler():
    def __init__(self):
        self.data: Dict[str, Data] = {}
    # 创建一个pcm数据存储
    def create_data(self, id: str) -> Self:
        self.data[str(id)] = Data(
            text=[],
            pcm_data=bytearray()
        )
        return self
    # 往pcm列表里面增加数据
    def add_data(self, id: str, pcm_data: Optional[bytearray] = None, text: Optional[Item] = None) -> Self:
        data = self.get_data(id=id)
        if not data:
            data = self.create_data(id=id).get_data(id=id)
        if pcm_data:
            data.add_pcm(pcm=pcm_data)
        if text:
            data.add_text(data=text)
        return self
    # 判断是否存在某个id的数据存储
    def get_data(self, id: str):
        return self.data.get(str(id))
    # 移除数据存储
    def remove_data(self, id: str):
        try:
          del self.data[str(id)]
        except Exception as e:
            logger.error(f"移除PCM数据错误：{str(e)}")
        return self
    def save_pcm_to_mp3(self, id: str, base_path: Optional[str] = UPLOAD_DIR) -> str:
        data = self.get_data(id=id)
        if not data:
            raise Exception("录音数据不存在")
        pcm_data = data.pcm_data
        audio = AudioSegment(
            data=bytes(pcm_data),
            sample_width=2,     # 16bit = 2 bytes
            frame_rate=16000,   # 16kHz
            channels=1          # 单声道
        )
        relative_path = f"{base_path}/output_{id}.mp3"
        logger.info(f"保存的MP3文件路径：{relative_path}")
        abs_file_path = os.path.join(os.getcwd(), relative_path)
        logger.info(f"======保存的MP3文件路径：{abs_file_path}")
        audio.export(abs_file_path, format="mp3")
        logger.info(f"保存的MP3文件成功: {relative_path}")
        return relative_path
    def combine_transcription_text(self, id: str):
        data = self.get_data(id=id)
        if not data:
            raise Exception("录音数据不存在")
        text = data.text
        json_str = json.dumps([item.to_dict() for item in text], ensure_ascii=False, indent=2)
        return json_str