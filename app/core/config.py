import os
from typing import Optional
from dotenv import load_dotenv

# 临时解决方案：直接使用BaseModel代替BaseSettings
from pydantic import BaseModel

class BaseSettings(BaseModel):
    """临时的BaseSettings替代方案"""
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 显式加载.env文件
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), ".env")
if os.path.exists(dotenv_path):
    print(f"config.py: 加载.env文件: {dotenv_path}")
    load_dotenv(dotenv_path, override=True)

class Settings(BaseSettings):
    PROJECT_NAME: str = "Hi-IdeaGen"
    PROJECT_VERSION: str = "0.1.0"
    PROJECT_DESCRIPTION: str = "智能研究报告生成系统"
    
    # 服务配置
    PORT: int = int(os.environ.get("PORT", "8001"))
    HOST: str = os.environ.get("HOST", "0.0.0.0")
    
    # 数据库配置
    DATABASE_URL: str = os.environ.get("DATABASE_URL", "postgres://postgres:postgres@localhost:5432/hi_ideagen")
    
    # JWT配置
    SECRET_KEY: str = os.environ.get("SECRET_KEY", "your-secret-key-to-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 14  # 14天
    
    # 默认管理员配置
    DEFAULT_ADMIN_USERNAME: str = os.environ.get("DEFAULT_ADMIN_USERNAME", "idea_admin")
    DEFAULT_ADMIN_PASSWORD: str = os.environ.get("DEFAULT_ADMIN_PASSWORD", "VD9liPmHg$4G0mNv")
    DEFAULT_ADMIN_ROLE: str = os.environ.get("DEFAULT_ADMIN_ROLE", "super_admin")
    # API密钥前缀
    API_KEY_PREFIX: str = os.environ.get("API_KEY_PREFIX", "sk-")
    
    # 加密配置
    CRYPTO_BASE_KEY: str = os.environ.get("CRYPTO_BASE_KEY", "P7YxQwbK3tJnMrVfL8zSgHe5R2NdAa6Z")
    
    # 研究配置
    # OPENROUTER_API_KEY: str = os.environ.get("OPENROUTER_API_KEY", "")
    SERPAPI_API_KEY: str = os.environ.get("SERPAPI_API_KEY", "")
    JINA_API_KEY: str = os.environ.get("JINA_API_KEY", "")
    
    # PubMed配置
    PUBMED_TOOL_NAME: str = os.environ.get("PUBMED_TOOL_NAME", "HiIdeagen")
    PUBMED_EMAIL: str = os.environ.get("PUBMED_EMAIL", "")
    
    # API端点
    OPENROUTER_URL: str = "https://openrouter.ai/api/v1/chat/completions"
    SERPAPI_URL: str = "https://serpapi.com/search"
    SERPAPI_GOOGLE_SCHOLAR_URL: str = "https://serpapi.com/search?engine=google_scholar"
    JINA_BASE_URL: str = "https://r.jina.ai/"
    OPENROUTER_API_URL: str = "https://openrouter.ai/api/v1"
    WZBJ_API_URL: str = os.environ.get("WZBJ_API_URL", "")
    GROBID_BASE_URL: str = os.environ.get("GROBID_BASE_URL", "http://localhost:8070")
    
    # 搜索配置
    SEARCH_RESULTS_LIMIT: int = int(os.environ.get("SEARCH_RESULTS_LIMIT", "30"))
    # DEFAULT_MODEL: str = os.environ.get("DEFAULT_MODEL", "anthropic/claude-3.7-sonnet")
    RESEARCH_ITERATION_LIMIT: int = int(os.environ.get("RESEARCH_ITERATION_LIMIT", "5"))
    MAX_SEARCH_QUERIES: int = int(os.environ.get("MAX_SEARCH_QUERIES", "35")) #最多处理的搜索查询
    MAX_CONTEXTS: int = int(os.environ.get("MAX_CONTEXTS", "5"))  # 使用配置的最大上下文数量
    MAX_LITERATURE_AND_CONTEXTS: int = int(os.environ.get("MAX_LITERATURE_AND_CONTEXTS", "35"))
    MAX_LITERATURES_COUNT: int = int(os.environ.get("MAX_LITERATURE_AND_CONTEXTS", "35"))  # 使用配置的最大文献数量
    MAX_HALLUCINATION_DETECTION_SEARCH: int = int(os.environ.get("MAX_HALLUCINATION_DETECTION_SEARCH", "5"))  # # 幻觉审查的google搜索条目数
    SEARCH_ENGINE  : str = os.environ.get("SEARCH_ENGINE", "google") #谷歌搜索引擎
    LITERATURE_LIBRARY_SEARCH_ENGINE  : str = os.environ.get("LITERATURE_LIBRARY_SEARCH_ENGINE", "google_scholar") #参考文献谷歌搜索引擎
    WEB_SEARCH_ENGINE: str = os.environ.get("WEB_SEARCH_ENGINE", "google") #参考文献谷歌搜索引擎

    # LLM参数
    LLM_DEFAULT_TEMPERATURE: float = float(os.environ.get("LLM_DEFAULT_TEMPERATURE", "1"))
    LLM_DEFAULT_TOP_K: int = int(os.environ.get("LLM_DEFAULT_TOP_K", "80"))
    LLM_DEFAULT_TOP_P: float = float(os.environ.get("LLM_DEFAULT_TOP_P", "0.8"))
    
    # 报告配置
    REPORT_MIN_WORDS: int = int(os.environ.get("REPORT_MIN_WORDS", "3000"))
    REPORT_MAX_WORDS: int = int(os.environ.get("REPORT_MAX_WORDS", "5000"))
    
    # 日志配置
    LOG_LEVEL: str = os.environ.get("LOG_LEVEL", "INFO")
    LOG_ROOT_DIR: str = os.environ.get("LOG_ROOT_DIR", "./logs")  # Docker宿主机挂载目录
    LOG_DIR: str = os.environ.get("LOG_DIR", "./logs")  # 应用程序内部日志目录
    LOG_RETENTION_DAYS: int = int(os.environ.get("LOG_RETENTION_DAYS", "30"))
    TZ: str = os.environ.get("TZ", "Asia/Shanghai")  # 时区配置
    
    # 文件字数限制
    MAX_FILE_WORD_COUNT: int = 20000
    # 体验用户允许看到的最大文字（报告和大纲都一样）
    TRIAL_USER_MAX_TEXT: int = int(os.environ.get("TRIAL_USER_MAX_TEXT", "10000"))

    # 参考资料上传文件数量限制
    MAX_BATCH_ATTACHMENT_COUNT: int = int(os.environ.get("MAX_BATCH_ATTACHMENT_COUNT", "10"))
    
    # Redis配置
    REDIS_URL: str = os.environ.get("REDIS_URL", "redis://localhost:6379/0")
    REDIS_PASSWORD: str = os.environ.get("REDIS_PASSWORD", "")
    IS_OPEN_SSO: str = os.environ.get("IS_OPEN_SSO", "OFF")
    
    # 使用次数限制配置
    DEFAULT_ORG_USAGE_COUNT: int = int(os.environ.get("DEFAULT_ORG_USAGE_COUNT", "30"))  # 机构默认使用次数
    DEFAULT_USER_USAGE_COUNT: int = int(os.environ.get("DEFAULT_USER_USAGE_COUNT", "5"))  # 用户默认使用次数
    DEFAULT_ORG_MODEL: str = str(os.environ.get("DEFAULT_ORG_MODEL", "anthropic/claude-sonnet-4"))  # 机构默认模型

    # 用来serpAPI请求的代理服务器的配置信息
    PROXY_URL: str = str(os.environ.get("PROXY_URL", "http://*************:3128"))
    PROXY_USERNAME: str = str(os.environ.get("PROXY_USERNAME", ""))
    PROXY_PASSWORD: str = str(os.environ.get("PROXY_PASSWORD", ""))
    # ANTHROPIC的api接口需要max_token必填
    ANTHROPIC_MAX_TOKEN: int = int(os.environ.get("ANTHROPIC_MAX_TOKEN", "50000"))
    # 阿里百炼的api_key用于会议纪要的语音识别
    ALI_LLM_API_KEY:str = os.environ.get("ALI_LLM_API_KEY", "")

    
    # 科大讯飞ASR配置 (新增)
    XFYUN_APP_ID: str = os.environ.get("XFYUN_APP_ID", "")
    XFYUN_API_KEY: str = os.environ.get("XFYUN_API_KEY", "")
    
    # ASR默认提供商 (新增)
    DEFAULT_ASR_PROVIDER: str = os.environ.get("DEFAULT_ASR_PROVIDER", "alibaba")   # 可选值: "alibaba", "xfyun"
    class Config:
        env_file = dotenv_path


settings = Settings()