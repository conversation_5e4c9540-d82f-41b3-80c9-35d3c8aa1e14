from app.core.redis_client import get_redis_client
import json

TOKEN_VALID_TIME = 60 * 24 * 1
class TokenData():
    def __init__(
        self,
        data: dict[str, int]
    ):
        self.input = data["input"]
        self.output = data["output"]
    def __str__(self):
        return json.dumps({
            "input": self.input,
            "output": self.output
        })

async def save_token_data(project_id: str, data: TokenData):
    try:
        redis_key = str(project_id)
        redis = get_redis_client()
        redis_data = await redis.get(redis_key)
        last_data = TokenData(json.loads(redis_data)) if redis_data else TokenData({
            "input": 0,
            "output": 0
        })
        new_data = {
            "input": data.input + last_data.input,
            "output": data.output + last_data.output
        }
        await redis.set(
            name=redis_key,
            value=json.dumps(new_data),
            # 1天
            ex=60 * 24 * 1
        )
        return True
    except Exception as e:
        return False
async def del_token_data(
    project_id: str
):
    try:
        redis_key = str(project_id)
        redis = get_redis_client()
        await redis.delete(redis_key)
        return True
    except Exception as e:
        return False
async def get_token_data(project_id: str):
    try:
        redis_key = str(project_id)
        redis = get_redis_client()
        redis_data = await redis.get(redis_key)
        parse_data = json.loads(redis_data)
        return TokenData(parse_data)
    except Exception as e:
        return TokenData({
            "input": 0,
            "output": 0
        })
        