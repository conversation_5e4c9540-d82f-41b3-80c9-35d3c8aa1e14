import os
import subprocess
from typing import Optional
from app.core.logging import get_logger

logger = get_logger(__name__)

def get_audio_duration(file_path: str) -> Optional[float]:
    """
    获取音频文件的时长（秒）
    
    参数:
    - file_path: 音频文件路径
    
    返回:
    - 音频时长（秒），如果获取失败返回None
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"音频文件不存在: {file_path}")
            return None
            
        # 使用ffprobe获取音频时长
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-show_entries', 'format=duration',
            '-of', 'csv=p=0',
            file_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            duration = float(result.stdout.strip())
            logger.info(f"获取音频时长成功: {file_path} -> {duration}秒")
            return duration
        else:
            logger.error(f"ffprobe执行失败: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        logger.error(f"获取音频时长超时: {file_path}")
        return None
    except Exception as e:
        logger.error(f"获取音频时长失败: {file_path}, 错误: {str(e)}")
        return None 