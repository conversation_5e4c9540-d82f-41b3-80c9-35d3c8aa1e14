"""
文献增强服务 - Google学术搜索和AI标签生成
"""
import json
import re
import aiohttp
from typing import Dict, List, Optional, Any
from app.core.logging import get_logger
from app.services.search_service import perform_serpapi_search
from app.services.llm_service import call_llm
from app.api.repository.user_default_model import get_user_model
from app.models.organization_model_use import UseCase
from app.api.schemas.user import UserResponse
from app.core.config import settings

logger = get_logger(__name__)

async def search_literature_by_jina(
    doi: Optional[str] = None,
    title: Optional[str] = None,
    limit: int = 5
) -> Dict[str, Any]:
    """
    使用JINA API搜索文献信息
    
    Args:
        doi: DOI号
        title: 文献标题
        limit: 搜索结果限制
        
    Returns:
        搜索结果字典，包含年份和其他信息
    """
    try:
        if not settings.JINA_API_KEY:
            logger.error("错误: 未设置JINA API密钥，请在.env文件中配置")
            return {
                "success": False,
                "error": "JINA API密钥未配置",
                "data": None
            }
        
        # 构建搜索查询 - 改进DOI搜索策略
        if doi:
            # 对于DOI，尝试多种搜索策略
            search_queries = [
                f'"{doi}"',  # 直接搜索DOI
                f'site:scholar.google.com "{doi}"',  # Google Scholar
                f'site:pubmed.ncbi.nlm.nih.gov "{doi}"',  # PubMed
            ]
            logger.info(f"使用JINA搜索DOI: {doi}")
        elif title:
            # 清理标题，移除特殊字符
            clean_title = re.sub(r'[^\w\s-]', ' ', title).strip()
            search_queries = [
                f'site:scholar.google.com "{clean_title}"',
                f'"{clean_title}" academic paper',
            ]
            logger.info(f"使用JINA搜索标题: {clean_title}")
        else:
            logger.error("DOI和标题都为空")
            return {
                "success": False,
                "error": "DOI和标题都为空",
                "data": None
            }
        
        # 尝试多个搜索查询直到找到结果
        for search_query in search_queries:
            try:
                # 使用JINA搜索API
                search_url = f"https://s.jina.ai/{search_query}"
                headers = {
                    "Authorization": f"Bearer {settings.JINA_API_KEY}",
                    "Accept": "application/json",
                    "X-Return-Format": "json"
                }
                
                timeout = aiohttp.ClientTimeout(total=30)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(search_url, headers=headers) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            if data and 'data' in data and data['data']:
                                # 解析JINA搜索结果
                                search_results = data.get('data', [])
                                enhanced_results = []
                                
                                for result in search_results[:limit]:
                                    if isinstance(result, dict):
                                        # 提取文献信息
                                        title_text = result.get('title', '')
                                        content = result.get('content', '')
                                        url = result.get('url', '')
                                        
                                        # 尝试从内容中提取年份
                                        year = extract_year_from_text(content + " " + title_text)
                                        
                                        # 尝试提取作者信息
                                        authors = extract_authors_from_text(content)
                                        
                                        enhanced_info = {
                                            "url": url,
                                            "title": title_text,
                                            "authors": authors,
                                            "authors_summary": authors,
                                            "year": year,
                                            "publication": content[:200] + "..." if len(content) > 200 else content,
                                            "snippet": content[:300] + "..." if len(content) > 300 else content,
                                            "cited_by": 0,  # JINA may not provide citation counts
                                            "result_id": url.split('/')[-1] if url else '',
                                            "pdf_links": [],  # May need to extract from content
                                            "versions_total": 0
                                        }
                                        enhanced_results.append(enhanced_info)
                                
                                if enhanced_results:
                                    logger.info(f"JINA搜索成功，找到 {len(enhanced_results)} 个结果")
                                    return {
                                        "success": True,
                                        "error": None,
                                        "data": {
                                            "query": search_query,
                                            "results": enhanced_results,
                                            "total": len(enhanced_results)
                                        }
                                    }
                        else:
                            error_text = await response.text()
                            logger.warning(f"JINA搜索查询 '{search_query}' 失败: {response.status}")
                            continue  # 尝试下一个查询
                            
            except Exception as e:
                logger.warning(f"JINA搜索查询 '{search_query}' 出错: {str(e)}")
                continue  # 尝试下一个查询
        
        # 所有查询都失败
        logger.warning(f"JINA所有搜索策略都无结果")
        return {
            "success": False,
            "error": "未找到相关文献",
            "data": None
        }
        
    except Exception as e:
        logger.error(f"JINA搜索失败: {str(e)}")
        return {
            "success": False,
            "error": f"搜索失败: {str(e)}",
            "data": None
        }

def extract_year_from_text(text: str) -> Optional[str]:
    """从文本中提取年份"""
    year_pattern = r'\b(19|20)\d{2}\b'
    matches = re.findall(year_pattern, text)
    if matches:
        # 返回最可能的年份（通常是最后一个匹配的）
        full_years = [match for match in matches if len(match) == 4]
        if full_years:
            return full_years[-1]
        else:
            # 如果只有两位数年份，补全
            return "20" + matches[-1] if int(matches[-1]) < 50 else "19" + matches[-1]
    return None

def extract_authors_from_text(text: str) -> str:
    """从文本中提取作者信息"""
    # 简单的作者提取逻辑，可以根据需要完善
    lines = text.split('\n')[:3]  # 取前几行
    for line in lines:
        if any(keyword in line.lower() for keyword in ['author', '作者', 'by']):
            return line.strip()[:100]  # 限制长度
    return ""

async def search_literature_by_doi_or_title(
    doi: Optional[str] = None,
    title: Optional[str] = None,
    limit: int = 5
) -> Dict[str, Any]:
    """
    通过DOI号或标题调用JINA API获取文献信息（替代Google学术）

    Args:
        doi: DOI号
        title: 文献标题
        limit: 搜索结果限制

    Returns:
        搜索结果字典，包含年份和其他信息
    """
    # 直接调用JINA搜索功能
    return await search_literature_by_jina(doi=doi, title=title, limit=limit)

def extract_year_from_result(result_data: Dict[str, Any]) -> Optional[str]:
    """
    从搜索结果中提取年份信息

    Args:
        result_data: Google学术搜索结果数据

    Returns:
        提取的年份字符串
    """
    try:
        # 尝试从不同字段提取年份
        year_sources = [
            result_data.get('publication_info', ''),
            result_data.get('snippet', ''),
            result_data.get('title', '')
        ]

        for source in year_sources:
            if source:
                # 使用正则表达式匹配年份 (1900-2099)
                year_match = re.search(r'\b(19|20)\d{2}\b', str(source))
                if year_match:
                    return year_match.group()

        return None
    except Exception as e:
        logger.warning(f"提取年份失败: {str(e)}")
        return None

async def generate_tags_with_llm(
    title: str,
    abstract: Optional[str] = None,
    content: Optional[str] = None,
    current_user: Optional[UserResponse] = None,
    tag_count: int = 5
) -> Dict[str, Any]:
    """
    使用大模型根据文献内容生成标签

    Args:
        title: 文献标题
        abstract: 文献摘要
        content: 文献正文内容（可选）
        current_user: 当前用户
        tag_count: 生成标签数量，默认5个

    Returns:
        生成的标签结果字典
    """
    try:
        # 构建输入文本
        input_texts = []
        if title:
            input_texts.append(f"标题: {title}")
        if abstract:
            input_texts.append(f"摘要: {abstract}")
        if content:
            # 限制内容长度，避免token过多
            truncated_content = content[:2000] if len(content) > 2000 else content
            input_texts.append(f"正文: {truncated_content}")

        if not input_texts:
            return {
                "success": False,
                "error": "没有可用于生成标签的文本内容",
                "data": None
            }

        input_text = "\n\n".join(input_texts)

        # 获取模型配置
        model_config = None
        logger.info(f"====current_user: {current_user.id}")
        if current_user:
            model_config = await get_user_model(current_user, UseCase.PI_MANAGE.value)

        # 如果没有用户模型配置，使用默认配置
        if not model_config:
            logger.info("使用默认模型配置生成标签")
            # 这里可以添加默认模型配置逻辑
            return {
                "success": False,
                "error": "未配置大模型，无法生成标签",
                "data": None
            }

        # 构建生成标签的提示词
        messages = [
            {
                "role": "system",
                "content": """你是一个专业的文献标签生成助手。请根据提供的文献内容，生成准确、相关的学术标签。

要求：
1. 生成恰好5个标签
2. 标签应该涵盖文献的主要研究领域、方法、概念等
3. 标签要简洁明了，通常为1-3个词
4. 优先考虑学术术语和专业概念
5. 标签之间不要重复，应该互补

请以JSON格式返回结果，格式如下：
{
    "tags": ["标签1", "标签2", "标签3", "标签4", "标签5"]
}"""
            },
            {
                "role": "user",
                "content": f"请为以下文献内容生成{tag_count}个学术标签：\n\n{input_text}"
            }
        ]

        # 调用LLM服务
        result = await call_llm(
            messages=messages,
            flag=f"generate_literature_tags",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url,
            max_tokens=200  # 限制token数量
        )

        if not result:
            return {
                "success": False,
                "error": "大模型未返回结果",
                "data": None
            }

        # 解析JSON结果
        try:
            tags_data = json.loads(result.strip())
            tags = tags_data.get('tags', [])

            if not tags or len(tags) != tag_count:
                logger.warning(f"生成的标签数量不正确，期望{tag_count}个，实际{len(tags)}个")
                # 如果数量不对，尝试调整
                if len(tags) > tag_count:
                    tags = tags[:tag_count]
                elif len(tags) < tag_count:
                    # 补充通用标签
                    while len(tags) < tag_count:
                        tags.append(f"研究{len(tags) + 1}")

            logger.info(f"成功生成{len(tags)}个标签: {tags}")
            return {
                "success": True,
                "error": None,
                "data": {
                    "tags": tags,
                    "count": len(tags)
                }
            }

        except json.JSONDecodeError as e:
            logger.error(f"解析标签JSON失败: {str(e)}, 原始结果: {result}")
            # 尝试从文本中提取标签
            tags = extract_tags_from_text(result, tag_count)
            if tags:
                return {
                    "success": True,
                    "error": "JSON解析失败，但成功提取标签",
                    "data": {
                        "tags": tags,
                        "count": len(tags)
                    }
                }
            else:
                return {
                    "success": False,
                    "error": f"解析标签结果失败: {str(e)}",
                    "data": None
                }

    except Exception as e:
        logger.error(f"生成标签失败: {str(e)}")
        return {
            "success": False,
            "error": f"生成标签失败: {str(e)}",
            "data": None
        }

def extract_tags_from_text(text: str, tag_count: int = 5) -> List[str]:
    """
    从文本中提取标签（备用方法）

    Args:
        text: 包含标签的文本
        tag_count: 需要的标签数量

    Returns:
        提取的标签列表
    """
    try:
        # 尝试多种提取方式
        tags = []

        # 方法1: 寻找列表格式
        list_patterns = [
            r'(?:标签|tags?)[:：]\s*\[([^\]]+)\]',
            r'(?:标签|tags?)[:：]\s*([^\n]+)',
            r'\[([^\]]+)\]',
            r'[\d\.]\s*([^\n\d]+?)(?=[\d\.]|$)'
        ]

        for pattern in list_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                # 分割标签
                potential_tags = re.split(r'[,，、\n]', match)
                for tag in potential_tags:
                    clean_tag = tag.strip().strip('"\'')
                    if clean_tag and len(clean_tag) > 1:
                        tags.append(clean_tag)
                        if len(tags) >= tag_count:
                            break
                if len(tags) >= tag_count:
                    break
            if len(tags) >= tag_count:
                break

        return tags[:tag_count] if tags else []

    except Exception as e:
        logger.error(f"从文本提取标签失败: {str(e)}")
        return []