import asyncio
import dashscope
from dashscope.audio.asr import *
from fastapi import WebSocket, Query, WebSocketException, status, WebSocketDisconnect
from app.core.logging import get_logger
from typing import Annotated
from enum import Enum
from app.api.deps import auth_token_data
from app.services.memory_storage import pcmHandler
from app.utils.pcm_handler import Item
from app.core.config import settings
import json
import threading
import time
from abc import ABC, abstractmethod
import websockets
import base64
import hashlib
import hmac
from urllib.parse import urlencode, quote
import ssl

# 配置日志
logger = get_logger(__name__)

class State(str, Enum):
    ONGOING = "ONGOING"
    STOP = "STOP"

class ASRProvider(str, Enum):
    ALIBABA = "alibaba"
    XFYUN = "xfyun"

def ms_to_hms(ms: int) -> str:
    """将毫秒转成00:00:00格式"""
    seconds = ms // 1000
    h = seconds // 3600
    m = (seconds % 3600) // 60
    s = seconds % 60
    return f"{h:02}:{m:02}:{s:02}"

# 抽象ASR回调基类
class BaseASRCallback(ABC):
    def __init__(self, websocket: WebSocket, loop: asyncio.AbstractEventLoop, voice_id: str):
        self.websocket = websocket
        self.loop = loop
        self.voice_id = voice_id

    def send_json(self, data):
        try:
            asyncio.run_coroutine_threadsafe(self.websocket.send_json(data), self.loop)
            logger.info("给客户端发送了asr数据")
        except Exception as e:
            logger.error(f"WebSocket send error: {e}")

    @abstractmethod
    def on_open(self) -> None:
        pass

    @abstractmethod
    def on_close(self) -> None:
        pass

    @abstractmethod
    def on_event(self, result) -> None:
        pass

# 阿里云 DashScope 回调
class AlibabaCallback(BaseASRCallback, TranslationRecognizerCallback):
    def on_open(self) -> None:
        logger.info("阿里云asr服务已经连接")

    def on_close(self) -> None:
        logger.info("阿里云asr服务已经关闭")
        self.send_json({
            "data": None,
            "status": State.STOP.value
        })

    def on_event(self, result: RecognitionResult) -> None:
        request_id = result.get_request_id()
        sentence = result.get_sentence()
        is_sentence_end = RecognitionResult.is_sentence_end(sentence)
        text = sentence.get("text")
        starttime = ms_to_hms(int(sentence.get("begin_time")))
        endtime = ms_to_hms(int(sentence.get("end_time") or 0))
        
        inner_data = {
            "request_id": request_id,
            "is_end": is_sentence_end,
            "text": text,
            "starttime": starttime,
            "endtime": endtime
        }
        data = {
            "data": inner_data,
            "status": State.ONGOING.value
        }
        logger.info(data)
        
        # 保存文本数据到内存
        text_data = Item(
            is_end=is_sentence_end,
            text=sentence.get("text"),  # 使用 sentence.get("text") 而不是 text
            starttime=starttime,
            endtime=endtime
        )
        pcmHandler.add_data(id=self.voice_id, text=text_data)
        self.send_json(data)

# 科大讯飞WebSocket回调
class XfyunCallback(BaseASRCallback):
    def __init__(self, websocket: WebSocket, loop: asyncio.AbstractEventLoop, voice_id: str):
        super().__init__(websocket, loop, voice_id)
        self.final_result = []
        # 记录开始时间，用于计算音频时间戳
        self.start_time = time.time()

    def on_open(self) -> None:
        logger.info("科大讯飞WebSocket asr服务已经连接")

    def on_close(self) -> None:
        logger.info("科大讯飞WebSocket asr服务已经关闭")
        self.send_json({
            "data": None,
            "status": State.STOP.value
        })

    def on_event(self, message: str) -> None:
        """处理科大讯飞WebSocket的识别结果"""
        try:
            # 解析JSON消息
            message_obj = json.loads(message)
            action = message_obj.get("action")
            
            logger.info(f"科大讯飞返回消息: {message_obj}")
            
            if action == "started":
                # 握手成功消息
                logger.info("科大讯飞握手成功")
                
            elif action == "result":
                # 识别结果消息
                data = message_obj.get("data", "")
                if data:
                    # 解析data字段中的JSON字符串
                    try:
                        result_data = json.loads(data)
                        cn = result_data.get("cn", {})  # 中文识别结果
                        st = cn.get("st", {})           # 句子类型
                        rt = st.get("rt", [])           # 识别结果数组
                        
                        text_parts = []
                        # 遍历识别结果，提取文本
                        for rt_item in rt:
                            ws = rt_item.get("ws", [])  # 词数组
                            for ws_item in ws:
                                cw = ws_item.get("cw", [])  # 候选词数组
                                for cw_item in cw:
                                    text_parts.append(cw_item.get("w", ""))  # 词内容
                        
                        text = "".join(text_parts)
                        if text.strip():
                            current_time = time.time()
                            elapsed_time = int((current_time - self.start_time) * 1000)
                            
                            # 判断是否为最终结果
                            # type为0表示完整转写内容，1表示中间结果
                            is_sentence_end = st.get("type") == "0"
                            
                            # 计算时间戳（简化计算）
                            starttime = ms_to_hms(max(0, elapsed_time - len(text) * 100))
                            endtime = ms_to_hms(elapsed_time)
                            
                            inner_data = {
                                "request_id": f"xfyun_ws_{int(current_time)}",
                                "is_end": is_sentence_end,
                                "text": text,
                                "starttime": starttime,
                                "endtime": endtime
                            }
                            
                            data_response = {
                                "data": inner_data,
                                "status": State.ONGOING.value
                            }
                            logger.info(f"识别结果: {data_response}")
                            
                            text_data = Item(
                                is_end=is_sentence_end,
                                text=text,
                                starttime=starttime,
                                endtime=endtime
                            )
                            pcmHandler.add_data(id=self.voice_id, text=text_data)
                            self.send_json(data_response)
                            
                            if is_sentence_end:
                                self.final_result.append(text)
                                
                    except json.JSONDecodeError as e:
                        logger.error(f"解析科大讯飞data字段失败: {e}, data: {data}")
                        
            elif action == "error":
                # 错误消息
                error_msg = message_obj.get("desc", "未知错误")
                logger.error(f"科大讯飞WebSocket识别失败: {error_msg}")
                
        except json.JSONDecodeError as e:
            logger.error(f"解析科大讯飞JSON消息失败: {e}, 原始消息: {message}")
        except Exception as e:
            logger.error(f"处理科大讯飞WebSocket识别结果错误: {e}")

# ASR服务工厂
class ASRServiceFactory:
    @staticmethod
    def create_service(provider: ASRProvider, websocket: WebSocket, loop: asyncio.AbstractEventLoop, voice_id: str):
        if provider == ASRProvider.ALIBABA:
            return AlibabaASRService(websocket, loop, voice_id)
        elif provider == ASRProvider.XFYUN:
            return XfyunASRService(websocket, loop, voice_id)
        else:
            raise ValueError(f"不支持的ASR提供商: {provider}")

# 抽象ASR服务基类
class BaseASRService(ABC):
    def __init__(self, websocket: WebSocket, loop: asyncio.AbstractEventLoop, voice_id: str):
        self.websocket = websocket
        self.loop = loop
        self.voice_id = voice_id

    @abstractmethod
    def start(self):
        pass

    @abstractmethod
    def send_audio_frame(self, data: bytes):
        pass

    @abstractmethod
    def stop(self):
        pass

# 阿里云ASR服务
class AlibabaASRService(BaseASRService):
    def __init__(self, websocket: WebSocket, loop: asyncio.AbstractEventLoop, voice_id: str):
        super().__init__(websocket, loop, voice_id)
        dashscope.api_key = settings.ALI_LLM_API_KEY
        self.callback = AlibabaCallback(websocket, loop, voice_id)
        self.recognition = None

    def start(self):
        self.recognition = Recognition(
            model="paraformer-realtime-8k-v2",
            format="pcm",
            sample_rate=16000,
            semantic_punctuation_enabled=True,
            callback=self.callback,
            punctuation_prediction_enabled=True
        )
        self.recognition.start()

    def send_audio_frame(self, data: bytes):
        if self.recognition:
            self.recognition.send_audio_frame(data)

    def stop(self):
        if self.recognition:
            self.recognition.stop()
            # 注意：这里不要清理数据，让用户主动结束录音时清理

# 科大讯飞WebSocket服务
class XfyunASRService(BaseASRService):
    def __init__(self, websocket: WebSocket, loop: asyncio.AbstractEventLoop, voice_id: str):
        super().__init__(websocket, loop, voice_id)
        self.callback = XfyunCallback(websocket, loop, voice_id)
        self.xfyun_ws = None
        self.is_running = False
        self.audio_buffer = b""
        # 音频缓冲区大小：每40ms发送1280字节
        # 计算方式：16000采样率 × 16bit × 0.04秒 ÷ 8bit = 1280字节
        # 这是科大讯飞推荐的音频数据发送频率
        self.buffer_size = 1280
        self.task = None
        self.connection_closed = False
        # 结束标记：告诉科大讯飞服务音频数据发送完毕
        self.end_tag = b'{"end": true}'

    def start(self):
        """启动科大讯飞WebSocket服务"""
        self.is_running = True
        # 创建异步任务处理WebSocket连接和音频数据
        self.task = asyncio.create_task(self._connect_and_process())

    async def _connect_and_process(self):
        """连接科大讯飞WebSocket并处理音频数据"""
        try:
            # 构建WebSocket连接URL
            url = self._build_websocket_url()
            logger.info(f"连接科大讯飞WebSocket: {url}")
            
            # 连接WebSocket
            async with websockets.connect(url) as websocket:
                self.xfyun_ws = websocket
                self.callback.on_open()
                
                # 启动音频处理任务
                audio_task = asyncio.create_task(self._process_audio_stream())
                
                # 启动消息接收任务
                message_task = asyncio.create_task(self._receive_messages())
                
                # 等待任务完成
                await asyncio.gather(audio_task, message_task, return_exceptions=True)
                
        except websockets.exceptions.ConnectionClosed as e:
            logger.info(f"科大讯飞WebSocket连接已关闭: {e}")
            self.connection_closed = True
        except Exception as e:
            logger.error(f"科大讯飞WebSocket连接失败: {e}")
        finally:
            self.callback.on_close()

    async def _process_audio_stream(self):
        """处理音频流"""
        while self.is_running and self.xfyun_ws and not self.connection_closed:
            if len(self.audio_buffer) >= self.buffer_size:
                chunk = self.audio_buffer[:self.buffer_size]
                self.audio_buffer = self.audio_buffer[self.buffer_size:]
                await self._send_audio_to_xfyun(chunk)
            else:
                # 等待更多音频数据，避免CPU占用过高
                await asyncio.sleep(0.01)  # 10ms等待间隔

    async def _send_audio_to_xfyun(self, audio_data: bytes):
        """发送音频数据到科大讯飞WebSocket"""
        try:
            if self.xfyun_ws and not self.connection_closed:
                # 直接发送二进制音频数据
                # 科大讯飞要求：采样率16k、位深16bit、单声道PCM格式
                await self.xfyun_ws.send(audio_data)
                logger.debug(f"发送音频数据到科大讯飞: {len(audio_data)} bytes")
                
        except websockets.exceptions.ConnectionClosed:
            logger.info("科大讯飞WebSocket连接已关闭，停止发送音频")
            self.connection_closed = True
        except Exception as e:
            logger.error(f"发送音频到科大讯飞WebSocket失败: {e}")

    async def _receive_messages(self):
        """接收科大讯飞WebSocket消息"""
        try:
            while self.is_running and self.xfyun_ws and not self.connection_closed:
                message = await self.xfyun_ws.recv()
                logger.debug(f"收到科大讯飞消息: {message}")
                self.callback.on_event(message)
        except websockets.exceptions.ConnectionClosed:
            logger.info("科大讯飞WebSocket连接已关闭，停止接收消息")
            self.connection_closed = True
        except Exception as e:
            logger.error(f"接收科大讯飞WebSocket消息失败: {e}")

    def _build_websocket_url(self):
        """构建科大讯飞WebSocket连接URL"""
        # 生成时间戳：当前Unix时间戳（秒）
        ts = str(int(time.time()))
        
        # 生成签名 - 按照科大讯飞官方示例的方式
        # 1. 将app_id和时间戳拼接
        tt = (settings.XFYUN_APP_ID + ts).encode('utf-8')
        
        # 2. 对拼接字符串进行MD5加密
        md5 = hashlib.md5()
        md5.update(tt)
        baseString = md5.hexdigest()
        baseString = bytes(baseString, encoding='utf-8')
        
        # 3. 使用API Key对MD5结果进行HMAC-SHA1加密
        apiKey = settings.XFYUN_API_KEY.encode('utf-8')
        signa = hmac.new(apiKey, baseString, hashlib.sha1).digest()
        
        # 4. 对HMAC结果进行Base64编码
        signa = base64.b64encode(signa)
        signa = str(signa, 'utf-8')
        
        # 构建WebSocket连接URL
        # 科大讯飞实时语音转写WebSocket地址
        url = f"ws://rtasr.xfyun.cn/v1/ws?appid={settings.XFYUN_APP_ID}&ts={ts}&signa={quote(signa)}"
        
        logger.info(f"科大讯飞WebSocket URL参数: appid={settings.XFYUN_APP_ID}, ts={ts}, signa={signa[:10]}...")
        
        return url

    def send_audio_frame(self, data: bytes):
        """添加音频帧到缓冲区"""
        if self.is_running and not self.connection_closed:
            self.audio_buffer += data

    def stop(self):
        """停止服务"""
        self.is_running = False
        self.connection_closed = True
        
        # 发送结束标记：告诉科大讯飞服务音频数据发送完毕
        # 这是必须的，否则科大讯飞会一直等待更多音频数据
        if self.xfyun_ws and not self.connection_closed:
            try:
                asyncio.create_task(self.xfyun_ws.send(self.end_tag))
                logger.info("发送科大讯飞结束标记")
            except Exception as e:
                logger.error(f"发送结束标记失败: {e}")
        
        if self.task:
            self.task.cancel()
        if self.xfyun_ws:
            asyncio.create_task(self.xfyun_ws.close())

# 获取默认ASR提供商
def get_default_asr_provider() -> ASRProvider:
    """从配置中获取默认的ASR提供商"""
    provider_str = settings.DEFAULT_ASR_PROVIDER.lower()
    
    if provider_str == "alibaba":
        return ASRProvider.ALIBABA
    elif provider_str == "xfyun":
        return ASRProvider.XFYUN
    else:
        # 默认使用阿里
        logger.warning(f"未知的ASR提供商配置: {provider_str}，使用默认的科大讯飞")
        return ASRProvider.ALIBABA

# WebSocket认证
async def ws_auth_token(
    token: Annotated[str, Query()],
    voice_id: Annotated[str, Query()]
):
    try:
        await auth_token_data(token=token)
    except Exception as e:
        logger.error(f"ws验证token错误：{str(e)}")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="缺少有效的令牌")
    if not voice_id:
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="缺少录音记录ID")
    
    return voice_id

# WebSocket端点
async def websocket_endpoint(
    websocket: WebSocket,
    voice_id: str
):
    await websocket.accept()
    loop = asyncio.get_running_loop()
    
    # 使用配置的默认ASR提供商
    provider = get_default_asr_provider()
    logger.info(f"使用默认ASR提供商: {provider.value}")
    
    # 根据提供商创建对应的ASR服务
    asr_service = ASRServiceFactory.create_service(provider, websocket, loop, voice_id)
    
    try:
        # 启动ASR服务
        asr_service.start()
        logger.info(f"启动{provider.value}ASR服务成功")
        
        while True:
            # 前端推送的二进制 PCM 数据
            data = await websocket.receive_bytes()
            
            # 保存PCM数据到内存（用于后续文件生成）
            pcmHandler.add_data(id=voice_id, pcm_data=data)
            logger.debug(f"保存PCM数据到录音ID {voice_id}，数据长度：{len(data)} bytes")
            
            # 发送音频帧给ASR服务
            asr_service.send_audio_frame(data)
            
    except WebSocketDisconnect:
        logger.info("WebSocket 已关闭")
        # 注意：这里不要清理数据，因为用户可能还要结束录音
    except Exception as e:
        logger.error(f"WebSocket错误: {str(e)}")
    finally:
        # 停止ASR服务
        asr_service.stop()
        logger.info(f"停止{provider.value}ASR服务")
        # 注意：这里也不要清理数据，让用户主动结束录音时清理