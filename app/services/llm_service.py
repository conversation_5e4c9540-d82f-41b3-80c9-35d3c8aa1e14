import json
import aiohttp
import asyncio
import traceback
from typing import Dict, List, Optional, Any
from app.api.schemas.user import UserResponse
from app.core.logging import get_logger
from app.utils.llm_service import (
    is_anthropic_provider,
    set_auth_data,
    set_anthropic_message,
    get_proxy
)
from app.services.token_calculate import save_token_data, TokenData
# 获取logger实例
logger = get_logger(__name__)


async def call_llm(
    messages: List[Dict[str, str]], 
    flag: str = "default", # 调用的目的，用于区分不同的调用场景
    model: str = "", 
    stream: bool = False,
    apiKey: str = "",
    apiUrl: str = "",
    max_tokens: Optional[int] = None,
    user: Optional[UserResponse] = None,
    related_id: Optional[str] = None
) -> Optional[str]:
    """
    调用OpenRouter LLM API
    
    Args:
        messages: 聊天消息列表
        model: 模型名称
        stream: 是否流式响应
    
    Returns:
        模型响应文本，如果失败则返回None
    """
    logger.info(f"调用LLM，模型: {model}, 目的: {flag}")
    if not apiKey:
        error = "未设置API_KEY环境变量"
        logger.error(error)
        raise ValueError(error)
    
    payload = {
        "model": model,
        "messages": messages,
        "stream": stream
    }  
    query = {
        "url": apiUrl
    }
    # 准备简单的测试请求
    headers = {
        "Content-Type": "application/json",
    }
    if max_tokens:
        payload["max_tokens"] = max_tokens 
    is_anthropic = is_anthropic_provider(api_url=apiUrl)
    headers = headers|set_auth_data(is_anthropic=is_anthropic, apiKey=apiKey)
    if is_anthropic:
        # anthropic需要配置代理才可以访问
        proxy_data = get_proxy()
        query = query | proxy_data
        payload = payload|set_anthropic_message(messages=messages)
       
    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"LLM请求，超时设置: {timeout.total}秒，消息数量: {len(messages)}")
    query["timeout"] = timeout
    query["json"] = payload
    query["headers"] = headers
    try:
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送请求: {apiUrl}")
            async with session.post(
                **query
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"模型名称:{model},apiUrl: {apiUrl} 错误: 状态码 {resp.status}")
                    logger.error(f"错误详情: {error_text}")
                    return None
                # token消耗
                token_consumed = {
                    "input": "",
                    "output": ""
                }

                # 处理非流式响应
                if not stream:
                    result = await resp.json()
                    logger.info(f"完整响应: {result}")
                    
                    try:
                        if 'usage' in result and result["usage"]:
                            token_consumed["input"] = result["usage"]["prompt_tokens"]
                            token_consumed["output"] = result["usage"]["completion_tokens"]
                            if related_id:
                                await save_token_data(related_id, TokenData(token_consumed))
                            logger.info(f"调用call_llm的关联ID:{related_id}的token消耗：{str(token_consumed)}")

                        # 检查OpenRouter返回的标准响应格式
                        if 'choices' in result and len(result['choices']) > 0:
                            if 'message' in result['choices'][0] and 'content' in result['choices'][0]['message']:
                                content = result['choices'][0]['message']['content']
                                logger.info(f"LLM响应成功(标准格式)，内容长度: {len(content)}")
                                return content
                        
                        # 检查替代响应格式: Claude格式
                        if 'content' in result:
                            content = result['content'][0].get("text", "")
                            logger.info(f"LLM响应成功(Claude格式)，内容长度: {len(content)}")
                            return content
                            
                        # 检查替代响应格式: Gemini格式
                        if 'candidates' in result and len(result['candidates']) > 0:
                            if 'content' in result['candidates'][0] and 'parts' in result['candidates'][0]['content']:
                                text_parts = [part.get('text', '') for part in result['candidates'][0]['content']['parts'] if 'text' in part]
                                content = ''.join(text_parts)
                                logger.info(f"LLM响应成功(Gemini格式)，内容长度: {len(content)}")
                                return content
                        
                        # 检查替代响应格式: 纯文本
                        if isinstance(result, str):
                            logger.info(f"LLM响应成功(纯文本格式)，内容长度: {len(result)}")
                            return result
                            
                        # 如果都无法解析，尝试将整个响应转换为字符串
                        logger.warning(f"无法解析的响应格式，尝试转换为字符串: {result}")
                        
                        # 检查是否为Google AI的位置限制错误
                        if isinstance(result, dict) and 'error' in result:
                            error_data = result['error']
                            # 检查是否包含provider_name和Google位置限制信息
                            if 'metadata' in error_data and 'provider_name' in error_data['metadata']:
                                provider = error_data['metadata'].get('provider_name', '')
                                raw_error = error_data['metadata'].get('raw', '')
                                
                                if provider == 'Google AI Studio' and 'User location is not supported' in raw_error:
                                    logger.error(f"Google AI Studio 位置限制错误: {raw_error}")
                                    return "由于地理位置限制，无法访问Google AI服务。正在尝试其他方法解析内容..."
                            raise ValueError(result)
                        return json.dumps(result)
                            
                    except (KeyError, IndexError) as e:
                        logger.error(f"模型名称:{model},apiUrl: {apiUrl} 响应结构异常: {str(e)}")
                        logger.debug(f"完整响应: {result}")
                        
                        # 尝试找到任何可能的文本内容
                        if isinstance(result, dict):
                            # 递归搜索字典中的任何文本内容
                            def extract_text(obj):
                                if isinstance(obj, str):
                                    return obj
                                elif isinstance(obj, dict):
                                    for k, v in obj.items():
                                        if k in ['content', 'text', 'message', 'response']:
                                            text = extract_text(v)
                                            if text:
                                                return text
                                    for v in obj.values():
                                        text = extract_text(v)
                                        if text:
                                            return text
                                elif isinstance(obj, list):
                                    for item in obj:
                                        text = extract_text(item)
                                        if text:
                                            return text
                                return None
                            
                            extracted_text = extract_text(result)
                            if extracted_text:
                                logger.info(f"从异常响应中提取到文本，长度: {len(extracted_text)}")
                                return extracted_text
                        
                        # 如果实在无法提取，返回整个响应的字符串形式
                        logger.warning("无法从响应中提取文本，返回整个响应的字符串形式")
                        return str(result)
                
                # 流式响应，返回完整内容
                else:
                    logger.debug("开始接收流式LLM响应")
                    full_response = ""
                    chunk_count = 0
                    async for line in resp.content:
                        line = line.decode('utf-8').strip()
                        if not line:
                            continue
                        
                        # 跳过 "data: " 前缀
                        if line.startswith("data: "):
                            line = line[6:]
                        
                        # 处理流结束标记
                        if line == "[DONE]":
                            logger.debug("流式响应结束")
                            break
                        
                        try:
                            # 解析 JSON 数据
                            data = json.loads(line)
                            logger.debug(f"流式响应数据: {data}")
                            
                            # 提取内容增量 - 标准格式
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    chunk = delta['content']
                                    full_response += chunk
                                    chunk_count += 1
                            # Claude格式
                            elif 'content' in data:
                                chunk = data['content']
                                full_response += chunk
                                chunk_count += 1
                            # Gemini格式
                            elif 'candidates' in data and len(data['candidates']) > 0:
                                if 'content' in data['candidates'][0] and 'parts' in data['candidates'][0]['content']:
                                    text_parts = [part.get('text', '') for part in data['candidates'][0]['content']['parts'] if 'text' in part]
                                    chunk = ''.join(text_parts)
                                    full_response += chunk
                                    chunk_count += 1
                            # 任何可能包含文本的字段
                            else:
                                for key in ['text', 'message', 'response', 'output']:
                                    if key in data:
                                        chunk = data[key] if isinstance(data[key], str) else str(data[key])
                                        full_response += chunk
                                        chunk_count += 1
                                        break
                            if 'usage' in data and data["usage"]:
                                token_consumed["input"] = data["usage"]["prompt_tokens"]
                                token_consumed["output"] = data["usage"]["completion_tokens"]
                                if related_id:
                                    await save_token_data(related_id, TokenData(token_consumed))
                                logger.info(f"调用call_llm的关联ID:{related_id}的token消耗：{str(token_consumed)}")
                        except json.JSONDecodeError:
                            logger.error(f"无法解析 JSON: {line}")
                        except Exception as e:
                            logger.error(f"处理流式响应时出错: {str(e)}")
                    
                    logger.info(f"流式响应完成，接收了 {chunk_count} 个数据块，总长度: {len(full_response)}")
                    return full_response
                    
    except asyncio.TimeoutError:
        logger.error(f"模型名称:{model},apiUrl: {apiUrl}  请求超时 ({timeout.total} 秒)")
        return None
    except aiohttp.ClientError as e:
        logger.error(f"aiohttp 客户端错误: {e.__class__.__name__}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"调用模型名称:{model},apiUrl: {apiUrl} 时发生错误: {e.__class__.__name__}: {str(e)}")
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        return None

async def call_llm_full_response(
    messages: List[Dict[str, str]],
    flag: str = "default", # 调用的目的，用于区分不同的调用场景
    model: str = "",
    stream: bool = False,
    apiKey: str = "",
    apiUrl: str = "",
    max_tokens: Optional[int] = None,
    user: Optional[UserResponse] = None,
    related_id: Optional[str] = None
) -> Optional[Dict]:
    """
    调用OpenRouter LLM API，返回完整的响应对象

    Args:
        messages: 聊天消息列表
        model: 模型名称
        stream: 是否流式响应

    Returns:
        完整的响应对象，如果失败则返回None
    """
    logger.info(f"调用LLM(完整响应)，模型: {model}, 目的: {flag}")
    if not apiKey:
        error = "未设置API_KEY环境变量"
        logger.error(error)
        raise ValueError(error)

    payload = {
        "model": model,
        "messages": messages,
        "stream": stream
    }
    query = {
        "url": apiUrl
    }
    # 准备简单的测试请求
    headers = {
        "Content-Type": "application/json",
    }
    if max_tokens:
        payload["max_tokens"] = max_tokens
    is_anthropic = is_anthropic_provider(api_url=apiUrl)
    headers = headers|set_auth_data(is_anthropic=is_anthropic, apiKey=apiKey)
    if is_anthropic:
        # anthropic需要配置代理才可以访问
        proxy_data = get_proxy()
        query = query | proxy_data
        payload = payload|set_anthropic_message(messages=messages)

    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"LLM请求(完整响应)，超时设置: {timeout.total}秒，消息数量: {len(messages)}")
    query["timeout"] = timeout
    query["json"] = payload
    query["headers"] = headers
    try:
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送请求(完整响应): {apiUrl}")
            async with session.post(
                **query
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"模型名称:{model},apiUrl: {apiUrl} 错误: 状态码 {resp.status}")
                    logger.error(f"错误详情: {error_text}")
                    return None

                # 处理非流式响应
                if not stream:
                    result = await resp.json()
                    logger.info(f"完整响应: {result}")

                    try:
                        # token消耗记录
                        if 'usage' in result and result["usage"]:
                            token_consumed = {
                                "input": result["usage"]["prompt_tokens"],
                                "output": result["usage"]["completion_tokens"]
                            }
                            if related_id:
                                await save_token_data(related_id, TokenData(token_consumed))
                            logger.info(f"调用call_llm_full_response的关联ID:{related_id}的token消耗：{str(token_consumed)}")

                        # 直接返回原始响应结果
                        logger.info(f"LLM响应成功(完整响应)，返回原始结果")
                        return result

                    except (KeyError, IndexError) as e:
                        logger.error(f"模型名称:{model},apiUrl: {apiUrl} 响应结构异常: {str(e)}")
                        logger.debug(f"完整响应: {result}")
                        return None

    except asyncio.TimeoutError:
        logger.error(f"模型名称:{model},apiUrl: {apiUrl}  请求超时")
        return None
    except aiohttp.ClientError as e:
        logger.error(f"aiohttp 客户端错误: {e.__class__.__name__}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"调用模型名称:{model},apiUrl: {apiUrl} 时发生错误: {e.__class__.__name__}: {str(e)}")
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        return None

async def call_llm_with_format_json(
    messages: List[Dict[str, str]],
    api_key: str,
    api_url: str,
    model: str,
    response_format: Optional[Dict[str, Any]] = None,
    timeout: int = 300,
    # 调用场景的文字描述
    flag: str = "",
    user: Optional[UserResponse] = None
) -> str:
    """
    调用大模型并支持自定义 response_format，专门用于处理需要返回JSON格式的场景
    
    该方法会：
    1. 支持自定义 response_format 参数，用于指定返回格式
    2. 自动清洗返回内容中的JSON相关格式，包括：
       - 移除 markdown 代码块格式 (```json ... ```)
       - 移除 LaTeX 格式 (\boxed{ ... })
       - 移除普通 markdown 代码块 (``` ... ```)
    3. 返回清洗后的纯JSON字符串
    
    Args:
        messages: 消息列表
        api_key: API密钥
        api_url: API接口地址
        model: 模型名称
        response_format: 响应格式配置（可选），用于指定返回格式
        timeout: 超时时间（秒）
        
    Returns:
        str: 清洗后的JSON字符串，如果调用失败则返回空字符串
    """
    try:
        logger.info(f"开始调用大模型，model: {model}")
        
        query = {
            "url": api_url
        }
        # 构建请求参数
        headers = {
            "X-Title": "Hi-IdeaGen",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": messages,
            "stream": False
        }
        is_anthropic = is_anthropic_provider(api_url=api_url)
        headers = headers|set_auth_data(is_anthropic=is_anthropic, apiKey=api_key)
        if is_anthropic:
            # anthropic需要配置代理才可以访问
            proxy_data = get_proxy()
            query = query | proxy_data
            payload = payload|set_anthropic_message(messages=messages)
       
        # 如果提供了 response_format，添加到 payload 中
        # response_format这个参数anthropic不允许传
        if not is_anthropic and response_format:
            payload["response_format"] = response_format
        logger.info(f"请求参数: {json.dumps(payload, ensure_ascii=False)}")
        logger.info(f"请求参数: {payload}")
        # 设置超时
        timeout_obj = aiohttp.ClientTimeout(total=timeout)
        query["timeout"] = timeout
        query["json"] = payload
        query["headers"] = headers
        # 发送请求
        async with aiohttp.ClientSession() as session:
            async with session.post(
                **query
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"调用大模型失败: {error_text}")
                    return ""
                
                result = await resp.json()
                content = None

                if is_anthropic:
                    content = result['content'][0].get("text", "")
                # 提取内容
                elif 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0].get('message', {}).get('content', '')
                    # 检查替代响应格式: Claude格式
                if content:
                    # 处理不同模型返回的格式
                    # 1. 处理 markdown 代码块格式 (```json ... ```)
                    if '```json' in content:
                        logger.info("'```json 处理")
                        content = content.split('```json')[1].split('```')[0].strip()
                    # 2. 处理 LaTeX 格式 (\boxed{ ... })
                    elif '\\boxed{' in content:
                        logger.info("'\\boxed 处理")
                        content = content.split('\\boxed{')[1]
                        last_brace = content.rfind('}')
                        if last_brace != -1:
                            content = content[:last_brace].strip()
                    # 3. 处理普通 markdown 代码块 (``` ... ```)
                    elif '```' in content:
                        logger.info("'```' 处理")
                        content = content.split('```')[1].split('```')[0].strip()
                    
                    return content
            
                logger.error("调用大模型失败")
                return ""
                
    except Exception as e:
        logger.error(f"调用大模型时发生错误: {str(e)}")
        return "" 