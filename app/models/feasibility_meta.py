import uuid
from tortoise import fields
from tortoise.models import Model


class FeasibilityMeta(Model):
    """可行性报告的配置项"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    # 为小数
    expect_return_rate = fields.FloatField(null=True, description="预期内部收益率")
    # 投资回收期（单位：年）
    payback_period = fields.FloatField(null=True, description="投资回收期")
    analysis_method = fields.CharField(null=True, max_length=50, description="分析方法")
    # 总投资额（单位：元）
    investment_amount = fields.DecimalField(null=True, max_digits=15, decimal_places=2, description="总投资额")
    # 风险评估
    risk_assessment = fields.TextField(null=True, description="风险评估")
    
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否已删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "docgen_feasibility_meta"
        description = "可行性报告的个性化配置内容"