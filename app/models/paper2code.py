import uuid
from enum import Enum
from tortoise.models import Model
from tortoise import fields


class Paper2CodeStatus(str, Enum):
    """Paper2Code处理状态"""
    PENDING = "pending"              # 待处理
    UPLOADING = "uploading"          # 上传中
    UPLOADED = "uploaded"            # 已上传
    PARSING = "parsing"              # PDF解析中
    PARSED = "parsed"               # 解析完成
    PLANNING = "planning"           # 规划中
    PLANNED = "planned"             # 规划完成
    ANALYZING = "analyzing"         # 分析中
    ANALYZED = "analyzed"           # 分析完成
    CODING = "coding"               # 编码中
    COMPLETED = "completed"         # 完成
    FAILED = "failed"               # 失败
    CANCELLED = "cancelled"         # 已取消


class Paper2CodeJob(Model):
    """Paper2Code任务模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)

    # 基本信息
    job_name = fields.CharField(max_length=255, description="任务名称")
    description = fields.TextField(null=True, description="任务描述")

    # 状态管理
    status = fields.CharEnumField(
        Paper2CodeStatus,
        default=Paper2CodeStatus.PENDING,
        description="任务状态"
    )

    # 原始文件信息
    original_file_name = fields.CharField(max_length=255, description="原始文件名")
    original_file_path = fields.CharField(max_length=500, description="原始文件存储路径")
    original_file_size = fields.BigIntField(description="原始文件大小(bytes)")
    file_type = fields.CharField(max_length=10, description="文件类型(pdf/docx)")
    mime_type = fields.CharField(max_length=100, description="MIME类型")

    # 处理过程文件路径
    json_file_path = fields.CharField(max_length=500, null=True, description="PDF解析后的JSON文件路径")
    output_directory = fields.CharField(max_length=500, null=True, description="输出目录路径")
    code_repository_path = fields.CharField(max_length=500, null=True, description="生成的代码仓库路径")
    zip_file_path = fields.CharField(max_length=500, null=True, description="打包后的ZIP文件路径")

    # 处理配置
    config_data = fields.JSONField(default=dict, description="处理配置数据")

    # 处理结果
    planning_result = fields.JSONField(null=True, description="规划阶段结果")
    analyzing_result = fields.JSONField(null=True, description="分析阶段结果")
    coding_result = fields.JSONField(null=True, description="编码阶段结果")

    # 错误信息
    error_message = fields.TextField(null=True, description="错误信息")
    error_details = fields.JSONField(null=True, description="详细错误信息")

    # 进度信息
    progress_percentage = fields.IntField(default=0, description="处理进度百分比")
    current_stage = fields.CharField(max_length=50, null=True, description="当前处理阶段")

    # 统计信息
    total_files_generated = fields.IntField(default=0, description="生成的文件总数")
    processing_time_seconds = fields.IntField(null=True, description="处理耗时(秒)")

    # 时间戳
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    started_at = fields.DatetimeField(null=True, description="开始处理时间")
    completed_at = fields.DatetimeField(null=True, description="完成时间")

    # 软删除
    is_deleted = fields.BooleanField(default=False, description="是否已删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    # 外键关联
    created_by = fields.ForeignKeyField(
        "models.User",
        related_name="paper2code_jobs",
        description="创建者"
    )

    organization = fields.ForeignKeyField(
        "models.Organizations",
        related_name="paper2code_jobs",
        null=True,
        description="所属机构"
    )

    class Meta:
        table = "paper2code_jobs"
        description = "Paper2Code任务"

    def __str__(self):
        return f"Paper2Code Job: {self.job_name} ({self.status})"


class Paper2CodeFile(Model):
    """Paper2Code生成的文件记录"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)

    # 文件信息
    file_name = fields.CharField(max_length=255, description="文件名")
    file_path = fields.CharField(max_length=500, description="文件路径")
    relative_path = fields.CharField(max_length=500, description="相对路径")
    file_size = fields.BigIntField(description="文件大小(bytes)")
    file_type = fields.CharField(max_length=50, description="文件类型")

    # 文件内容信息
    content_preview = fields.TextField(null=True, description="内容预览(前500字符)")
    line_count = fields.IntField(null=True, description="代码行数")

    # 生成阶段
    generation_stage = fields.CharField(max_length=50, description="生成阶段(planning/analyzing/coding)")

    # 时间戳
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")

    # 外键关联
    job = fields.ForeignKeyField(
        "models.Paper2CodeJob",
        related_name="generated_files",
        description="所属任务"
    )

    class Meta:
        table = "paper2code_files"
        description = "Paper2Code生成的文件"

    def __str__(self):
        return f"Generated File: {self.file_name}"


class Paper2CodeLog(Model):
    """Paper2Code处理日志"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)

    # 日志信息
    stage = fields.CharField(max_length=50, description="处理阶段")
    level = fields.CharField(max_length=10, description="日志级别(INFO/WARN/ERROR)")
    message = fields.TextField(description="日志消息")
    details = fields.JSONField(null=True, description="详细信息")

    # 时间戳
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")

    # 外键关联
    job = fields.ForeignKeyField(
        "models.Paper2CodeJob",
        related_name="processing_logs",
        description="所属任务"
    )

    class Meta:
        table = "paper2code_logs"
        description = "Paper2Code处理日志"

    def __str__(self):
        return f"Log: {self.stage} - {self.level}"