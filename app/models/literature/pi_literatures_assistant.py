"""
文献AI助手数据模型
"""

from tortoise.models import Model
from tortoise import fields


class PiLiteraturesAssistant(Model):
    """
    文献AI助手关联表
    """

    # 主键
    id = fields.UUIDField(pk=True)
    # 关联文献
    literature = fields.ForeignKeyField(
        "models.PiLiteratures",
        related_name="ai_assistants",
        on_delete=fields.CASCADE,
        description="关联文献"
    )

    # 关联用户
    user = fields.ForeignKeyField(
        "models.User",
        related_name="literature_assistants",
        on_delete=fields.CASCADE,
        description="创建用户"
    )

    # AI生成内容
    ai_summary = fields.TextField(null=True, description="AI简介")
    follow_up_questions = fields.TextField(null=True, description="追问列表")
    analysis_content = fields.TextField(null=True, description="分析内容")
    key_points = fields.TextField(null=True, description="重点列表")
    outline = fields.TextField(null=True, description="大纲结构")

   
    # 创建信息
    created_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "pi_literatures_assistant"
        ordering = ["-created_time"]
        indexes = [
            ["literature_id"],
            ["user_id"],
            ["created_time"]
        ]

    def __str__(self):
        return f"文献助手-{self.literature.name[:30]}..."