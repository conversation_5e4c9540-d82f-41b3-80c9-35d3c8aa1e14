"""
文献管理数据模型
"""

from tortoise.models import Model
from tortoise import fields
from enum import Enum
import uuid
from typing import Optional


class FileParseStatus(str, Enum):
    """文件解析状态"""
    PARSING = "parsing"      # 解析中
    COMPLETED = "completed"  # 已完成
    ERROR = "error"         # 解析错误




class LiteratureSource(str, Enum):
    """文献来源"""
    PUBMED = "pubmed"          # PubMed
    ARXIV = "arxiv"            # arXiv
    IEEE = "ieee"              # IEEE Xplore
    SPRINGER = "springer"       # Springer
    ELSEVIER = "elsevier"      # Elsevier/ScienceDirect
    WILEY = "wiley"            # Wiley
    NATURE = "nature"          # Nature系列
    SCIENCE = "science"        # Science系列
    MANUAL_UPLOAD = "manual_upload"  # 手动上传
    WEB_CRAWLING = "web_crawling"    # 网络爬取
    OTHER = "other"            # 其他来源


class PiLiteratures(Model):
    """
    文献主表模型
    """

    # 主键
    id = fields.UUIDField(pk=True)
    # 基础信息
    name = fields.CharField(max_length=500, description="文献标题")
    doi = fields.CharField(max_length=200, null=True, description="DOI")
    authors = fields.JSONField(default=list, description="作者列表")
    keywords = fields.JSONField(default=list, description="关键词列表")
    abstract = fields.TextField(null=True, description="摘要")
    journal = fields.CharField(max_length=300, null=True, description="期刊名称")
    reference_count = fields.IntField(default=0, description="参考文献数量")

    # 扩展信息
    literature_date = fields.CharField(max_length=50, null=True, description="文献发表日期")
    literature_source = fields.CharEnumField(LiteratureSource, default=LiteratureSource.MANUAL_UPLOAD, description="文献来源")
    impact_factor = fields.DecimalField(max_digits=8, decimal_places=3, null=True, description="影响因子")

    # 文件处理
    file_parse_status = fields.CharEnumField(FileParseStatus, default=FileParseStatus.PARSING, description="文件解析状态")
    parse_error_message = fields.TextField(null=True, description="解析错误信息")
    original_file_path = fields.CharField(max_length=500, null=True, description="原始文件路径")
    parsed_content = fields.CharField(max_length=500, null=True, description="解析内容文件路径")
    
    # 关联上传文件记录
    upload_file = fields.ForeignKeyField(
        'models.UploadFile',
        related_name='pi_literatures',
        description="关联文件上传表",
        null=True,
        to_field='id'  # 明确指定目标字段
    )
    # 关联用户
    user = fields.ForeignKeyField(
        "models.User",
        related_name="pi_literatures",
        on_delete=fields.CASCADE,
        description="创建用户"
    )
    # 关联机构
    organization = fields.ForeignKeyField(
        "models.Organizations",
        related_name="pi_literatures",
        on_delete=fields.SET_NULL,
        null=True,
        description="所属机构"
    )

    # 软删除
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    deleted_time = fields.DatetimeField(null=True, description="删除时间")

    # 时间戳
    created_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_time = fields.DatetimeField(auto_now=True, description="更新时间")

    # 统计字段
    view_count = fields.IntField(default=0, description="查看次数")
    download_count = fields.IntField(default=0, description="下载次数")
    ai_analysis_count = fields.IntField(default=0, description="AI分析次数")



    class Meta:
        table = "pi_literatures"
        ordering = ["-created_time"]
        indexes = [
            ["doi"],
            ["user"],
            ["organization"],
            ["file_parse_status"],
            ["is_deleted"],
            ["created_time"],
            ["literature_date"],
            ["upload_file"]
        ]

    def __str__(self):
        return f"{self.name[:50]}..." if len(self.name) > 50 else self.name



