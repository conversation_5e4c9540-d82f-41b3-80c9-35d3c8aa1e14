"""
PaperToX Agent 数据模型
用于管理 PaperToX Agent 广场的 AI 代理信息
"""

import uuid
from enum import Enum
from tortoise import fields
from tortoise.models import Model


class PaperToXAgentStatus(str, Enum):
    """PaperToX Agent 状态枚举"""
    AVAILABLE = "available"              # 可用
    IN_DEVELOPMENT = "in_development"    # 开发中
    COMING_SOON = "coming_soon"          # 即将推出


class PaperToXAgentVersion(str, Enum):
    """PaperToX Agent 版本分类枚举"""
    BASIC = "BASIC"        # 基础版本
    ADVANCED = "ADVANCED"  # 进阶版本
    EXPERT = "EXPERT"      # 专家版本


class PaperToXAgentCategory(str, Enum):
    """PaperToX Agent 分类枚举"""
    CORE_TOOLS = "CORE_TOOLS"                # 核心工具
    ENHANCEMENT_MODULES = "ENHANCEMENT_MODULES"  # 增强模块
    CLINICAL_SPECIALTY = "CLINICAL_SPECIALTY"    # 临床专业
    ANALYSIS_TOOLS = "ANALYSIS_TOOLS"            # 分析工具
    VISUALIZATION = "VISUALIZATION"              # 可视化
    RESEARCH_ASSISTANCE = "RESEARCH_ASSISTANCE"  # 研究辅助


class PiPaperToXAgent(Model):
    """
    PaperToX Agent 数据模型

    用于管理 PaperToX Agent 广场的 AI 代理信息，包括：
    - Agent 基本信息（名称、描述、图标等）
    - 状态管理（可用、开发中、即将推出）
    - 使用统计和分类标签
    - 排序和启用状态
    - 机构隔离和权限控制
    - 逻辑删除和审计字段
    """

    # 主键 - 使用 UUID
    id = fields.UUIDField(pk=True, default=uuid.uuid4, description="主键ID")

    # 基本信息
    name = fields.CharField(max_length=100, description="Agent名称，如Paper2Code")
    short_description = fields.CharField(max_length=200, description="简短功能描述")
    long_description = fields.TextField(null=True, description="详细功能描述")
    icon_identifier = fields.CharField(max_length=50, null=True, description="图标标识符")
    url = fields.CharField(max_length=500, null=True, description="跳转页面URL")

    # 状态和统计
    status = fields.CharEnumField(PaperToXAgentStatus, default=PaperToXAgentStatus.IN_DEVELOPMENT, description="状态：available, in_development, coming_soon")
    usage_count = fields.IntField(default=0, description="使用次数")

    # 分类和标签
    category = fields.CharField(max_length=50, null=True, description="分类")
    tags = fields.TextField(null=True, description="标签，JSON格式存储")
    version_type = fields.CharField(max_length=20, null=True, description="版本分类：BASIC, ADVANCED, EXPERT")

    # 排序和启用状态
    sort_order = fields.IntField(default=0, description="排序权重")
    is_active = fields.BooleanField(default=True, description="是否启用")

    # 机构隔离
    organization = fields.ForeignKeyField(
        "models.Organizations",
        related_name="papertox_agents",
        null=True,
        description="所属机构"
    )

    # 审计字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    created_by = fields.ForeignKeyField(
        "models.User",
        related_name="created_papertox_agents",
        null=True,
        description="创建人"
    )
    updated_by = fields.ForeignKeyField(
        "models.User",
        related_name="updated_papertox_agents",
        null=True,
        description="更新人"
    )

    # 逻辑删除
    is_deleted = fields.BooleanField(default=False, description="是否逻辑删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "pi_papertox_agents"
        description = "PaperToX Agent 广场"

    def __str__(self):
        return f"{self.name} ({self.status})"