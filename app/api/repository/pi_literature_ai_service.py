"""
PI文献AI服务Repository层
提供文献AI分析、大纲、重点、追问等功能的通用实现
"""

from typing import Optional, Dict, Any
from uuid import UUID
import json
import re
from app.models.organization_model_use import UseCase
from app.api.repository.user_default_model import get_user_model
from app.api.schemas.user import UserResponse
from app.models.literature import PiLiteratures, PiLiteraturesAssistant
from app.models.user import User
from app.services.llm_service import call_llm, call_llm_with_format_json
from app.services.insight.insight_prompts import (
    generate_analysis_prompt,
    insight_generate_outline_prompt,
    generate_key_points_prompt,
    generate_probe_questions_prompt,
    generate_probe_answer_prompt
)
from app.core.logging import get_logger
from app.utils.utils import send_data
import aiofiles
from pathlib import Path

logger = get_logger(__name__)


def clean_outline_format(outline_content: str) -> list:
    """
    清洗大纲格式，提取层级嵌套的标题结构

    Args:
        outline_content: AI生成的大纲内容

    Returns:
        清洗后的层级标题结构列表
    """

    def clean_markdown_format(text: str) -> str:
        """清理文本中的Markdown格式标记"""
        if not text:
            return text

        # 去除标题格式
        text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)
        # 去除粗体格式
        text = re.sub(r'\*\*([^\*\n]{1,200}?)\*\*', r'\1', text)
        text = re.sub(r'__([^_\n]{1,200}?)__', r'\1', text)
        # 去除斜体格式
        text = re.sub(r'\*([^\*\n]{1,200}?)\*', r'\1', text)
        text = re.sub(r'_([^_\n]{1,200}?)_', r'\1', text)
        # 去除删除线格式
        text = re.sub(r'~~([^~\n]{1,200}?)~~', r'\1', text)
        # 去除代码格式
        text = re.sub(r'`([^`\n]{1,200}?)`', r'\1', text)
        text = re.sub(r'^```[a-zA-Z]*\s*$', '', text, flags=re.MULTILINE)
        # 去除链接格式
        text = re.sub(r'\[([^\]]{1,100}?)\]\([^)]{1,200}?\)', r'\1', text)
        # 去除图片格式
        text = re.sub(r'!\[([^\]]{0,100}?)\]\([^)]{1,200}?\)', r'\1', text)
        # 去除引用格式
        text = re.sub(r'^>\s+', '', text, flags=re.MULTILINE)
        # 去除列表标记
        text = re.sub(r'^[-*+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\d+\.\s+', '', text, flags=re.MULTILINE)
        # 去除水平分割线
        text = re.sub(r'^[-*]{3,}\s*$', '', text, flags=re.MULTILINE)
        # 去除表格分隔符
        text = re.sub(r'\|', ' ', text)
        # 去除多余的空行和空格
        text = re.sub(r'\n\s*\n', '\n', text)
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    try:
        if not outline_content or not outline_content.strip():
            return []

        # 限制总输入文本长度
        MAX_INPUT_LENGTH = 20000
        if len(outline_content) > MAX_INPUT_LENGTH:
            logger.warning(f"输入文本过长 ({len(outline_content)} 字符)，截断到 {MAX_INPUT_LENGTH} 字符")
            outline_content = outline_content[:MAX_INPUT_LENGTH]

        result = []
        lines = outline_content.strip().split('\n')

        current_h1 = None
        current_h2 = None
        current_h3 = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 匹配一级标题
            h1_match = re.match(r'^#\s+(.+)$', line)
            if h1_match:
                current_h1 = {
                    "title": h1_match.group(1).strip(),
                    "subtitle": [],
                    "content": None
                }
                result.append(current_h1)
                current_h2 = None
                current_h3 = None
                continue

            # 匹配二级标题
            h2_match = re.match(r'^##\s+(.+)$', line)
            if h2_match:
                if current_h1 is None:
                    current_h1 = {
                        "title": "默认标题",
                        "subtitle": [],
                        "content": None
                    }
                    result.append(current_h1)

                current_h2 = {
                    "title": h2_match.group(1).strip(),
                    "subtitle": [],
                    "content": None
                }
                current_h1["subtitle"].append(current_h2)
                current_h3 = None
                continue

            # 匹配三级标题
            h3_match = re.match(r'^###\s+(.+)$', line)
            if h3_match:
                if current_h2 is None:
                    if current_h1 is None:
                        current_h1 = {
                            "title": "默认标题",
                            "subtitle": [],
                            "content": None
                        }
                        result.append(current_h1)

                    current_h2 = {
                        "title": "默认子标题",
                        "subtitle": [],
                        "content": None
                    }
                    current_h1["subtitle"].append(current_h2)

                current_h3 = {
                    "title": h3_match.group(1).strip(),
                    "subtitle": [],
                    "content": None
                }
                current_h2["subtitle"].append(current_h3)
                continue

            # 四级及以上标题作为内容处理
            h4_or_more_match = re.match(r'^#{4,}\s+(.+)$', line)
            if h4_or_more_match:
                content_text = clean_markdown_format(line)

                if current_h3:
                    if current_h3["content"]:
                        current_h3["content"] += f"\n{content_text}"
                    else:
                        current_h3["content"] = content_text
                elif current_h2:
                    if current_h2["content"]:
                        current_h2["content"] += f"\n{content_text}"
                    else:
                        current_h2["content"] = content_text
                elif current_h1:
                    if current_h1["content"]:
                        current_h1["content"] += f"\n{content_text}"
                    else:
                        current_h1["content"] = content_text
                continue

            # 匹配内容项 (数字. 内容)
            content_match = re.match(r'^\d+\.\s+(.+)$', line)
            if content_match:
                content_text = clean_markdown_format(content_match.group(1).strip())

                if current_h3:
                    if current_h3["content"]:
                        current_h3["content"] += f"\n{content_text}"
                    else:
                        current_h3["content"] = content_text
                elif current_h2:
                    if current_h2["content"]:
                        current_h2["content"] += f"\n{content_text}"
                    else:
                        current_h2["content"] = content_text
                elif current_h1:
                    if current_h1["content"]:
                        current_h1["content"] += f"\n{content_text}"
                    else:
                        current_h1["content"] = content_text
                continue

            # 其他文本内容
            if line:
                cleaned_line = clean_markdown_format(line)

                if current_h3:
                    if current_h3["content"]:
                        current_h3["content"] += f"\n{cleaned_line}"
                    else:
                        current_h3["content"] = cleaned_line
                elif current_h2:
                    if current_h2["content"]:
                        current_h2["content"] += f"\n{cleaned_line}"
                    else:
                        current_h2["content"] = cleaned_line
                elif current_h1:
                    if current_h1["content"]:
                        current_h1["content"] += f"\n{cleaned_line}"
                    else:
                        current_h1["content"] = cleaned_line

        # 清理结果
        def clean_node(node):
            if node.get("subtitle") and len(node["subtitle"]) > 0:
                node["content"] = None
                for sub_node in node["subtitle"]:
                    clean_node(sub_node)
            elif not node.get("subtitle"):
                node["subtitle"] = []

            if node.get("content"):
                node["content"] = clean_markdown_format(node["content"])

        for item in result:
            clean_node(item)

        # 层级提升清洗
        if len(result) == 1 and result[0].get("subtitle"):
            logger.info("检测到只有一个一级标题，进行层级提升清洗")
            single_h1 = result[0]

            promoted_result = []
            for h2_item in single_h1["subtitle"]:
                promoted_h1 = {
                    "title": h2_item["title"],
                    "subtitle": [],
                    "content": h2_item["content"]
                }

                if h2_item.get("subtitle"):
                    for h3_item in h2_item["subtitle"]:
                        promoted_h2 = {
                            "title": h3_item["title"],
                            "subtitle": [],
                            "content": h3_item["content"]
                        }
                        promoted_h1["subtitle"].append(promoted_h2)

                clean_node(promoted_h1)
                promoted_result.append(promoted_h1)

            result = promoted_result
            logger.info(f"层级提升完成，现在有 {len(result)} 个一级标题项目")

        logger.info(f"大纲清洗完成，提取了 {len(result)} 个一级标题项目")
        return result

    except Exception as e:
        logger.error(f"清洗大纲格式失败: {str(e)}")
        return []


# 定义JSON Schema
KEYNOTES_SCHEMA = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "keynote_title": {
                "type": "string",
                "description": "重点标题"
            },
            "keynote_sub_title": {
                "type": "array",
                "description": "重点子标题列表",
                "items": {
                    "type": "string"
                }
            }
        },
        "required": ["keynote_title", "keynote_sub_title"],
        "additionalProperties": False
    }
}

PROBE_SCHEMA = {
    "type": "array",
    "items": {
        "type": "string",
        "description": "追问问题"
    }
}


async def read_literature_content(literature: PiLiteratures) -> Optional[str]:
    """读取文献内容"""
    if not literature.parsed_content:
        return None

    try:
        async with aiofiles.open(literature.parsed_content, 'r', encoding='utf-8') as f:
            content = await f.read()

        # 组合标题、摘要和正文内容
        content_parts = []
        if literature.name:
            content_parts.append(f"标题: {literature.name}")
        if literature.abstract:
            content_parts.append(f"摘要: {literature.abstract}")
        if content:
            content_parts.append(f"正文: {content}")

        return "\n\n".join(content_parts)

    except Exception as e:
        logger.error(f"读取文献内容失败: {str(e)}")
        return None


async def pi_literature_ai_analyze(
    literature_id: UUID,
    current_user: UserResponse
) -> Dict[str, Any]:
    """
    PI文献AI分析

    Args:
        literature_id: 文献ID
        current_user: 当前用户

    Returns:
        分析结果字典
    """
    try:
        # 获取文献
        literature = await PiLiteratures.get_or_none(id=literature_id, is_deleted=False)
        if not literature:
            return send_data(False, None, "未找到对应的文献")

        # 获取或创建AI助手记录
        db_user = await User.get(id=current_user.id)
        ai_assistant = await PiLiteraturesAssistant.get_or_none(
            literature_id=literature_id,
            user_id=current_user.id
        )
        if not ai_assistant:
            ai_assistant = await PiLiteraturesAssistant.create(
                literature=literature,
                user=db_user
            )

        # 检查缓存
        if ai_assistant.analysis_content:
            logger.info(f"文献 {literature_id} 已存在AI分析，直接返回")
            return send_data(True, {"analysis": ai_assistant.analysis_content})

        # 读取文献内容
        content = await read_literature_content(literature)
        if not content:
            return send_data(False, None, "文献内容为空，无法进行分析")

        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.PI_AI_ASSISTANT.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")

        # 生成分析提示词
        messages = generate_analysis_prompt(name=literature.name, content=content)
        if not messages:
            return send_data(False, None, "生成分析提示词失败")

        # 调用LLM服务
        analysis_content = await call_llm(
            messages=messages,
            flag=f"analyze_literature_{literature_id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )

        if not analysis_content:
            return send_data(False, None, "AI生成分析内容为空")

        # 保存分析结果
        ai_assistant.analysis_content = analysis_content
        await ai_assistant.save()

        logger.info(f"成功为文献 {literature_id} 生成AI分析")
        return send_data(True, {"analysis": analysis_content})

    except Exception as e:
        logger.error(f"文献AI分析失败: {str(e)}")
        return send_data(False, None, f"AI分析失败: {str(e)}")


async def pi_literature_ai_outline(
    literature_id: UUID,
    current_user: UserResponse
) -> Dict[str, Any]:
    """
    PI文献AI大纲生成

    Args:
        literature_id: 文献ID
        current_user: 当前用户

    Returns:
        大纲结果字典 - 包含summary, outline, cleaned_outline, combined
    """
    try:
        # 获取文献
        literature = await PiLiteratures.get_or_none(id=literature_id, is_deleted=False)
        if not literature:
            return send_data(False, None, "未找到对应的文献")

        # 获取或创建AI助手记录
        db_user = await User.get(id=current_user.id)
        ai_assistant = await PiLiteraturesAssistant.get_or_none(
            literature_id=literature_id,
            user_id=current_user.id
        )
        if not ai_assistant:
            ai_assistant = await PiLiteraturesAssistant.create(
                literature=literature,
                user=db_user
            )

        # 检查缓存 - 如果已有大纲和概要，直接返回
        if ai_assistant.outline and ai_assistant.ai_summary:
            logger.info(f"文献 {literature_id} 已存在AI大纲和概要，直接返回")
            cleaned_outline = clean_outline_format(ai_assistant.outline)
            combined = f"{ai_assistant.ai_summary or ''} {ai_assistant.outline or ''}".strip()
            return send_data(True, {
                "summary": ai_assistant.ai_summary,
                "outline": ai_assistant.outline,
                "cleaned_outline": cleaned_outline,
                "combined": combined
            })

        # 读取文献内容
        content = await read_literature_content(literature)
        if not content:
            return send_data(False, None, "文献内容为空，无法生成大纲")

        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.PI_AI_ASSISTANT.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")

        # 生成概要（如果还没有）
        summary = ai_assistant.ai_summary
        if not summary:
            logger.info(f"为文献 {literature_id} 生成概要")
            from app.services.insight.insight_prompts import generate_summary_prompt
            summary_messages = generate_summary_prompt(content=content, is_detailed=False)
            if summary_messages:
                summary = await call_llm(
                    messages=summary_messages,
                    flag=f"generate_literature_summary_{literature_id}",
                    model=model_config.model_name,
                    apiKey=model_config.api_key,
                    apiUrl=model_config.api_url
                )
                if summary:
                    ai_assistant.ai_summary = summary

        # 生成大纲
        logger.info(f"为文献 {literature_id} 生成大纲")
        messages = insight_generate_outline_prompt(name=literature.name, content=content)
        if not messages:
            return send_data(False, None, "生成大纲提示词失败")

        # 调用LLM服务
        outline_content = await call_llm(
            messages=messages,
            flag=f"generate_literature_outline_{literature_id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )

        if not outline_content:
            return send_data(False, None, "AI生成大纲内容为空")

        # 清洗大纲格式
        cleaned_outline = clean_outline_format(outline_content)

        # 保存结果
        ai_assistant.outline = outline_content
        await ai_assistant.save()

        # 组合summary和outline
        combined = f"{summary or ''} {outline_content or ''}".strip()

        logger.info(f"成功为文献 {literature_id} 生成AI大纲")
        return send_data(True, {
            "summary": summary,
            "outline": outline_content,
            "cleaned_outline": cleaned_outline,
            "combined": combined
        })

    except Exception as e:
        logger.error(f"文献AI大纲生成失败: {str(e)}")
        return send_data(False, None, f"AI大纲生成失败: {str(e)}")


async def pi_literature_ai_keynotes(
    literature_id: UUID,
    current_user: UserResponse
) -> Dict[str, Any]:
    """
    PI文献AI重点生成

    Args:
        literature_id: 文献ID
        current_user: 当前用户

    Returns:
        重点结果字典
    """
    try:
        # 获取文献
        literature = await PiLiteratures.get_or_none(id=literature_id, is_deleted=False)
        if not literature:
            return send_data(False, None, "未找到对应的文献")

        # 获取或创建AI助手记录
        db_user = await User.get(id=current_user.id)
        ai_assistant = await PiLiteraturesAssistant.get_or_none(
            literature_id=literature_id,
            user_id=current_user.id
        )
        if not ai_assistant:
            ai_assistant = await PiLiteraturesAssistant.create(
                literature=literature,
                user=db_user
            )

        # 检查缓存
        if ai_assistant.key_points:
            logger.info(f"文献 {literature_id} 已存在AI重点，直接返回")
            try:
                keynotes_data = json.loads(ai_assistant.key_points)
                return send_data(True, {"keynotes": keynotes_data})
            except json.JSONDecodeError:
                # 如果缓存的数据不是JSON格式，则重新生成
                logger.warning(f"文献 {literature_id} 缓存的重点数据格式错误，重新生成")

        # 读取文献内容
        content = await read_literature_content(literature)
        if not content:
            return send_data(False, None, "文献内容为空，无法生成重点")

        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_GENERATE.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")

        # 生成重点提示词
        messages = generate_key_points_prompt(content=content)
        if not messages:
            return send_data(False, None, "生成重点提示词失败")

        # 定义response_format
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "name": "keynotes_result",
                "strict": True,
                "schema": KEYNOTES_SCHEMA
            }
        }

        # 调用LLM服务
        keynotes_content = await call_llm_with_format_json(
            messages=messages,
            model=model_config.model_name,
            api_key=model_config.api_key,
            api_url=model_config.api_url,
            response_format=response_format
        )

        if not keynotes_content:
            return send_data(False, None, "AI生成重点内容为空")

        try:
            # 解析JSON内容
            keynotes_data = json.loads(keynotes_content)

            # 保存重点结果
            ai_assistant.key_points = keynotes_content
            await ai_assistant.save()

            logger.info(f"成功为文献 {literature_id} 生成AI重点")
            return send_data(True, {"keynotes": keynotes_data})

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return send_data(False, None, "AI生成的重点内容格式错误")

    except Exception as e:
        logger.error(f"文献AI重点生成失败: {str(e)}")
        return send_data(False, None, f"AI重点生成失败: {str(e)}")


async def pi_literature_ai_probe_questions(
    literature_id: UUID,
    current_user: UserResponse
) -> Dict[str, Any]:
    """
    PI文献AI追问问题生成

    Args:
        literature_id: 文献ID
        current_user: 当前用户

    Returns:
        追问问题结果字典
    """
    try:
        # 获取文献
        literature = await PiLiteratures.get_or_none(id=literature_id, is_deleted=False)
        if not literature:
            return send_data(False, None, "未找到对应的文献")

        # 获取或创建AI助手记录
        db_user = await User.get(id=current_user.id)
        ai_assistant = await PiLiteraturesAssistant.get_or_none(
            literature_id=literature_id,
            user_id=current_user.id
        )
        if not ai_assistant:
            ai_assistant = await PiLiteraturesAssistant.create(
                literature=literature,
                user=db_user
            )

        # 检查缓存
        if ai_assistant.follow_up_questions:
            logger.info(f"文献 {literature_id} 已存在AI追问，直接返回")
            try:
                probe_data = json.loads(ai_assistant.follow_up_questions)
                return send_data(True, {"probes": probe_data})
            except json.JSONDecodeError:
                # 如果缓存的数据不是JSON格式，则重新生成
                logger.warning(f"文献 {literature_id} 缓存的追问数据格式错误，重新生成")

        # 读取文献内容
        content = await read_literature_content(literature)
        if not content:
            return send_data(False, None, "文献内容为空，无法生成追问")

        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.PI_AI_ASSISTANT.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")

        # 生成追问提示词
        messages = generate_probe_questions_prompt(content=content)
        if not messages:
            return send_data(False, None, "生成追问提示词失败")

        # 定义response_format
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "name": "probe_result",
                "strict": True,
                "schema": PROBE_SCHEMA
            }
        }

        # 调用LLM服务
        probe_content = await call_llm_with_format_json(
            messages=messages,
            model=model_config.model_name,
            api_key=model_config.api_key,
            api_url=model_config.api_url,
            response_format=response_format
        )

        if not probe_content:
            return send_data(False, None, "AI生成追问内容为空")

        try:
            # 解析JSON内容
            probe_data = json.loads(probe_content)

            # 保存追问结果
            ai_assistant.follow_up_questions = probe_content
            await ai_assistant.save()

            logger.info(f"成功为文献 {literature_id} 生成AI追问")
            return send_data(True, {"probes": probe_data})

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return send_data(False, None, "AI生成的追问内容格式错误")

    except Exception as e:
        logger.error(f"文献AI追问生成失败: {str(e)}")
        return send_data(False, None, f"AI追问生成失败: {str(e)}")


async def pi_literature_ai_probe_answer(
    literature_id: UUID,
    current_user: UserResponse,
    question: str
) -> Dict[str, Any]:
    """
    PI文献AI追问回答

    Args:
        literature_id: 文献ID
        current_user: 当前用户
        question: 问题

    Returns:
        回答结果字典
    """
    try:
        # 获取文献
        literature = await PiLiteratures.get_or_none(id=literature_id, is_deleted=False)
        if not literature:
            return send_data(False, None, "未找到对应的文献")

        # 读取文献内容
        content = await read_literature_content(literature)
        if not content:
            return send_data(False, None, "文献内容为空，无法回答问题")

        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.PI_AI_ASSISTANT.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")

        # 生成回答提示词（只需要content和question两个参数）
        messages = generate_probe_answer_prompt(
            content=content,
            question=question
        )
        if not messages:
            return send_data(False, None, "生成回答提示词失败")

        # 调用LLM服务
        answer_content = await call_llm(
            messages=messages,
            flag=f"probe_answer_literature_{literature_id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )

        if not answer_content:
            return send_data(False, None, "AI生成回答内容为空")

        logger.info(f"成功为文献 {literature_id} 生成AI追问回答")
        return send_data(True, {"answer": answer_content})

    except Exception as e:
        logger.error(f"文献AI追问回答失败: {str(e)}")
        return send_data(False, None, f"AI追问回答失败: {str(e)}")