"""
GROBID服务客户端
用于调用GROBID API解析PDF文献元数据
"""

import aiohttp
import asyncio
from typing import Optional, Dict, Any, List
import xml.etree.ElementTree as ET
from pathlib import Path
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)


class GrobidClient:
    """GROBID API客户端"""

    def __init__(self, base_url: Optional[str] = None):
        """
        初始化GROBID客户端
        
        Args:
            base_url: GROBID服务地址，如果为None则从配置中读取
        """
        if base_url is None:
            base_url = settings.GROBID_BASE_URL
        self.base_url = base_url.rstrip('/')

    async def parse_pdf_header(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """
        解析PDF头部信息（标题、作者、摘要等）

        Args:
            pdf_path: PDF文件路径

        Returns:
            解析结果字典，包含标题、作者、摘要等信息
        """
        try:
            async with aiohttp.ClientSession(
                connector=aiohttp.TCPConnector(local_addr=None)
            ) as session:
                with open(pdf_path, 'rb') as pdf_file:
                    files = {'input': pdf_file}

                    async with session.post(
                        f"{self.base_url}/api/processHeaderDocument",
                        data=files,
                        timeout=aiohttp.ClientTimeout(total=120),
                        proxy=None  # 避免代理问题
                    ) as response:
                        if response.status == 200:
                            content = await response.text()
                            # 检查返回内容格式
                            if content.strip().startswith('@'):
                                # BibTeX格式
                                return self._parse_bibtex(content)
                            elif content.strip().startswith('<?xml') or content.strip().startswith('<TEI'):
                                # XML格式
                                return self._parse_header_xml(content)
                            else:
                                logger.error("Unknown response format")
                                return None
                        else:
                            logger.error(f"GROBID API error: {response.status}")
                            return None

        except Exception as e:
            logger.error(f"Error calling GROBID API: {str(e)}")
            return None

    async def parse_pdf_fulltext(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """
        解析PDF全文

        Args:
            pdf_path: PDF文件路径

        Returns:
            解析结果字典，包含全文结构化信息
        """
        try:
            async with aiohttp.ClientSession(
                connector=aiohttp.TCPConnector(local_addr=None)
            ) as session:
                with open(pdf_path, 'rb') as pdf_file:
                    files = {'input': pdf_file}

                    async with session.post(
                        f"{self.base_url}/api/processFulltextDocument",
                        data=files,
                        timeout=aiohttp.ClientTimeout(total=180),  # 全文解析需要更长时间
                        proxy=None  # 避免代理问题
                    ) as response:
                        if response.status == 200:
                            xml_content = await response.text()
                            return self._parse_fulltext_xml(xml_content)
                        else:
                            logger.error(f"GROBID API error: {response.status}")
                            return None

        except Exception as e:
            logger.error(f"Error calling GROBID API: {str(e)}")
            return None

    def _parse_bibtex(self, bibtex_content: str) -> Dict[str, Any]:
        """解析BibTeX格式内容"""
        try:
            result = {
                'title': None,
                'authors': [],
                'abstract': None,
                'keywords': [],
                'doi': None,
                'publication_date': None,
                'journal': None
            }

            lines = bibtex_content.strip().split('\n')

            for line in lines:
                line = line.strip()
                if not line or line.startswith('@'):
                    continue

                # 解析字段
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip(',').strip('{}').strip('"')

                    if key == 'title':
                        result['title'] = value
                    elif key == 'author':
                        # 处理作者字段
                        if value:
                            # 简单分割作者（通过 'and' 分隔）
                            authors = [name.strip() for name in value.split(' and ')]
                            result['authors'] = [{'name': name} for name in authors if name]
                    elif key == 'abstract':
                        result['abstract'] = value
                    elif key == 'doi':
                        result['doi'] = value
                    elif key == 'year':
                        result['publication_date'] = value
                    elif key == 'journal':
                        result['journal'] = value

            return result

        except Exception as e:
            logger.error(f"Error parsing BibTeX: {str(e)}")
            return {}

    def _parse_header_xml(self, xml_content: str) -> Dict[str, Any]:
        """解析头部XML内容"""
        try:
            root = ET.fromstring(xml_content)

            # 定义命名空间
            ns = {'tei': 'http://www.tei-c.org/ns/1.0'}

            result = {
                'title': None,
                'authors': [],
                'abstract': None,
                'keywords': [],
                'doi': None,
                'publication_date': None,
                'journal': None
            }

            # 提取标题
            title_elem = root.find('.//tei:title[@type="main"]', ns)
            if title_elem is not None and title_elem.text:
                result['title'] = title_elem.text.strip()

            # 提取作者
            authors = root.findall('.//tei:author', ns)
            for author in authors:
                author_info = {}

                # 姓名
                forename = author.find('.//tei:forename[@type="first"]', ns)
                surname = author.find('.//tei:surname', ns)

                if forename is not None and surname is not None:
                    author_info['name'] = f"{forename.text} {surname.text}".strip()
                elif surname is not None:
                    author_info['name'] = surname.text.strip()

                # 机构
                affiliation = author.find('.//tei:orgName[@type="institution"]', ns)
                if affiliation is not None:
                    author_info['affiliation'] = affiliation.text.strip()

                if author_info:
                    result['authors'].append(author_info)

            # 提取摘要
            abstract_elem = root.find('.//tei:abstract', ns)
            if abstract_elem is not None:
                # 获取所有段落文本
                abstract_text = []
                for p in abstract_elem.findall('.//tei:p', ns):
                    if p.text:
                        abstract_text.append(p.text.strip())
                result['abstract'] = ' '.join(abstract_text)

            # 提取DOI
            doi_elem = root.find('.//tei:idno[@type="DOI"]', ns)
            if doi_elem is not None and doi_elem.text:
                result['doi'] = doi_elem.text.strip()

            # 提取关键词
            keywords = root.findall('.//tei:term', ns)
            for kw in keywords:
                if kw.text:
                    result['keywords'].append(kw.text.strip())

            # 提取发表日期
            date_info = self._extract_date_info(root, ns)
            if date_info:
                # 优先使用ISO格式日期
                if 'date_iso' in date_info:
                    result['publication_date'] = date_info['date_iso']
                elif 'year' in date_info:
                    result['publication_date'] = date_info['year']
                elif 'date_published' in date_info:
                    result['publication_date'] = date_info['date_published']

            return result

        except ET.ParseError as e:
            logger.error(f"XML parsing error: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"Error parsing GROBID XML: {str(e)}")
            return {}

    def _parse_fulltext_xml(self, xml_content: str) -> Dict[str, Any]:
        """
        解析全文XML内容
        根据GROBID文档标准解析TEI格式的全文XML
        """
        try:
            # 先解析头部信息
            header_info = self._parse_header_xml(xml_content)

            root = ET.fromstring(xml_content)
            ns = {'tei': 'http://www.tei-c.org/ns/1.0'}

            # 添加全文特有的信息
            header_info['sections'] = []
            header_info['references'] = []
            header_info['figures'] = []
            header_info['tables'] = []
            header_info['formulas'] = []

            # 解析正文内容 - 按照文档流顺序解析
            body = root.find('.//tei:body', ns)
            if body is not None:
                self._parse_body_content(body, header_info, ns)

            # 解析参考文献
            self._parse_references(root, header_info, ns)

            # 解析图表和公式
            self._parse_figures_and_formulas(root, header_info, ns)

            # 生成完整的正文内容
            self._generate_fulltext(header_info)

            return header_info

        except Exception as e:
            logger.error(f"Error parsing fulltext XML: {str(e)}")
            return self._parse_header_xml(xml_content)  # 降级到头部解析

    def _parse_body_content(self, body, result_dict: Dict[str, Any], ns: Dict[str, str]):
        """
        解析正文内容，按照文档流顺序处理各种元素
        """
        current_section = None
        
        for element in body:
            tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
            
            if tag == 'div':
                # 处理章节
                current_section = self._parse_section(element, ns)
                if current_section:
                    result_dict['sections'].append(current_section)
            
            elif tag == 'head' and current_section is None:
                # 处理独立的标题
                heading_text = self._extract_text_content(element, ns)
                if heading_text:
                    current_section = {
                        'heading': heading_text,
                        'content': '',
                        'paragraphs': [],
                        'lists': [],
                        'formulas': []
                    }
                    result_dict['sections'].append(current_section)
            
            elif tag == 'p':
                # 处理段落
                paragraph_text = self._extract_text_content(element, ns)
                if paragraph_text:
                    if current_section is None:
                        # 创建默认章节
                        current_section = {
                            'heading': '正文',
                            'content': '',
                            'paragraphs': [],
                            'lists': [],
                            'formulas': []
                        }
                        result_dict['sections'].append(current_section)
                    
                    current_section['paragraphs'].append(paragraph_text)
                    current_section['content'] += paragraph_text + ' '
            
            elif tag == 'list':
                # 处理列表
                list_items = self._parse_list(element, ns)
                if list_items:
                    if current_section is None:
                        current_section = {
                            'heading': '正文',
                            'content': '',
                            'paragraphs': [],
                            'lists': [],
                            'formulas': []
                        }
                        result_dict['sections'].append(current_section)
                    
                    current_section['lists'].extend(list_items)
                    current_section['content'] += ' '.join(list_items) + ' '
            
            elif tag == 'formula':
                # 处理公式
                formula_info = self._parse_formula(element, ns)
                if formula_info:
                    if current_section is None:
                        current_section = {
                            'heading': '正文',
                            'content': '',
                            'paragraphs': [],
                            'lists': [],
                            'formulas': []
                        }
                        result_dict['sections'].append(current_section)
                    
                    current_section['formulas'].append(formula_info)
                    result_dict['formulas'].append(formula_info)

    def _parse_section(self, section_element, ns: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """解析章节内容"""
        section_info = {
            'heading': '',
            'content': '',
            'paragraphs': [],
            'lists': [],
            'formulas': []
        }
        
        # 提取章节标题
        head = section_element.find('.//tei:head', ns)
        if head is not None:
            section_info['heading'] = self._extract_text_content(head, ns)
        
        # 处理章节内的所有内容
        for element in section_element:
            tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
            
            if tag == 'p':
                paragraph_text = self._extract_text_content(element, ns)
                if paragraph_text:
                    section_info['paragraphs'].append(paragraph_text)
                    section_info['content'] += paragraph_text + ' '
            
            elif tag == 'list':
                list_items = self._parse_list(element, ns)
                if list_items:
                    section_info['lists'].extend(list_items)
                    section_info['content'] += ' '.join(list_items) + ' '
            
            elif tag == 'formula':
                formula_info = self._parse_formula(element, ns)
                if formula_info:
                    section_info['formulas'].append(formula_info)
        
        return section_info if section_info['content'].strip() else None

    def _extract_text_content(self, element, ns: Dict[str, str]) -> str:
        """
        提取元素的文本内容，正确处理换行和引用标记
        """
        text_parts = []
        
        def process_element(elem):
            if elem.text:
                text_parts.append(elem.text)
            
            # 处理子元素
            for child in elem:
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                
                if child_tag == 'lb':
                    # 换行标记
                    text_parts.append('\n')
                elif child_tag == 'ref':
                    # 引用标记，保留引用内容
                    ref_text = self._extract_text_content(child, ns)
                    if ref_text:
                        text_parts.append(f"[{ref_text}]")
                else:
                    # 其他元素，递归处理
                    process_element(child)
                
                if child.tail:
                    text_parts.append(child.tail)
        
        process_element(element)
        return ''.join(text_parts).strip()

    def _parse_list(self, list_element, ns: Dict[str, str]) -> List[str]:
        """解析列表内容"""
        items = []
        for item in list_element.findall('.//tei:item', ns):
            item_text = self._extract_text_content(item, ns)
            if item_text:
                items.append(item_text)
        return items

    def _parse_formula(self, formula_element, ns: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """解析公式内容"""
        formula_info = {
            'content': '',
            'label': None
        }
        
        # 提取公式标签
        label = formula_element.find('.//tei:label', ns)
        if label is not None and label.text:
            formula_info['label'] = label.text.strip()
        
        # 提取公式内容
        formula_text = self._extract_text_content(formula_element, ns)
        if formula_text:
            formula_info['content'] = formula_text
        
        return formula_info if formula_info['content'] else None

    def _parse_references(self, root, result_dict: Dict[str, Any], ns: Dict[str, str]):
        """解析参考文献"""
        # 尝试多个可能的参考文献路径
        ref_paths = [
            './/tei:back/tei:listBibl/tei:biblStruct',
            './/tei:listBibl/tei:biblStruct',
            './/tei:div[@type="references"]//tei:biblStruct'
        ]
        
        for path in ref_paths:
            references = root.findall(path, ns)
            if references:
                for ref in references:
                    ref_info = self._parse_reference_item(ref, ns)
                    if ref_info:
                        result_dict['references'].append(ref_info)
                break

    def _parse_reference_item(self, ref_element, ns: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """解析单个参考文献项"""
        ref_info = {
            'title': None,
            'authors': [],
            'journal': None,
            'year': None,
            'month': None,
            'day': None,
            'date_iso': None,
            'date_text': None,
            'doi': None,
            'url': None,
            'publisher': None,
            'pages': None,
            'volume': None,
            'issue': None
        }
        
        # 提取标题
        title_elem = ref_element.find('.//tei:title[@level="a"]', ns)
        if title_elem is not None:
            ref_info['title'] = self._extract_text_content(title_elem, ns)
        
        # 提取作者
        authors = ref_element.findall('.//tei:author', ns)
        for author in authors:
            author_name = self._extract_author_name(author, ns)
            if author_name:
                ref_info['authors'].append(author_name)
        
        # 提取期刊
        journal_elem = ref_element.find('.//tei:title[@level="j"]', ns)
        if journal_elem is not None:
            ref_info['journal'] = self._extract_text_content(journal_elem, ns)
        
        # 提取日期信息
        date_info = self._extract_date_info(ref_element, ns)
        if date_info:
            # 统一处理日期字段，确保结构一致性
            if 'year' in date_info:
                ref_info['year'] = date_info['year']
            if 'month' in date_info:
                ref_info['month'] = date_info['month']
            if 'day' in date_info:
                ref_info['day'] = date_info['day']
            if 'date_iso' in date_info:
                ref_info['date_iso'] = date_info['date_iso']
            if 'date_text' in date_info:
                ref_info['date_text'] = date_info['date_text']
        
        # 提取DOI
        doi_elem = ref_element.find('.//tei:idno[@type="DOI"]', ns)
        if doi_elem is not None and doi_elem.text:
            ref_info['doi'] = doi_elem.text.strip()
        
        # 提取URL
        url_elem = ref_element.find('.//tei:idno[@type="URL"]', ns)
        if url_elem is not None and url_elem.text:
            ref_info['url'] = url_elem.text.strip()
        
        # 提取出版商
        publisher_elem = ref_element.find('.//tei:publisher', ns)
        if publisher_elem is not None:
            ref_info['publisher'] = self._extract_text_content(publisher_elem, ns)
        
        # 提取页码
        pages_elem = ref_element.find('.//tei:biblScope[@unit="page"]', ns)
        if pages_elem is not None and pages_elem.text:
            ref_info['pages'] = pages_elem.text.strip()
        
        # 提取卷号
        volume_elem = ref_element.find('.//tei:biblScope[@unit="volume"]', ns)
        if volume_elem is not None and volume_elem.text:
            ref_info['volume'] = volume_elem.text.strip()
        
        # 提取期号
        issue_elem = ref_element.find('.//tei:biblScope[@unit="issue"]', ns)
        if issue_elem is not None and issue_elem.text:
            ref_info['issue'] = issue_elem.text.strip()
        
        return ref_info if any(ref_info.values()) else None

    def _extract_author_name(self, author_element, ns: Dict[str, str]) -> Optional[str]:
        """提取作者姓名"""
        # 尝试不同的姓名组合方式
        forename = author_element.find('.//tei:forename', ns)
        surname = author_element.find('.//tei:surname', ns)
        
        if forename is not None and surname is not None:
            return f"{forename.text} {surname.text}".strip()
        elif surname is not None:
            return surname.text.strip()
        elif forename is not None:
            return forename.text.strip()
        
        # 如果没有找到结构化姓名，尝试直接获取文本
        author_text = self._extract_text_content(author_element, ns)
        return author_text if author_text else None

    def _extract_date_info(self, element, ns: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """
        提取日期信息，支持多种日期格式
        """
        date_info = {}
        
        # 查找各种日期元素
        date_elements = element.findall('.//tei:date', ns)
        
        for date_elem in date_elements:
            date_type = date_elem.get('type', 'published')
            
            # 提取when属性（ISO格式日期）
            when = date_elem.get('when')
            if when:
                try:
                    # 解析ISO日期格式
                    if len(when) >= 4:
                        date_info['year'] = when[:4]
                    if len(when) >= 7:
                        date_info['month'] = when[5:7]
                    if len(when) >= 10:
                        date_info['day'] = when[8:10]
                    date_info['date_iso'] = when
                except:
                    pass
            
            # 提取文本内容（可能包含非标准格式的日期）
            date_text = self._extract_text_content(date_elem, ns)
            if date_text:
                # 尝试从文本中提取年份
                import re
                year_match = re.search(r'\b(19|20)\d{2}\b', date_text)
                if year_match and 'year' not in date_info:
                    date_info['year'] = year_match.group()
                
                # 保存原始日期文本
                if f'date_{date_type}' not in date_info:
                    date_info[f'date_{date_type}'] = date_text
        
        # 如果没有找到结构化日期，尝试从其他元素中提取
        if not date_info:
            # 查找年份信息
            year_elem = element.find('.//tei:date[@type="published"]', ns)
            if year_elem is None:
                year_elem = element.find('.//tei:date', ns)
            
            if year_elem is not None:
                when = year_elem.get('when')
                if when and len(when) >= 4:
                    date_info['year'] = when[:4]
                    date_info['date_iso'] = when
                else:
                    year_text = self._extract_text_content(year_elem, ns)
                    if year_text:
                        import re
                        year_match = re.search(r'\b(19|20)\d{2}\b', year_text)
                        if year_match:
                            date_info['year'] = year_match.group()
                        date_info['date_text'] = year_text
        
        return date_info if date_info else None

    def _parse_figures_and_formulas(self, root, result_dict: Dict[str, Any], ns: Dict[str, str]):
        """解析图表和公式"""
        # 解析图表
        figures = root.findall('.//tei:figure', ns)
        for figure in figures:
            figure_info = self._parse_figure(figure, ns)
            if figure_info:
                if figure_info.get('type') == 'table':
                    result_dict['tables'].append(figure_info)
                else:
                    result_dict['figures'].append(figure_info)

    def _parse_figure(self, figure_element, ns: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """解析图表信息"""
        figure_info = {
            'type': figure_element.get('type', 'figure'),
            'title': None,
            'content': None,
            'caption': None
        }
        
        # 提取标题
        head = figure_element.find('.//tei:head', ns)
        if head is not None:
            figure_info['title'] = self._extract_text_content(head, ns)
        
        # 提取内容
        figure_content = self._extract_text_content(figure_element, ns)
        if figure_content:
            figure_info['content'] = figure_content
        
        # 提取说明文字
        caption = figure_element.find('.//tei:figDesc', ns)
        if caption is not None:
            figure_info['caption'] = self._extract_text_content(caption, ns)
        
        return figure_info if figure_info['content'] else None

    def _generate_fulltext(self, result_dict: Dict[str, Any]):
        """
        根据解析的sections生成完整的正文内容
        """
        fulltext_parts = []

        # 添加摘要
        if result_dict.get('abstract'):
            fulltext_parts.append(f"## Abstract\n\n{result_dict['abstract']}\n\n")

        # 添加各个章节的内容
        for section in result_dict.get('sections', []):
            if section.get('heading'):
                fulltext_parts.append(f"## {section['heading']}\n\n")

            if section.get('content'):
                fulltext_parts.append(f"{section['content'].strip()}\n\n")

        # 将所有部分组合成完整的正文
        result_dict['fulltext'] = ''.join(fulltext_parts).strip()

    async def health_check(self) -> bool:
        """检查GROBID服务是否可用"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/api/isalive",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
        except Exception as e:
            logger.error(f"GROBID health check failed: {str(e)}")
            return False


# 创建默认客户端实例
grobid_client = GrobidClient()


async def parse_literature_pdf(pdf_path: str, use_fulltext: bool = False) -> Optional[Dict[str, Any]]:
    """
    解析文献PDF的便捷函数

    Args:
        pdf_path: PDF文件路径
        use_fulltext: 是否使用全文解析（更慢但信息更全）

    Returns:
        解析结果字典
    """
    if use_fulltext:
        return await grobid_client.parse_pdf_fulltext(pdf_path)
    else:
        return await grobid_client.parse_pdf_header(pdf_path)


# Demo函数
async def demo_parse_pdf(pdf_path: str):
    """演示PDF解析功能"""
    print(f"正在解析PDF: {pdf_path}")

    # 检查服务状态
    if not await grobid_client.health_check():
        print("❌ GROBID服务不可用，请确保服务已启动在端口8070")
        return

    print("✅ GROBID服务已连接")

    # 解析头部信息
    print("\n📄 解析头部信息...")
    header_result = await grobid_client.parse_pdf_header(pdf_path)

    if header_result:
        print(f"标题: {header_result.get('title', 'N/A')}")
        print(f"作者: {', '.join([a.get('name', '') for a in header_result.get('authors', [])])}")
        print(f"摘要: {header_result.get('abstract', 'N/A')[:200]}...")
        print(f"DOI: {header_result.get('doi', 'N/A')}")
        print(f"关键词: {', '.join(header_result.get('keywords', []))}")
    else:
        print("❌ 解析失败")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        pdf_file = sys.argv[1]
        asyncio.run(demo_parse_pdf(pdf_file))
    else:
        print("用法: python grobid_client.py <pdf_file_path>")