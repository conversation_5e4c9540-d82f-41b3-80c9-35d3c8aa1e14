from pydantic import UUID4
from app.core.logging import get_logger
from app.models.voice_text import VoiceText
from app.api.schemas.voice_text import VoiceTextUpdate, VoiceTextResponse
from app.api.repository.upload_file import create_upload_file
from app.services.memory_storage import pcmHandler
from app.services.audio_utils import get_audio_duration
from typing import Optional
from datetime import datetime

logger = get_logger(__name__)

async def get_voice_text(voice_id: str):
  try:
    result = await VoiceText.filter(
      id=voice_id
    ).first()
    logger.info(f"获取录音内容记录成功，记录ID：{voice_id}")
    return VoiceTextResponse.model_validate(result, from_attributes=True)
  except Exception as e:
    logger.error(f"获取录音内容记录失败:{str(e)}")
    raise e
async def create_voice_text(
  user_id: str,
  text_content: Optional[str] = None
) -> VoiceText:
  """
  创建一条录音转文字记录。
  参数说明：
  - user_id: 当前请求的用户，用于审计日志记录
  - text_content: 文字内容，可为空（异步转写场景）
  返回：
  - 创建成功的 VoiceTextResponse 实体
  """
  try:
    result = await VoiceText.create(
      user_id=user_id,
      text_content=text_content
    )
    logger.info(f"用户{user_id}创建VoiceText记录: {result.id}")
    return VoiceTextResponse.model_validate(result, from_attributes=True)
  except Exception as e:
    logger.error(f"创建VoiceText失败: {str(e)}")
    raise e

async def update_voice_text(
    voice_text_id: UUID4,
    data: VoiceTextUpdate
) -> bool:
  """
  更新录音转文字记录。
  参数说明：
  - voice_text_id: 需要更新的记录ID
  - text_content: 新的文字内容
  - upload_file_id: 新的关联上传文件ID，可为空
  返回：
  - True 表示更新成功，False 表示无记录被更新
  """
  try:
    update_fields = {}
    if data.text_content is not None:
      update_fields["text_content"] = data.text_content
    if data.upload_file_id is not None:
      update_fields["upload_files_id"] = data.upload_file_id
    if not update_fields:
      logger.info("没有可以更新的数据")
      return True
    affected_rows = await VoiceText.filter(id=voice_text_id, is_deleted=False).update(**update_fields)
    is_updated = affected_rows > 0
    if is_updated:
      logger.info(f"更新VoiceText记录: {voice_text_id}")
    else:
      logger.info(f"尝试更新VoiceText记录但未找到: {voice_text_id}")
    return is_updated
  except Exception as e:
    logger.error(f"更新VoiceText失败: {str(e)}")
    raise e
async def end_voice_text(
    voice_id: str,
    user_id: str
  ):
  """结束录音并且将pcm数据导出为mp3存储"""
  try:
    logger.info(f"开始结束录音，录音ID：{voice_id}")
    file_path = pcmHandler.save_pcm_to_mp3(id=voice_id)
    logger.info(f"录音文件保存成功，文件路径：{file_path}")
    
    # 获取音频文件时长并转换为字符串格式的整数（秒）
    voice_duration = get_audio_duration(file_path)
    if voice_duration is None:
      logger.warning(f"无法获取录音时长，录音ID：{voice_id}")
      voice_time_str = "0"  # 设置默认值
    else:
      # 将浮点数秒数向上取整为整数，然后转换为字符串
      import math
      voice_time_seconds = math.ceil(voice_duration)
      voice_time_str = str(voice_time_seconds)
    
    text = pcmHandler.combine_transcription_text(id=voice_id)
    logger.info(f"录音转文字成功，文字内容：{text}")
    result = await create_upload_file(file_path=file_path)
    logger.info(f"录音文件上传成功，文件ID：{result.id}")
    
    # 更新录音记录，包含时长信息（字符串格式的整数秒）
    affected_rows = await VoiceText.filter(id=voice_id, user_id=user_id).update(
      **{
        "upload_files_id": result.id,
        "text_content": text,
        "voice_time": voice_time_str,  # 使用字符串格式的整数秒数
        "updated_at": datetime.now()
      }
    )
    
    # 清理临时数据
    pcmHandler.remove_data(id=voice_id)
    
    if affected_rows > 0:
      logger.info(f"录音结束处理完成，录音ID：{voice_id}，时长：{voice_time_str}秒")
    else:
      logger.warning(f"未找到对应的录音记录，录音ID：{voice_id}")
    
    return affected_rows > 0
  except Exception as e:
    logger.error(f"结束录音失败：{str(e)}")
    raise e