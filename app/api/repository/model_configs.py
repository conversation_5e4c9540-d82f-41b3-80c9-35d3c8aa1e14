from app.models.model_config import ModelConfig
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
from app.api.schemas.model_config import ModelConfigResponse
from app.utils.enum import ModelConfigError

logger = get_logger(__name__)

async def get_super_admin_model():
  all_model = await ModelConfig.filter(
    is_deleted=False,
    is_active=True
  ).all()
  return all_model
