from app.models.dictionary import Dictionary

# 获取某条字典信息
async def get_one_dict_by_category_and_value(
  category: str,
  value: str
) -> Dictionary:
  literature_library_dict = await Dictionary.filter(
    category_value=category,
    value=value,
    is_deleted=False
  ).first()
  return literature_library_dict
# 根据value获取对应的label
async def get_label_by_value(
  value: str
):
  literature_library_dict = await Dictionary.filter(
    value=value,
    is_deleted=False
  ).first()
  return literature_library_dict.label