from typing import List, Optional
from uuid import UUID
from datetime import datetime
from tortoise.expressions import Q

from app.models.think_tank_meta import ThinkTankMeta
from app.api.schemas.think_tank_meta import (
    ThinkTankMetaResponse
)
from app.core.logging import get_logger

logger = get_logger(__name__)

async def get_think_tank_meta(meta_id: str):
    """
    获取智库报告元数据记录
    
    Args:
        meta_id: 智库报告ID
        
    Returns:
        ThinkTankMetaResponse: 智库报告响应数据
        
    Raises:
        Exception: 查询失败时抛出异常
    """
    try:
        result = await ThinkTankMeta.filter(
            id=meta_id
        ).first()
        return ThinkTankMetaResponse.model_validate(result, from_attributes=True)
    except Exception as e:
        logger.error(f"获取智库报告元数据记录失败:{str(e)}")
        raise e
