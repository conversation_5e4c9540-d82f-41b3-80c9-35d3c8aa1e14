from app.models.literatures import Literature
from app.api.schemas.literatures import LiteratureCreate, LiteratureResponse
from datetime import datetime
from typing import List
from app.models.project_configs import ProjectConfig
from app.core.logging import get_logger
from tortoise.expressions import Q

# 获取logger实例
logger = get_logger(__name__)

"""去除doi的域名"""
def normalize_doi(doi: str) -> str:
    if not doi:
        return doi
    # 常见前缀形式
    prefixes = [
        "https://doi.org/",
        "http://doi.org/",
        "doi.org/",
        "https://dx.doi.org/",
        "http://dx.doi.org/",
        "dx.doi.org/"
    ]
    for p in prefixes:
        if doi.lower().startswith(p):
            return doi[len(p):]
    return doi

"""创建参考文献记录"""
async def create_literature(literature: LiteratureCreate) -> LiteratureResponse:
    """创建文献"""
    try:  
      data = literature.model_dump()
      data["updated_at"] = datetime.now()
      literature = Literature(**data)
      await literature.save()
      return LiteratureResponse.model_validate(literature)
    except Exception as e:
      logger.error(f"创建文献失败:{e}")

"""通过研究ID来获取参考文献列表"""
async def get_literature_by_id(research_id: str) -> List[LiteratureResponse]:
    """根据研究ID获取文献"""
    literature = await Literature.filter(research_id=research_id).all()
    return [LiteratureResponse.model_validate(item) for item in literature]

"""获取某个项目材料最新的参考文献列表"""
async def get_literature_by_project(config_id: str) -> List[LiteratureResponse]:
    """根据研究ID获取项目配置"""
    project_config = await ProjectConfig.filter(id=config_id, is_deleted=False).first()
    if not project_config:
      raise ValueError("项目配置不存在")
    literatures = await Literature.filter(research_id=project_config.research_id).all()
    literature_list = [LiteratureResponse.model_validate(item) for item in literatures]
    return literature_list
"""获取某个研究的搜索总结到的文献个数"""
async def get_literature_length(research_id: str) -> int:
   literature_count = await Literature.filter(research_id=research_id).count()
   return literature_count
"""根据一个链接url获取文献记录"""
async def get_literature_by_url(url: str) -> LiteratureResponse:
    """对url进行一下处理"""
    url = normalize_doi(url)
    literature_data = await Literature.filter(
        Q(url__icontains=url)|Q(doi=url)
    ).first()
    if not literature_data:
        raise Exception(f"url:{url}数据不存在。")
    return LiteratureResponse.model_validate(literature_data, from_attributes=True)