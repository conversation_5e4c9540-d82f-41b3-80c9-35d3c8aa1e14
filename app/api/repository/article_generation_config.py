# 实现从研究报告流式生成内容的异步生成器函数
import asyncio
import json
import aiohttp
import traceback
import base64
from typing import Callable, Dict, List, Optional
from pydantic import UUID4
from app.api.repository.article_literatures import create_article_literature, get_article_literature_by_id
from app.api.repository.dictionary import get_one_dict_by_category_and_value
from app.api.repository.research import get_context_length
from app.api.repository.user_default_model import get_user_model
from app.api.repository.llm_call_log import create_llm_call_log, update_llm_call
from app.api.schemas.article_literatures import ArticleLiteratureCreate
from app.api.schemas.project_configs import LiteratureLibraryType
from app.api.schemas.user import UserResponse
from app.models.article_generation_config import ArticleGenerationConfig
from app.models.article_literatures import ArticleLiterature
from app.models.organization_model_use import UseCase
from app.models.research import Research, ResearchStatus
from app.services import search_service
from app.services import prompts
from app.services.research_service import extract_relevant_context, generate_search_queries, get_new_search_queries, is_page_useful, is_valid_reference
from app.utils.constants import ProductType
from app.utils.enum import CallLLMFlag, ProjectReportError
from app.core.logging import get_logger
from app.core.config import settings

from pymed import PubMed
pubmed = PubMed(tool=settings.PUBMED_TOOL_NAME, email=settings.PUBMED_EMAIL)

logger = get_logger(__name__)

async def stream_article_generation_config_content(
    current_user: UserResponse,
    research: Research,
    config_response: ArticleGenerationConfig,
    callback: Optional[Callable[[str], None]] = None,
    complete_callback: Optional[Callable[[str], None]] = None,
    error_callback: Optional[Callable[[str], None]] = None,
    api_key: str = "",
    api_url: str = "",
    model: str = "",
    open_literature_summary = False
):
    """
    流式生成报告内容
    
    执行真实的搜索-总结-迭代-搜索流程，并流式返回生成结果
    """
    #  team_members = [] if not config_response.team_members else [
    #   ProjectMemberBase(
    #     name=join.member.name,
    #     title=join.member.title,
    #     representative_works=join.member.representative_works,
    #     organization=join.member.organization
    #   )
    #   for join in config_response.team_members
    # ]
    # 生成报告提示词
    model_config = await get_user_model(
        current_user=current_user,
        use_case=UseCase.JUDGE_WEB_USEFUL.value
    )
    try:
        # 安全地处理 reference_library，确保至少走一次循环
        try:
            if config_response.reference_library and config_response.reference_library.strip():
                search_engine_list = [item.strip() for item in config_response.reference_library.split(",") if item.strip()]
            else:
                search_engine_list = [""]  # 空字符串，保证走一次循环
                logger.info(f"文章配置 {config_response.id} 的 reference_library 为空，使用默认值进行搜索")
            
            logger.info(f"文章配置 {config_response.id} 开始搜索参考文献库，搜索列表: {search_engine_list}")
            
            # 搜索参考文献库
            for search_method_flag in search_engine_list:
                logger.info(f"开始搜索参考文献库: {search_method_flag}")
                await search_to_carry_info(
                    research=research,
                    current_user=current_user,
                    config_response=config_response,
                    # 搜索关键词的引擎的标识符
                    search_method_flag=search_method_flag,
                    open_literature_summary=open_literature_summary,
                )
                
        except Exception as e:
            logger.error(f"处理 reference_library 时出错: {str(e)}")
            
            

        try:
            # 搜索用户给的指定url
            logger.info(f"开始进行用户上传的URL的查询")
            
            # 从配置中获取reference_library_urls，而不是从数据库查询
            url_list = []
            if config_response.reference_library_urls:
                try:
                    import json
                    url_list = json.loads(config_response.reference_library_urls)
                    logger.info(f"从配置中获取到 {len(url_list)} 个URL")
                except json.JSONDecodeError as e:
                    logger.error(f"解析reference_library_urls失败: {e}")
                    url_list = []
            
            if url_list:
                tasks = [process_url(
                    title=config_response.name,  # name
                    url=url,  # 直接使用URL字符串
                    research_id=str(research.id),
                    search_query=config_response.name,  # 作为搜索查询
                    api_key=model_config.api_key,
                    api_url=model_config.api_url,
                    model=model_config.model_name,
                    is_summary_literature=open_literature_summary,
                    config_response=config_response
                ) for url in url_list]
                resources = await asyncio.gather(*tasks)
                url_contexts = []
                # 收集成功提取的上下文
                for resource in resources:
                    if resource:
                        url_contexts.append(resource)
                research.contexts += url_contexts
                await research.save()
                logger.info(f"成功处理了 {len(url_contexts)} 个URL资源")
            else:
                logger.info("没有配置reference_library_urls，跳过URL处理")
                
        except Exception as e:
            error_msg = ProjectReportError.GET_USER_ADD_URL_FAIL.value
            logger.error(error_msg)
        logger.info("迭代结束！开始正常流式生成报告内容")
        # 步骤3: 生成最终报告
        final_section = "## 步骤3: 生成研究报告\n\n"
        # yield f"data: {json.dumps({'content': final_section, 'type': 'section'})}\n\n".encode('utf-8')
        logger.info(final_section)

        generating_msg = "正在生成最终报告，这可能需要一些时间...\n\n"
        # yield f"data: {json.dumps({'content': generating_msg, 'type': 'status'})}\n\n".encode('utf-8')
        logger.info(generating_msg)

        research.status = ResearchStatus.ANALYZING
        await research.save()
        
        # 打印research.query和research.contexts用于调试
        debug_info = f"DEBUG - Query: {research.query}\n\n"
        logger.info(debug_info)
        # yield f"data: {json.dumps({'content': debug_info, 'type': 'debug'})}\n\n".encode('utf-8')

        # 显示收集的上下文数量
        contexts_count = f"DEBUG - 收集到 {len(research.contexts)} 条上下文\n\n"
        logger.info(contexts_count)
        # yield f"data: {json.dumps({'content': contexts_count, 'type': 'debug'})}\n\n".encode('utf-8')

        # 如果需要显示详细内容，可以添加以下代码（谨慎使用，可能输出很多内容）
        for i, context in enumerate(research.contexts[:3]):  # 只显示前3条，避免输出过多
            context_preview = context[:200] + "..." if len(context) > 200 else context
            context_info = f"上下文 #{i+1}:\n{context_preview}\n\n"
            logger.info(context_info)
            # yield f"data: {json.dumps({'content': context_info, 'type': 'debug'})}\n\n".encode('utf-8')

        # 还可以添加日志记录
        logger.info(f"研究id {research.id}: 查询: {research.query}")
        logger.info(f"研究id {research.id}: 收集到 {len(research.contexts)} 条上下文")
        messages = None
        try:
            # config = generate_project_config_prompt(
            #     name=config_response.name,
            #     # application_category=config_response.application_category or "",
            #     leader=config_response.leader,
            #     team_members=None,
            #     word_count_requirement=config_response.word_count_requirement or 0,
            #     team_introduction=config_response.team_introduction or "",
            #     flag='REPORT',
            #     leader_introduction=config_response.ai_leader_introduction
            #     # language_style=config_response.language_style or ""
            # )
            # participants = "、".join([
            #     f"{item.name}{item.title or ''}" + 
            #     (f"，就职于{item.organization}" if item.organization else "") + 
            #     (f"，{item.education + '学历'}" if item.education else "") + 
            #     (f"，代表性成就有{item.representative_works}" if item.representative_works else "")
            #     for item in config_response.team_members
            # ])
            # main = "" if not config_response.leader else f"{config_response.leader.name}, institution established date {config_response.leader.founded_date}, related projects: {config_response.leader.related_projects}"
            literatures = await get_article_literature_by_id(research.id)

            # 获取参考资料 - 使用新的中间表
            from app.models.file_biz_relations import FileBizRelation
            from app.utils.constants import ProductType
            
            # 获取该项目的所有参考资料关联（只查询未删除的文件）
            # reference_relations = await FileBizRelation.filter(
            #     product_type=ProductType.DOCGEN,
            #     biz_id=str(config_response.id),
            #     file__is_deleted=False  # 通过关联查询过滤已删除的文件
            # ).prefetch_related("file").all()
            
            # 汇总所有AI分析结果
            # all_analysis_results = []
            # for relation in reference_relations:
            #     file = relation.file
            #     # 🔧 关键修复：添加文件存在性和软删除检查
            #     if not file:
            #         logger.warning(f"关联记录 {relation.id} 的文件已被删除，跳过处理")
            #         continue
            #     if getattr(file, 'is_deleted', False):
            #         logger.warning(f"关联记录 {relation.id} 的文件已被软删除，跳过处理，文件名: {file.file_name}")
            #         continue
            #     file_name = file.file_name           # 文件名称
            #     word_count = file.word_count         # 字数（不是file_size）
            #     analysis_result = relation.ai_analysis_summary  # 从关联表获取AI分析结果
                
            #     logger.info(f"处理参考资料文件: {file_name}, 字数: {word_count}, AI分析结果长度: {len(analysis_result) if analysis_result else 0}")
                
            #     if analysis_result:
            #         all_analysis_results.append(f"\n{analysis_result}")
            
            # # 合并所有分析结果
            # final_analysis_result = "\n\n".join(all_analysis_results) if all_analysis_results else ""
            # logger.info(f"项目 {config_response.id} 的参考资料总结完成，共 {len(reference_relations)} 个文件，合并后分析结果长度: {len(final_analysis_result)}")
           
            # 准备生成报告的提示词

            # 局部导入 replace_template_variables 函数
            from app.api.routes.article_generation_config import replace_template_variables
            
            # 使用类似生成大纲的方式处理用户提示词
            user_prompt = config_response.content_user_prompt 
            if user_prompt:
                # 使用变量替换功能
                user_prompt = replace_template_variables(user_prompt, config_response)
                logger.info(f"文章内容用户提示词：\n{user_prompt}")
        
            # 如果开启了文献总结，添加参考文献提示词
            if open_literature_summary:
                reference_prompt = await prompts.final_report_reference_prompt(
                    literatures=literatures,
                    is_summary_literature=open_literature_summary
                )
                logger.info(f"参考文献的提示词：\n{reference_prompt}")
                logger.info(f"文章内容用户提示词：\n{user_prompt}")
                user_prompt = reference_prompt + user_prompt

            # 使用默认的系统提示词
            # system_prompt = await prompts.final_report_prompt_system(
            #     word_count=config_response.word_count_requirement or 0,
            #     is_summary_literature=open_literature_summary
            # )

            # 对系统提示词也使用变量替换
            system_prompt = replace_template_variables(config_response.content_system_prompt, config_response)
            logger.info(f"替换变量后的系统提示词：\n{system_prompt}")

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            logger.info(f"最终报告的用户提示词：\n{user_prompt}")
        except Exception as e:
            msg = f"生成报告提示词失败: {str(e)}"
            logger.error(msg)
            raise Exception(msg)
        
        await article_stream_llm_and_save(
            messages=messages,
            user=current_user, 
            callback=callback,
            complete_callback=complete_callback,
            error_callback=error_callback,
            flag=CallLLMFlag.GENERATE_REPORT.value,
            model=model.model_name,
            apiKey=api_key,
            apiUrl=api_url,
            related_id=config_response.id,
            config_response=config_response
        )
    except Exception as e:
        error_msg = f"报告流式生成错误: {str(e)}"
        logger.error(error_msg)
        await wrapper_error_callback(error_msg)

async def wrapper_error_callback(
  error_msg: str,
  error_callback: Optional[Callable[[str], None]] = None
):
  logger.error(error_msg)
  if error_callback:
      await error_callback(error_msg)
      

async def search_to_carry_info(
    research: Research,
    config_response: ArticleGenerationConfig,
    current_user: UserResponse,
    # 搜索关键词的引擎的标识符
    search_method_flag: Optional[str] = None,
    open_literature_summary = False,
):
    model_config = await get_user_model(
        current_user=current_user,
        use_case=UseCase.JUDGE_WEB_USEFUL.value
    )
    logger.info(f"材料{config_response.id}: 开始生成初始网络搜索查询关联词列表")
    # 获得初始的google查询关键词列表
    initial_keywords_list = await generate_search_queries(
        title=config_response.name,
        api_key=model_config.api_key,
        api_url=model_config.api_url,
        model=model_config.model_name,
        search_engine=config_response.search_engine
    )
    if not initial_keywords_list:
        error_msg = f"{ProjectReportError.NOT_KEY_WORDS.value}"
        await wrapper_error_callback(error_msg)
        raise Exception(error_msg)
  
    # 打印关键词列表
    queries_list = '\n'.join([f"- {query}" for query in initial_keywords_list])
    progress_msg = f"{ProjectReportError.KEY_WORDS_LIST.value}：\n\n{queries_list}\n\n"
    logger.info(progress_msg)

    # 已经迭代的次数
    iteration = 0
    # 最大迭代次数
    # max_iterations = settings.RESEARCH_ITERATION_LIMIT 
    max_iterations = config_response.search_iterations
    # 添加最大搜索查询次数限制
    #max_search_queries = settings.MAX_SEARCH_QUERIES  # 使用配置的最大搜索查询数
    max_search_queries = config_response.retrieval_content_count
    # max_contexts = settings.MAX_LITERATURE_AND_CONTEXTS if open_literature_summary else settings.MAX_CONTEXTS  # 使用配置的最大上下文数量
    max_contexts = config_response.max_reference_count if open_literature_summary else config_response.max_content_collection_count
    # 已经处理的关键词个数
    total_processed_queries = 0
    # 所有的关键词列表
    keywords_list = initial_keywords_list.copy()
    # 关键词的计位数字
    keyword_index = 0
    while iteration < max_iterations:
        # 检查是否已处理所有查询或达到查询上限
        remaining_queries = keywords_list[keyword_index:]
        if not remaining_queries or total_processed_queries >= max_search_queries:
            done_msg = "✅ 所有查询已处理完毕或达到查询上限\n\n"
            logger.info(done_msg)
            break
            
        # 处理每个查询，但确保不超过最大查询次数
        for query in remaining_queries:
            # 检查是否达到查询上限
            if total_processed_queries >= max_search_queries:
                limit_msg = f"⚠️ 已达到查询上限({max_search_queries}个)，停止搜索\n\n"
                logger.info(limit_msg)
                # yield f"data: {json.dumps({'content': limit_msg, 'type': 'warning'})}\n\n".encode('utf-8')
                break
                
            search_msg = f"正在搜索关键词: \"{query}\"\n\n"
            logger.info(search_msg)
            # yield f"data: {json.dumps({'content': search_msg, 'type': 'status'})}\n\n".encode('utf-8')
            
            # 处理搜索查询
            search_engine_site_url = ""
            if search_method_flag:
                result = await get_one_dict_by_category_and_value(
                    category="参考文献库",
                    value=search_method_flag
                )
                if result:
                    search_engine_site_url = result.remark
            contexts = await article_process_search_query(
                title=config_response.name,
                query=query,
                limit=config_response.search_list_count,
                research_id=str(research.id),
                api_key=model_config.api_key,
                api_url=model_config.api_url,
                model=model_config.model_name,
                is_summary_literature=open_literature_summary,
                search_method_flag=search_method_flag,
                search_engine_site_url=search_engine_site_url,
                search_engine=config_response.search_engine,
                config_response=config_response
            )
            logger.info(f"关键词：【{query}】的上下文收集完毕")
            keyword_index += 1
            # 增加迭代计数和处理查询计数
            research.iterations += 1
            research.contexts += contexts
            total_processed_queries += 1
            await research.save()
            
            # 如果开启了有用的context总结文献的话，
            # 就context的长度为有用的context加上文献条数，
            # 否则仅是有用的context长度
            reference_length = await ArticleLiterature.filter(research_id=research.id).count()
            other_length = await get_context_length(research_id=research.id)
            context_length = len(research.contexts) if not open_literature_summary else reference_length + other_length
            
            # 检查上下文数量，如果已经收集足够多的上下文，则提前结束
            if context_length >= max_contexts:  # 使用配置的最大上下文数量
                enough_msg = f"✅ 已收集足够的信息（{len(research.contexts)}条上下文）\n\n（318）"
                logger.info(enough_msg)
                # yield f"data: {json.dumps({'content': enough_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
            # 报告进度
            if contexts:
                success_msg = f"✅ 已找到 {len(contexts)} 条相关信息\n\n"
                logger.info(success_msg)
                # yield f"data: {json.dumps({'content': success_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            else:
                warning_msg = "⚠️ 未找到相关信息\n\n"
                logger.info(warning_msg)
                # yield f"data: {json.dumps({'content': warning_msg, 'type': 'warning'})}\n\n".encode('utf-8')
        # 如果开启了有用的context总结文献的话，
        # 就context的长度为有用的context加上文献条数，
        # 否则仅是有用的context长度
        reference_length = await ArticleLiterature.filter(research_id=research.id).count()
        other_length = await get_context_length(research_id=research.id)
        context_length = len(research.contexts) if not open_literature_summary else reference_length + other_length
        
        # 检查上下文数量，如果已经收集足够多的上下文，则提前结束
        if context_length >= max_contexts:  # 使用配置的最大上下文数量
            enough_msg = f"✅ 已收集足够的信息：（{context_length}条背景信息（文献加context））max_contexts：{max_contexts}\n\n（334）"
            logger.info(enough_msg)
            # yield f"data: {json.dumps({'content': enough_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            break
        
        # 分析已收集的信息并获取新的搜索查询
        analyzing_msg = "正在分析已收集的信息...\n\n"
        logger.info(analyzing_msg)
        # yield f"data: {json.dumps({'content': analyzing_msg, 'type': 'status'})}\n\n".encode('utf-8')
        
        research.status = ResearchStatus.ANALYZING
        await research.save()
        
        try:
            new_queries = await get_new_search_queries(
                research=research,
                api_key=model_config.api_key,
                api_url=model_config.api_url,
                model=model_config.model_name,
                max_contexts=max_contexts
            )
            
            # 如果不需要更多查询，退出循环
            if new_queries is None:
                complete_msg = "✅ 已收集足够的信息\n\n（357）"
                logger.info(complete_msg)
                # yield f"data: {json.dumps({'content': complete_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
            
            # 更新搜索查询并继续
            if new_queries and len(new_queries) > 0:
                # 限制新增查询数量，避免无限增长
                new_queries = new_queries[:5]  # 每轮最多添加5个新查询
                research.search_queries = research.search_queries + new_queries
                research.status = ResearchStatus.SEARCHING
                await research.save()
                
                new_queries_list = '\n'.join([f"- {query}" for query in new_queries])
                new_queries_msg = f"已生成新的搜索查询：\n\n{new_queries_list}\n\n"
                logger.info(new_queries_msg)
                # yield f"data: {json.dumps({'content': new_queries_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            else:
                # 无新查询，退出循环
                done_msg = "✅ 搜索完成，未生成新的查询\n\n"
                logger.info(done_msg)
                # yield f"data: {json.dumps({'content': done_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
        except Exception as e:
            error_msg = f"⚠️ 生成新查询时出错: {str(e)}\n\n"
            # yield f"data: {json.dumps({'content': error_msg, 'type': 'error'})}\n\n".encode('utf-8')
            logger.error(f"研究 {research.id}: 生成新查询时出错: {str(e)}")
            # 出错时也要继续，避免整个过程中断
            break
        
        iteration += 1
    


async def article_process_search_query(
    title: str,
    query: str,
    research_id: str,
    api_key: str = "",
    api_url: str = "",
    model: str = "",
    limit: int = settings.SEARCH_RESULTS_LIMIT,
    is_summary_literature = False,
    search_method_flag: Optional[str] = None,
    search_engine_site_url: Optional[str] = None,
    search_engine: Optional[str] = None,
    config_response: ArticleGenerationConfig = None
) -> List[str]:
    """
    处理单个搜索查询，搜索并处理结果
    
    Args:
        query: 搜索查询
        limit: 结果数量限制
    
    Returns:
        收集的上下文列表
    """
    logger.info(f"研究【{title}】: 处理搜索查询: {query}")
    logger.info(f"研究【{title}】: 模型参数: search_method_flag: {search_method_flag} api_key: {api_key} api_url: {api_url} model: {model}")
    # urls = await search_service.perform_google_search(query, limit=limit)
    urls = []
    contain_origin = True
    #选择参考文献走 指定网站检索和Pubmed检索，否者走谷歌检索
    if search_method_flag:
        # 从dictionary表中查询literature_library对应的值  
        if search_method_flag == LiteratureLibraryType.PUBMED.value:
             #获取URLS
            logger.info(f"pubmed搜索查询: {search_method_flag} , query: {query}")          
            urls = await search_service.search_pubmed(query, limit=limit)
        else:
            #获取URLS
            logger.info(f"scholar搜索查询: {search_method_flag} , query: {query}")      
            logger.info(f"scholar搜索查询: {search_method_flag} , query: {query} search_engine_site_url: {search_engine_site_url} contain_origin: {contain_origin}")      
            urls = await search_service.perform_serpapi_search(
                query=query,
                limit=limit,
                contain_origin=contain_origin,
                search_engine="google_scholar",
                site_url=search_engine_site_url
            )
    else:
        logger.info(f"谷歌搜索查询: {settings.SEARCH_ENGINE} , query: {query}")
        urls = await search_service.perform_serpapi_search(
            query=query,
            limit=limit,
            contain_origin=contain_origin,
            search_engine = search_engine
        )
        
    logger.info(f"研究【{title}】: 搜索返回 {len(urls)} 个结果")
    if not urls:
        logger.warning(f"研究 【{title}】: 搜索无结果: {query}")
        return []
    
    logger.info(f"研究 【{title}】: 搜索返回 {len(urls)} 个结果")
    # 收集的上下文
    contexts = []
    
    # 并发处理URL
    tasks = [process_url(
        title=title,
        url=url,
        research_id=research_id,
        search_query=query,
        api_key=api_key,
        api_url=api_url,
        model=model,
        is_summary_literature=is_summary_literature,
        config_response=config_response
    ) for url in urls]
    resources = await asyncio.gather(*tasks)
    
    # 收集成功提取的上下文
    for resource in resources:
        if resource:
            contexts.append(resource)
    
    logger.info(f"研究 【{title}】: 查询 '{query}' 处理完成，收集了 {len(contexts)} 个上下文")
    return contexts


async def process_url(
    title: str,
    url: any,
    research_id: str,
    search_query: str,
    api_key: str = "",
    api_url: str = "",
    model: str = "",
    # 是否开启有用context总结文献的功能
    is_summary_literature = False,
    config_response: ArticleGenerationConfig = None
) -> Optional[str]:
    """
    处理单个URL：获取内容，判断有用性，提取上下文
    
    Args:
        url: 网页URL
        search_query: 用于获取此URL的搜索查询
    
    Returns:
        处理后的资源对象，如果处理失败则返回None
    """
    origin = url["origin"] if isinstance(url, dict) else ""
    url = url["url"] if isinstance(url, dict) else url
    logger.info(f"研究【{title}】: 处理URL, 开始获取页面内容: {url}")
    # 获取网页内容
    page_content = await search_service.fetch_webpage_text_async(url)
    if not page_content:
        logger.warning(f"研究【{title}】: 无法获取页面内容: {url}")
        return None
    
    # 清理页面内容，移除无效的UTF-8字符（尤其是空字节0x00）
    if page_content:
        # 移除控制字符（包括空字节），但保留基本格式（如换行符等）
        page_content = ''.join(char for char in page_content if char == '\n' or char == '\t' or char >= ' ')
        # 额外处理：确保有效的UTF-8
        page_content = page_content.encode('utf-8', 'ignore').decode('utf-8', 'ignore')
    
    # 判断有用性
    logger.info(f"研究【{title}】: 评估页面有用性: {url}")
    try:
        is_useful = await is_page_useful(
            title,
            page_content,
            url,
            api_key,
            api_url,
            model
        )
    except Exception as e:
        print(f"is_page_useful评估页面有用性失败: {str(e)}")
        logger.info("is_page_useful评估页面有用性失败")
        return None
    
    try:
    # 如果有用，提取上下文
        if is_useful:
            logger.info(f"研究 【{title}】: 页面有用，提取上下文: {url}")
            logger.info(f"研究 【{title}】: api_key: {api_key}, api_url: {api_url}, model: {model}")
            context = await extract_relevant_context(
                title,
                search_query,
                page_content,
                api_key,
                api_url,
                model
            )
            if context:
                if is_summary_literature:
                    reference_data = None
                    try:
                        reference_data = await is_valid_reference(
                            page_content,
                            url,
                            api_key,
                            api_url,
                            model,
                            origin["title"]
                        )
                    except Exception as e:
                        logger.error(f"is_valid_reference调用报错：{str(e)}")
                    logger.info(f"reference_data:{reference_data}")
                    literature_list = await get_article_literature_by_id(research_id)
                    if reference_data and len(literature_list) < config_response.max_reference_count:
                        logger.info(f"article literature已经收集了{len(literature_list)}篇")
                        reference_data["research_id"] = research_id  # 改为article_config_id
                        create_data = ArticleLiteratureCreate.model_validate(reference_data, from_attributes=True)
                        print("reference_data_research_id:", create_data.article_config_id)
                        await create_article_literature(create_data)
                        return None
                    else:
                        logger.info(f"没有总结出文献的context内容：\n{context}")
                        logger.info(f"没有总结出文献的serpAPI返回的内容：\n{str(origin)}")
                        logger.info(f"没有总结出文献的网页地址：{url}")
                        return context
                logger.info(f"研究 【{title}】: 已将提取的上下文添加到研究中")
                # 将上下文添加到研究中
                return context
        else:
            logger.info(f"研究【{title}】: 页面不相关，跳过: {url}")
            return None
    except Exception as e:
        logger.error(f"研究  【{title}】: 处理URL时出错: {str(e)}")
        return None
  

async def article_stream_llm_and_save(
    messages: List[Dict[str, str]],
    apiKey,
    apiUrl,
    model,
    user: UserResponse,
    flag: str,
    callback: Callable[[str], None],
    complete_callback: Callable[[str], None],
    error_callback: Callable[[str], None],
    related_id: Optional[UUID4] = None, # type: ignore
    product_type = ProductType.DOCGEN.value,
    config_response: Optional[ArticleGenerationConfig] = None
) -> str:
    """
    流式调用LLM，实时保存响应内容到文件，并返回完整响应
    
    Args:
        messages: 聊天消息列表
        model: 模型名称
        callback: 可选的回调函数，每接收到一个chunk就调用一次
        complete_callback: 可选的完成回调函数，生成完成时调用
        config_response: 文章生成配置对象
    
    Returns:
        完整的模型响应文本
    """
    query = {
        "url": apiUrl
    }
    logger.info(f"流式调用LLM并保存到文件，模型: {model}")
    if not apiKey:
        err_msg = "未设置API_KEY环境变量"
        logger.error(err_msg)
        error_callback and await error_callback(err_msg)
        # raise ValueError("未设置API_KEY环境变量")
    logger.info(f"开始请求API: {model}")
    headers = {
        "X-Title": "Hi-IdeaGen",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model,
        "messages": messages,
        "stream": True,
        "temperature": float(config_response.temperature) if config_response.temperature is not None else 0.7,
        "top_k": config_response.top_k if config_response.top_k is not None else 50,
        "top_p": float(config_response.top_p) if config_response.top_p is not None else 0.9,
        # "provider": {
        #     "order": [
        #         "anthropic"
        #     ]
        # }
    }
    is_anthropic = is_anthropic_provider(api_url=apiUrl)
    headers = headers|set_auth_data(is_anthropic=is_anthropic, apiKey=apiKey)
    if is_anthropic:
        # anthropic需要配置代理才可以访问
        proxy_data = get_proxy()
        query = query | proxy_data
        payload = payload|set_anthropic_message(messages=messages)
        
    
    logger.info(f"大模型流式请求的参数:{str(payload)}")
    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"流式LLM请求，超时设置: {timeout.total}秒,温度: {settings.LLM_DEFAULT_TEMPERATURE},上采样top_k: {settings.LLM_DEFAULT_TOP_K},上采样top_p: {settings.LLM_DEFAULT_TOP_P}")
    
    try:
        llm_log = await create_llm_call_log(
            current_user=user,
            model_name=model,
            model_api_key=apiKey,
            model_api_url=apiUrl,
            response="",
            messages=messages,
            product_type=product_type,
            related_id=related_id,
            flag=flag
        )
    except Exception as e:
        logger.error(f"创建大模型调用日志失败：{str(e)}")
    query["headers"] = headers
    query["timeout"] = timeout
    query["json"] = payload
    try:
        full_response = ""
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送流式请求: {settings.OPENROUTER_URL}")
            async with session.post(
                **query
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    error_msg = f"模型名称:{model},apiUrl: {apiUrl} 错误: 状态码 {resp.status}, 错误详情: {error_text}"
                    logger.error(error_msg)
                    error_callback and await error_callback(error_msg)
                    return f"模型名称:{model},apiUrl: {apiUrl} 请求错误: {error_msg}"
                
                # 流式响应并写入文件
                logger.debug(f"开始接收流式LLM响应并写入文件")
                chunk_count = 0
                open_router_id: Optional[str] = None
                # 结束原因：
                stop_reason = {
                    "finish_reason": "",
                    "native_finish_reason": ""
                }
                # token消耗
                token_consumed = {
                    "input": "",
                    "output": ""
                }
                # 使用追加模式写入文件
                try:
                    if not is_anthropic:
                        async for line in resp.content:
                            try:
                                line_str = line.decode('utf-8').strip()
                                if not line_str:
                                    continue
                                
                                # 跳过 "data: " 前缀
                                if line_str.startswith("data: "):
                                    line_str = line_str[6:]
                                
                                # 处理流结束标记
                                if line_str == "[DONE]":
                                    logger.debug("流式响应结束")
                                    break
                                # 解析 JSON 数据
                                try:
                                    data = json.loads(line_str)
                                    open_router_id = data.get("id")
                                    # print(open_router_id)
                                    # 提取内容增量
                                    if 'choices' in data and len(data['choices']) > 0:
                                        delta = data['choices'][0].get('delta', {})
                                        if 'content' in delta and delta['content']:
                                            chunk = delta['content']

                                            # is_not_empty = chunk.strip() and not chunk.isspace()
                                            # 只有当chunk包含非空白字符时，才将其拼接到full_response
                                            if chunk:
                                                full_response += chunk
                                            logger.info(f"LLM返回的文字：{chunk[:100]}")
                                            # 如果有回调函数，则调用
                                            if callback and chunk:
                                                await callback(chunk)
                                            
                                            chunk_count += 1
                                        finish_reason = data['choices'][0].get('finish_reason', "")
                                        native_finish_reason = data['choices'][0].get('native_finish_reason', "")
                                        if finish_reason:
                                            stop_reason["finish_reason"] = finish_reason
                                        if native_finish_reason:
                                            stop_reason["native_finish_reason"] = native_finish_reason
                                    if 'usage' in data and data["usage"]:
                                        token_consumed["input"] = data["usage"]["prompt_tokens"]
                                        token_consumed["output"] = data["usage"]["completion_tokens"]
                                except json.JSONDecodeError as je:
                                    logger.error(f"无法解析 JSON '{line_str[:100]}...': {str(je)}")
                                except Exception as e:
                                    logger.error(f"处理响应数据时出错: {str(e)}, 数据: {line_str[:100]}...")
                            except UnicodeDecodeError as ue:
                                logger.error(f"解码响应时出错: {str(ue)}")
                            except Exception as e:
                                logger.error(f"处理响应行时出错: {str(e)}")
                    else:
                        async for line in resp.content:
                            line_str = line.decode('utf-8').strip()
                            if not line_str:
                                continue
                            # 跳过 "data: " 前缀
                            if line_str.startswith("data: "):
                                line_str = line_str[6:]
                                logger.info(f"流式响应数据：{line_str}")
                            if line_str == "event: content_block_stop":
                                logger.debug("流式响应结束")
                                continue
                            if line_str.startswith('data: {"type":"message_delta"'):
                                logger.debug("流式响应结束")
                                end_data = json.loads(line_str[6:])
                                finish_reason = end_data['delta'].get('stop_reason', "")
                                if finish_reason:
                                    stop_reason["finish_reason"] = finish_reason
                                if finish_reason:
                                    stop_reason["native_finish_reason"] = finish_reason
                                if 'usage' in end_data and end_data["usage"]:
                                    token_consumed["input"] = end_data["usage"]["input_tokens"]
                                    token_consumed["output"] = end_data["usage"]["output_tokens"]
                                break   
                            try:
                                data = json.loads(line_str)
                                # 提取内容增量
                                if 'delta' in data and data['delta']['text']:
                                    delta = data['delta'].get('text', "")
                                    chunk = delta

                                    # is_not_empty = chunk.strip() and not chunk.isspace()
                                    # 只有当chunk包含非空白字符时，才将其拼接到full_response
                                    if chunk:
                                        full_response += chunk
                                    logger.info(f"LLM返回的文字：{chunk[:100]}")
                                    # 如果有回调函数，则调用
                                    if callback and chunk:
                                        await callback(chunk)
                                    
                                    chunk_count += 1
                            except json.JSONDecodeError as je:
                                logger.error(f"无法解析 JSON '{line_str[:100]}...': {str(je)}")
                            except Exception as e:
                                logger.error(f"处理响应数据时出错: {str(e)}, 数据: {line_str[:100]}...")
                                        
                except Exception as e:
                    logger.error(f"读取响应流时出错: {str(e)}")
                
                logger.info(f"流式响应完成，接收了 {chunk_count} 个数据块，总长度: {len(full_response)}")
                
                # 如果没有收到任何内容，记录更多的调试信息
                if not full_response:
                    result = f"没有接收到有效内容"
                    logger.warning(result)
                    if error_callback:
                        await error_callback(result)
                    # 写入一些信息到文件，表明请求成功但没有收到内容
                    return f"请求完成，但未收到有效内容"
                
                # 调用完成回调
                if complete_callback:
                    try:
                        logger.info("LLM流式已经完成了。现在调用complete_callback函数")
                        logger.info(f"报告{related_id}的结束原因：{str(stop_reason)},token消耗：{str(token_consumed)}")
                        await complete_callback(open_router_id, full_response)
                        await update_llm_call(
                            log_id=llm_log.id,
                            response=full_response
                        )
                    except Exception as e:
                        logger.error(f"执行完成回调时出错: {str(e)}")
                
                return full_response
                    
    except asyncio.TimeoutError as te:
        error_msg = f"模型名称:{model},apiUrl: {apiUrl} 请求超时 ({timeout.total} 秒)"
        logger.error(error_msg)
        return f"请求超时: {error_msg}"
    except aiohttp.ClientError as ce:
        error_msg = f"HTTP客户端错误: {str(ce)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"调用OpenRouter时发生错误: {str(e)}"
        logger.error(error_msg)
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        return error_msg 
    except asyncio.CancelledError:
        logger.warning("请求被取消")
        return "请求被取消"

def extract_and_remove_system(messages: List[dict]):
    """
    从 messages 中移除 role=system 的项，
    返回 (新的 messages, system_contents)
    """
    logger.info(messages)
    system_contents = [m["content"] for m in messages if m.get("role") == "system"]
    new_messages = [m for m in messages if m.get("role") != "system"]
    return new_messages, system_contents[0] if len(system_contents) > 0 else None

def is_anthropic_provider(api_url:str):
    return "api.anthropic.com" in api_url

def get_proxy():
    query = {}
    proxy = settings.PROXY_URL
    proxy_auth_username = settings.PROXY_USERNAME
    proxy_auth_password = settings.PROXY_PASSWORD
    # 如果配置了代理服务器信息
    if proxy and proxy_auth_password and proxy_auth_username:
        # aiohttp 需要 proxy headers 来传递 Basic Auth
        credentials = f"{proxy_auth_username}:{proxy_auth_password}"
        encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
        proxy_headers = {
            "Proxy-Authorization": f"Basic {encoded_credentials}"
        }
        query["proxy"] = proxy
        query["proxy_headers"] = proxy_headers
    return query

def set_auth_data(
    is_anthropic:bool,
    apiKey: str
):
    headers = {}
    if is_anthropic:
        headers["X-Api-Key"] = f"{apiKey}"
        headers["Anthropic-Version"] = "2023-06-01"
        # anthropic的系统提示词写法与众不同
    else:
        headers["Authorization"] = f"Bearer {apiKey}"
    return headers

def set_anthropic_message(
    messages: List[dict]
):
    payload = {}
    messages_list, system_prompt = extract_and_remove_system(messages=messages)
    payload["messages"] = messages_list
    if system_prompt:
        payload["system"] = system_prompt
    payload["max_tokens"] = settings.ANTHROPIC_MAX_TOKEN
    return payload
  
