import os
import time
from pydantic import UUID4
from typing import List, Optional
from fastapi import UploadFile
from app.models.upload_file import UploadFile as UploadFileModel
from app.core.logging import get_logger
from datetime import datetime
from app.utils.enum import UploadFileError
from app.api.schemas.upload_file import UploadFileResponse

logger = get_logger(__name__)

UPLOAD_DIR = 'attachments'  # 可根据实际需要调整

async def save_upload_file(file: UploadFile) -> UploadFileResponse:
    """
    上传文件并保存到 upload_files 表，文件名用时间戳覆盖。
    :param file: FastAPI 的 UploadFile 对象
    :return: 保存后的文件信息字典
    """
    date_str = datetime.now().strftime("%Y%m%d")
    document_path = f"{UPLOAD_DIR}/{date_str}"
    # 确保上传目录存在
    if not os.path.exists(document_path):
        os.makedirs(document_path)
    
    # 生成新文件名：时间戳+原后缀
    ext = os.path.splitext(file.filename)[1].lower()
    timestamp = int(time.time() * 1000)
    new_filename = f"{timestamp}{ext}"
    file_path = f"{document_path}/{new_filename}"

    # 获取绝对路径
    abs_path = os.path.join(os.getcwd(), file_path)
    # 保存文件到本地
    try:
        with open(abs_path, "wb") as f:
            content = await file.read()
            f.write(content)
        logger.info(f"文件已保存到: {file_path}")
    except Exception as e:
        error_msg = f"{UploadFileError.SAVE_FILE_FAIL.value}: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)

    # 保存数据库记录
    try:
        upload_file = await UploadFileModel.create(
            file_path=file_path,
            file_name=file.filename
        )
        return UploadFileResponse.model_validate(upload_file, from_attributes=True)
    except Exception as e:
        error_msg = f"{UploadFileError.SAVE_DB_FAIL.value}: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)
async def create_upload_file(
    file_path: str,
    file_name: Optional[str] = None
) -> UploadFileResponse:
    """根据相对路径地址生成上传文件记录"""
    filename = file_name or os.path.basename(file_path)
    # 保存数据库记录
    try:
        upload_file = await UploadFileModel.create(
            file_path=file_path,
            file_name=filename
        )
        return UploadFileResponse.model_validate(upload_file, from_attributes=True)
    except Exception as e:
        error_msg = f"{UploadFileError.SAVE_DB_FAIL.value}: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)

async def get_file_content_by_id(file_id: str) -> UploadFileResponse:
    """
    根据文件id获取文件记录。
    :param file_id: 文件的UUID
    :return: 文件内容字符串
    """
    try:
        upload_file = await UploadFileModel.filter(
          id=file_id,
          is_deleted=False
        ).first()
        if not upload_file:
          raise Exception(f"{UploadFileError.NOT_RECORD.value}")
        return UploadFileResponse.model_validate(upload_file, from_attributes=True)
    except Exception as e:
        error_msg = f"{UploadFileError.EXCEPTION_WHEN_QUERY.value}: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)
async def get_file_content_by_ids(file_ids: List[str]) -> List[UploadFileResponse]:
    """
    根据文件ids获取文件列表。
    :param file_id: 文件的UUID
    :return: 文件内容字符串
    """
    try:
        upload_file = await UploadFileModel.filter(
          id__in=file_ids,
          is_deleted=False
        ).all()
        if not upload_file:
          raise Exception(f"{UploadFileError.NOT_RECORD.value}")
        return [UploadFileResponse.model_validate(item, from_attributes=True) for item in upload_file]
    except Exception as e:
        error_msg = f"{UploadFileError.EXCEPTION_WHEN_QUERY.value}: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)