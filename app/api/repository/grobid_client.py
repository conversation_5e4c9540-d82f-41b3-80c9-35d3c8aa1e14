"""
GROBID服务客户端 - 使用grobid_client_python包
用于调用GROBID API解析PDF文献元数据
"""

import asyncio
import tempfile
import os
from typing import Optional, Dict, Any, List
import xml.etree.ElementTree as ET
from pathlib import Path
from loguru import logger

from app.core.config import settings
from grobid_client.grobid_client import GrobidClient as GrobidPythonClient


class GrobidClient:
    """GROBID客户端 - 使用grobid_client_python包"""

    def __init__(self, base_url: Optional[str] = None):
        """
        初始化GROBID客户端

        Args:
            base_url: GROBID服务地址，如果为None则从配置中读取
        """
        if base_url is None:
            base_url = settings.GROBID_BASE_URL
        self.base_url = base_url.rstrip('/')

        # 初始化grobid_client_python客户端
        self.client = GrobidPythonClient(
            grobid_server=self.base_url,
            batch_size=1000,
            sleep_time=5,
            timeout=120,
            coordinates=["persName", "figure", "ref", "biblStruct", "formula", "s"],
            check_server=False  # 不在初始化时检查服务器
        )

    async def parse_pdf_header(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """
        解析PDF头部信息（标题、作者、摘要等）

        Args:
            pdf_path: PDF文件路径

        Returns:
            解析结果字典，包含标题、作者、摘要等信息
        """
        try:
            # 创建临时目录用于输出
            with tempfile.TemporaryDirectory() as temp_dir:
                # 使用grobid_client_python处理文档
                result = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self._process_header,
                    pdf_path,
                    temp_dir
                )
                return result
        except Exception as e:
            logger.error(f"Error processing PDF header: {str(e)}")
            return None

    def _process_header(self, pdf_path: str, output_dir: str) -> Optional[Dict[str, Any]]:
        """同步处理PDF头部信息"""
        try:
            # 确保输入文件存在
            if not os.path.exists(pdf_path):
                logger.error(f"PDF file not found: {pdf_path}")
                return None

            # 使用processHeaderDocument服务
            self.client.process(
                service="processHeaderDocument",
                input_path=os.path.dirname(pdf_path),
                output=output_dir,
                n=1,  # 单文件处理
                force=True,
                tei_coordinates=True
            )

            # 查找生成的XML文件
            output_files = list(Path(output_dir).glob("*.tei.xml"))
            if not output_files:
                logger.error("No TEI XML output file generated")
                return None

            # 读取并解析XML内容
            xml_file = output_files[0]
            with open(xml_file, 'r', encoding='utf-8') as f:
                xml_content = f.read()

            return self._parse_header_xml(xml_content)

        except Exception as e:
            logger.error(f"Error in _process_header: {str(e)}")
            return None

    async def parse_pdf_fulltext(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """
        解析PDF全文

        Args:
            pdf_path: PDF文件路径

        Returns:
            解析结果字典，包含全文结构化信息
        """
        try:
            # 创建临时目录用于输出
            with tempfile.TemporaryDirectory() as temp_dir:
                # 使用grobid_client_python处理文档
                result = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self._process_fulltext,
                    pdf_path,
                    temp_dir
                )
                return result
        except Exception as e:
            logger.error(f"Error processing PDF fulltext: {str(e)}")
            return None

    def _process_fulltext(self, pdf_path: str, output_dir: str) -> Optional[Dict[str, Any]]:
        """同步处理PDF全文"""
        try:
            # 确保输入文件存在
            if not os.path.exists(pdf_path):
                logger.error(f"PDF file not found: {pdf_path}")
                return None

            # 使用processFulltextDocument服务
            self.client.process(
                service="processFulltextDocument",
                input_path=os.path.dirname(pdf_path),
                output=output_dir,
                n=1,  # 单文件处理
                force=True,
                tei_coordinates=True
            )

            # 查找生成的XML文件
            output_files = list(Path(output_dir).glob("*.tei.xml"))
            if not output_files:
                logger.error("No TEI XML output file generated")
                return None

            # 读取并解析XML内容
            xml_file = output_files[0]
            with open(xml_file, 'r', encoding='utf-8') as f:
                xml_content = f.read()

            return self._parse_fulltext_xml(xml_content)

        except Exception as e:
            logger.error(f"Error in _process_fulltext: {str(e)}")
            return None

    async def health_check(self) -> bool:
        """检查GROBID服务是否可用"""
        try:
            # 使用grobid_client_python的健康检查
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                self.client.ping
            )
            return result
        except Exception as e:
            logger.error(f"GROBID health check failed: {str(e)}")
            return False

    def _parse_header_xml(self, xml_content: str) -> Dict[str, Any]:
        """解析头部XML内容"""
        try:
            root = ET.fromstring(xml_content)

            # 定义命名空间
            ns = {'tei': 'http://www.tei-c.org/ns/1.0'}

            result = {
                'title': None,
                'authors': [],
                'abstract': None,
                'keywords': [],
                'doi': None,
                'publication_date': None,
                'journal': None
            }

            # 提取标题
            title_elem = root.find('.//tei:title[@type="main"]', ns)
            if title_elem is not None and title_elem.text:
                result['title'] = title_elem.text.strip()

            # 提取作者
            authors = root.findall('.//tei:author', ns)
            for author in authors:
                author_info = {}

                # 姓名
                forename = author.find('.//tei:forename[@type="first"]', ns)
                surname = author.find('.//tei:surname', ns)

                if forename is not None and surname is not None:
                    author_info['name'] = f"{forename.text} {surname.text}".strip()
                elif surname is not None:
                    author_info['name'] = surname.text.strip()

                # 机构
                affiliation = author.find('.//tei:orgName[@type="institution"]', ns)
                if affiliation is not None:
                    author_info['affiliation'] = affiliation.text.strip()

                if author_info:
                    result['authors'].append(author_info)

            # 提取摘要
            abstract_elem = root.find('.//tei:abstract', ns)
            if abstract_elem is not None:
                # 获取所有段落文本
                abstract_text = []
                for p in abstract_elem.findall('.//tei:p', ns):
                    if p.text:
                        abstract_text.append(p.text.strip())
                result['abstract'] = ' '.join(abstract_text)

            # 提取DOI
            doi_elem = root.find('.//tei:idno[@type="DOI"]', ns)
            if doi_elem is not None and doi_elem.text:
                result['doi'] = doi_elem.text.strip()

            # 提取关键词
            keywords = root.findall('.//tei:term', ns)
            for kw in keywords:
                if kw.text:
                    result['keywords'].append(kw.text.strip())

            # 提取发表日期
            date_info = self._extract_date_info(root, ns)
            if date_info:
                # 优先使用ISO格式日期
                if 'date_iso' in date_info:
                    result['publication_date'] = date_info['date_iso']
                elif 'year' in date_info:
                    result['publication_date'] = date_info['year']
                elif 'date_published' in date_info:
                    result['publication_date'] = date_info['date_published']

            return result

        except ET.ParseError as e:
            logger.error(f"XML parsing error: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"Error parsing GROBID XML: {str(e)}")
            return {}

    def _parse_fulltext_xml(self, xml_content: str) -> Dict[str, Any]:
        """
        解析全文XML内容
        根据GROBID文档标准解析TEI格式的全文XML
        """
        try:
            # 先解析头部信息
            header_info = self._parse_header_xml(xml_content)

            root = ET.fromstring(xml_content)
            ns = {'tei': 'http://www.tei-c.org/ns/1.0'}

            # 添加全文特有的信息
            header_info['sections'] = []
            header_info['references'] = []
            header_info['figures'] = []
            header_info['tables'] = []
            header_info['formulas'] = []

            # 解析正文内容 - 按照文档流顺序解析
            body = root.find('.//tei:body', ns)
            if body is not None:
                self._parse_body_content(body, header_info, ns)

            # 解析参考文献
            self._parse_references(root, header_info, ns)

            # 解析图表和公式
            self._parse_figures_and_formulas(root, header_info, ns)

            # 生成完整的正文内容
            self._generate_fulltext(header_info)

            return header_info

        except Exception as e:
            logger.error(f"Error parsing fulltext XML: {str(e)}")
            return self._parse_header_xml(xml_content)  # 降级到头部解析

    def _parse_body_content(self, body, result_dict: Dict[str, Any], ns: Dict[str, str]):
        """
        解析正文内容，按照文档流顺序处理各种元素
        """
        current_section = None

        for element in body:
            tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag

            if tag == 'div':
                # 处理章节
                current_section = self._parse_section(element, ns)
                if current_section:
                    result_dict['sections'].append(current_section)

            elif tag == 'head' and current_section is None:
                # 处理独立的标题
                heading_text = self._extract_text_content(element, ns)
                if heading_text:
                    current_section = {
                        'heading': heading_text,
                        'content': '',
                        'paragraphs': [],
                        'lists': [],
                        'formulas': []
                    }
                    result_dict['sections'].append(current_section)

            elif tag == 'p':
                # 处理段落
                paragraph_text = self._extract_text_content(element, ns)
                if paragraph_text:
                    if current_section is None:
                        # 创建默认章节
                        current_section = {
                            'heading': '正文',
                            'content': '',
                            'paragraphs': [],
                            'lists': [],
                            'formulas': []
                        }
                        result_dict['sections'].append(current_section)

                    current_section['paragraphs'].append(paragraph_text)
                    current_section['content'] += paragraph_text + ' '

    def _parse_section(self, section_element, ns: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """解析章节内容"""
        section_info = {
            'heading': '',
            'content': '',
            'paragraphs': [],
            'lists': [],
            'formulas': []
        }

        # 提取章节标题
        head = section_element.find('.//tei:head', ns)
        if head is not None:
            section_info['heading'] = self._extract_text_content(head, ns)

        # 处理章节内的所有内容
        for element in section_element:
            tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag

            if tag == 'p':
                paragraph_text = self._extract_text_content(element, ns)
                if paragraph_text:
                    section_info['paragraphs'].append(paragraph_text)
                    section_info['content'] += paragraph_text + ' '

        return section_info if section_info['content'].strip() else None

    def _extract_text_content(self, element, ns: Dict[str, str]) -> str:
        """
        提取元素的文本内容，正确处理换行和引用标记
        """
        text_parts = []

        def process_element(elem):
            if elem.text:
                text_parts.append(elem.text)

            # 处理子元素
            for child in elem:
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag

                if child_tag == 'lb':
                    # 换行标记
                    text_parts.append('\n')
                elif child_tag == 'ref':
                    # 引用标记，保留引用内容
                    ref_text = self._extract_text_content(child, ns)
                    if ref_text:
                        text_parts.append(f"[{ref_text}]")
                else:
                    # 其他元素，递归处理
                    process_element(child)

                if child.tail:
                    text_parts.append(child.tail)

        process_element(element)
        return ''.join(text_parts).strip()

    def _parse_references(self, root, result_dict: Dict[str, Any], ns: Dict[str, str]):
        """解析参考文献"""
        # 尝试多个可能的参考文献路径
        ref_paths = [
            './/tei:back/tei:listBibl/tei:biblStruct',
            './/tei:listBibl/tei:biblStruct',
            './/tei:div[@type="references"]//tei:biblStruct'
        ]

        for path in ref_paths:
            references = root.findall(path, ns)
            if references:
                for ref in references:
                    ref_info = self._parse_reference_item(ref, ns)
                    if ref_info:
                        result_dict['references'].append(ref_info)
                break

    def _parse_reference_item(self, ref_element, ns: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """解析单个参考文献项"""
        ref_info = {
            'title': None,
            'authors': [],
            'journal': None,
            'year': None,
            'doi': None
        }

        # 提取标题
        title_elem = ref_element.find('.//tei:title[@level="a"]', ns)
        if title_elem is not None:
            ref_info['title'] = self._extract_text_content(title_elem, ns)

        # 提取作者
        authors = ref_element.findall('.//tei:author', ns)
        for author in authors:
            author_name = self._extract_author_name(author, ns)
            if author_name:
                ref_info['authors'].append(author_name)

        # 提取期刊
        journal_elem = ref_element.find('.//tei:title[@level="j"]', ns)
        if journal_elem is not None:
            ref_info['journal'] = self._extract_text_content(journal_elem, ns)

        # 提取DOI
        doi_elem = ref_element.find('.//tei:idno[@type="DOI"]', ns)
        if doi_elem is not None and doi_elem.text:
            ref_info['doi'] = doi_elem.text.strip()

        return ref_info if any(ref_info.values()) else None

    def _extract_author_name(self, author_element, ns: Dict[str, str]) -> Optional[str]:
        """提取作者姓名"""
        # 尝试不同的姓名组合方式
        forename = author_element.find('.//tei:forename', ns)
        surname = author_element.find('.//tei:surname', ns)

        if forename is not None and surname is not None:
            return f"{forename.text} {surname.text}".strip()
        elif surname is not None:
            return surname.text.strip()
        elif forename is not None:
            return forename.text.strip()

        # 如果没有找到结构化姓名，尝试直接获取文本
        author_text = self._extract_text_content(author_element, ns)
        return author_text if author_text else None

    def _extract_date_info(self, element, ns: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """
        提取日期信息，支持多种日期格式
        """
        date_info = {}

        # 查找各种日期元素
        date_elements = element.findall('.//tei:date', ns)

        for date_elem in date_elements:
            # 提取when属性（ISO格式日期）
            when = date_elem.get('when')
            if when:
                try:
                    # 解析ISO日期格式
                    if len(when) >= 4:
                        date_info['year'] = when[:4]
                    if len(when) >= 7:
                        date_info['month'] = when[5:7]
                    if len(when) >= 10:
                        date_info['day'] = when[8:10]
                    date_info['date_iso'] = when
                except:
                    pass

            # 提取文本内容（可能包含非标准格式的日期）
            date_text = self._extract_text_content(date_elem, ns)
            if date_text:
                # 尝试从文本中提取年份
                import re
                year_match = re.search(r'\b(19|20)\d{2}\b', date_text)
                if year_match and 'year' not in date_info:
                    date_info['year'] = year_match.group()

                # 保存原始日期文本
                date_info['date_published'] = date_text

        return date_info if date_info else None

    def _parse_figures_and_formulas(self, root, result_dict: Dict[str, Any], ns: Dict[str, str]):
        """解析图表和公式"""
        # 解析图表
        figures = root.findall('.//tei:figure', ns)
        for figure in figures:
            figure_info = self._parse_figure(figure, ns)
            if figure_info:
                if figure_info.get('type') == 'table':
                    result_dict['tables'].append(figure_info)
                else:
                    result_dict['figures'].append(figure_info)

    def _parse_figure(self, figure_element, ns: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """解析图表信息"""
        figure_info = {
            'type': figure_element.get('type', 'figure'),
            'title': None,
            'content': None,
            'caption': None
        }

        # 提取标题
        head = figure_element.find('.//tei:head', ns)
        if head is not None:
            figure_info['title'] = self._extract_text_content(head, ns)

        # 提取内容
        figure_content = self._extract_text_content(figure_element, ns)
        if figure_content:
            figure_info['content'] = figure_content

        return figure_info if figure_info['content'] else None

    def _generate_fulltext(self, result_dict: Dict[str, Any]):
        """
        根据解析的sections生成完整的正文内容
        """
        fulltext_parts = []

        # 添加摘要
        if result_dict.get('abstract'):
            fulltext_parts.append(f"## Abstract\n\n{result_dict['abstract']}\n\n")

        # 添加各个章节的内容
        for section in result_dict.get('sections', []):
            if section.get('heading'):
                fulltext_parts.append(f"## {section['heading']}\n\n")

            if section.get('content'):
                fulltext_parts.append(f"{section['content'].strip()}\n\n")

        # 将所有部分组合成完整的正文
        result_dict['fulltext'] = ''.join(fulltext_parts).strip()


# 创建全局实例
grobid_client = GrobidClient()


async def parse_literature_pdf(pdf_path: str) -> Optional[Dict[str, Any]]:
    """
    解析文献PDF的便捷函数

    Args:
        pdf_path: PDF文件路径

    Returns:
        解析结果字典
    """
    try:
        return await grobid_client.parse_pdf_fulltext(pdf_path)
    except Exception as e:
        logger.error(f"Error parsing literature PDF: {str(e)}")
        return await grobid_client.parse_pdf_header(pdf_path)


async def demo_parse_pdf():
    """演示解析PDF文件"""
    import sys

    if len(sys.argv) > 1:
        pdf_file = sys.argv[1]
    else:
        print("用法: python grobid_client.py <pdf_file_path>")
        return

    if not os.path.exists(pdf_file):
        print(f"文件不存在: {pdf_file}")
        return

    print(f"正在解析PDF文件: {pdf_file}")

    # 检查GROBID服务
    if not await grobid_client.health_check():
        print("GROBID服务不可用，请检查服务是否启动")
        return

    # 先解析头部信息
    print("\n=== 解析头部信息 ===")
    header_result = await grobid_client.parse_pdf_header(pdf_file)
    if header_result:
        print(f"标题: {header_result.get('title', 'N/A')}")
        print(f"作者: {[author.get('name', 'N/A') for author in header_result.get('authors', [])]}")
        print(f"摘要: {header_result.get('abstract', 'N/A')[:200]}...")
        print(f"DOI: {header_result.get('doi', 'N/A')}")
    else:
        print("头部信息解析失败")

    # 解析全文
    print("\n=== 解析全文 ===")
    fulltext_result = await grobid_client.parse_pdf_fulltext(pdf_file)
    if fulltext_result:
        print(f"章节数量: {len(fulltext_result.get('sections', []))}")
        print(f"参考文献数量: {len(fulltext_result.get('references', []))}")
        print(f"图表数量: {len(fulltext_result.get('figures', []))}")
        if fulltext_result.get('fulltext'):
            print(f"全文长度: {len(fulltext_result['fulltext'])} 字符")
    else:
        print("全文解析失败")


if __name__ == "__main__":
    asyncio.run(demo_parse_pdf())