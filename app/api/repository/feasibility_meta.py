from typing import List, Optional
from uuid import UUID
from datetime import datetime
from app.models.feasibility_meta import FeasibilityMeta
from tortoise.expressions import Q

from app.api.schemas.feasibility_meta import (
    FeasibilityMetaResponse
)
from app.core.logging import get_logger

logger = get_logger(__name__)

async def get_feasibility_meta(meta_id: str):
    """
    获取可行性研究报告元数据记录
    
    Args:
        meta_id: 可行性研究报告ID
        
    Returns:
        FeasibilityMetaResponse: 可行性研究报告响应数据
        
    Raises:
        Exception: 查询失败时抛出异常
    """
    try:
        result = await FeasibilityMeta.filter(
            id=meta_id
        ).first()
        return FeasibilityMetaResponse.model_validate(result, from_attributes=True)
    except Exception as e:
        logger.error(f"获取可行性研究报告元数据记录失败:{str(e)}")
        raise e
