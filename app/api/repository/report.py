from app.api.repository.feasibility_meta import get_feasibility_meta
from app.api.repository.project_config import (
    get_one_project_config,
    update_estimated_time
)
from app.api.repository.literatures import (
    get_literature_by_project,
    get_literature_length
)
import re
from app.api.schemas.literatures import LiteratureResponse
from app.api.repository.think_tank_meta import get_think_tank_meta
from app.core.logging import get_logger
from app.services.prompts import (
    get_feasibility_content_prompt,
    get_feasibility_outline_prompt,
    get_literature_review_content_prompt,
    get_literature_review_outline_prompt,
    get_market_investment_content_prompt,
    get_market_investment_outline_prompt,
    get_meeting_summary_prompt,
    get_chapter_review_prompt,
    generate_outline_prompt,
    OUTLINE_SYSTEM_PROMPT,
    generate_project_config_prompt,
    final_report_prompt,
    final_report_reference_prompt,
    final_report_prompt_system,
    get_think_tank_content_prompt,
    get_think_tank_outline_prompt,
    get_comprise_content_prompt,
    FINAL_REPORT_BACKGROUND_PROMPT,
    LITERATURE_PROMPT,
    THIS_TIME_INFO_PROMPT,
    REPORT_SYSTEM_PROMPT,
    THINK_TANK_ITER_PROMPT,
    THINK_TANK_BACKGROUND_PROMPT,
    MARKET_INVESTMENT_BACKGROUND_PROMPT,
    MARKET_INVESTMENT_ITER_PROMPT,
    FEASIBILITY_BACKGROUND_PROMPT,
    FEASIBILITY_ITER_PROMPT,
    LITERATURE_REVIEW_ITER_PROMPT,
    LITERATURE_REVIEW_BACKGROUND_PROMPT
)
from app.api.repository.project_url_summary import get_urls_by_project_id
from app.api.repository.research import get_context_length
from app.api.repository.literatures import (
  get_literature_by_id,
  get_literature_length
)
from app.api.schemas.project_members import ProjectMemberBase
from app.api.repository.dictionary import get_one_dict_by_category_and_value
from app.models.organization_model_use import UseCase
from app.api.repository.user_default_model import get_user_model
from app.models.project_member_joins import ProjectMemberJoin
from app.api.repository.upload_file import get_file_content_by_id
from app.utils.content_manager import Data
from app.models.user_report_usage import UserReportUsage
from app.api.repository.user_report_usage import check_user_usage_limit
import json
from app.models.project_configs import ProjectConfig, ProjectConfigStatus
from app.utils.llm_service import stream_llm_and_save
import asyncio
from app.api.schemas.user import UserResponse
from app.utils.enum import CallLLMFlag
from app.api.repository.voice_text import get_voice_text
from app.services.memory_storage import report_content_manager, outline_content_manager
from datetime import datetime
from app.utils.utils import (
  save_text_to_file,
  handle_before_save,
  remove_markdown_h1_and_text,
  generate_storage_path,
  stream_file_content_sse,
  docx_file_to_markdown,
  sanitize_filename
)
from app.utils.content_manager import ContentStatus
from app.api.schemas.project_configs import (
  ProjectConfigResponse2,
  UpdateEstimatedTimeProcessingMode
)
from app.services.research_service import (
    generate_search_queries,
    process_search_query,
    get_new_search_queries,
    process_url
)
from app.utils.enum import (
  DOC_TYPE,
  ProjectConfigError,
  ProjectReportError
)
from app.models.research import Research, ResearchStatus
from fastapi.responses import StreamingResponse
from starlette.background import BackgroundTask
from app.services.memory_storage import outline_content_manager, report_content_manager
from app.utils.content_manager import ContentStatus
from app.models.project_configs import ProjectConfigStatus, ProjectConfig
from datetime import datetime
from app.utils.utils import (
    save_text_to_file,
    handle_before_save,
    remove_markdown_h1_and_text,
    generate_storage_path,
    read_file_content,
    stream_handle_before_send,
    convert_markdown_to_docx,
    handle_text_before_use,
    sanitize_filename,
    remove_after_reference,
    remove_markdown_heading,
    remove_word_count_tag,
    adjust_markdown_heading_levels,
    remove_markdown_code_fence,
    remove_paragraph_bold,
    modify_series_num,
    remove_contain_outline_headings
)
from app.utils.enum import ErrorType
from app.core.logging import get_logger
from typing import Optional, Callable, List
from app.api.schemas.user import UserResponse
from app.models.project_configs import ProjectConfig
from app.api.repository.user import is_user_authed
from app.core.config import settings
from app.services.token_calculate import get_token_data, del_token_data
# 获取参考资料 - 使用新的中间表
from app.models.file_biz_relations import FileBizRelation
from app.utils.constants import ProductType
from app.services.llm_service import call_llm
from pydantic import BaseModel
from app.api.repository.dictionary import get_label_by_value

logger = get_logger(__name__)

class OutlineNode(BaseModel):
    # 纯标题不带 ###
    title: str
    # 标题后面紧邻的文本
    content: Optional[str] = ""
    # 子内容（必须是标题+正文这种形式）
    children: List["OutlineNode"] = []
    # #是1 ##是2 以此类推
    level: Optional[int] = 0
    # 计算分配该title下面要生成多少字，如果有children。那么此处会是0.
    count: int
"""搜索用户自主上传的URL链接"""
async def search_user_upload_url(
    config_response: ProjectConfigResponse2,
    research: Research,
    current_user: UserResponse,
    open_literature_summary: Optional[bool] = True
):
    try:
        model_config = await get_user_model(
            use_case=UseCase.JUDGE_WEB_USEFUL.value,
            current_user=current_user
        )
        # 搜索用户给的指定url
        logger.info(f"材料：{config_response.id}开始进行用户上传的URL的查询")
        url_list = await get_urls_by_project_id(config_response.id)
        # 总结到的参考文献会在process_url里面进行存储到数据库里面的动作。
        tasks = [process_url(
            title=config_response.name,
            url=item.url,
            research_id=str(research.id),
            search_query=config_response.name,
            api_key=model_config.api_key,
            api_url=model_config.api_url,
            model=model_config.model_name,
            is_summary_literature=open_literature_summary,
            related_id=config_response.id
        ) for item in url_list]
        resources = await asyncio.gather(*tasks)
        url_contexts = []
        # 收集成功提取的上下文
        for resource in resources:
            if resource:
                url_contexts.append(resource)
        research.contexts += url_contexts
        await research.save()
        
    except Exception as e:
        error_msg = ProjectReportError.GET_USER_ADD_URL_FAIL.value
        logger.error(error_msg + str(e))

"""申报材料网页搜索有用上下文和总结参考文献(包含了用户自主上传的文献URL)"""
async def search_to_carry_info(
    research: Research,
    config_response: ProjectConfigResponse2,
    current_user: UserResponse,
    # 搜索关键词的引擎的标识符
    search_method_flag: Optional[str] = None,
    open_literature_summary: Optional[bool] = True
):
    await del_token_data(config_response.id)
    model_conclude_useful = await get_user_model(
        current_user=current_user,
        use_case=UseCase.JUDGE_WEB_USEFUL.value
    )
    logger.info(f"材料{config_response.id}: 开始生成初始网络搜索查询关联词列表")
    # 從系統配置中動態獲取搜索引擎配置
    # from app.services.system_config_service import system_config_service
    # search_engine_config = await system_config_service.get_config("SEARCH_ENGINE")
    # if not search_engine_config:
    search_engine_config = settings.SEARCH_ENGINE
    if open_literature_summary and not ("google_scholar" in settings.SEARCH_ENGINE):
        logger.warning("开启了参考文献总结但是用的搜索引擎不是google_scholar，会导致参考文献的数量下降")
        search_engine_config = settings.LITERATURE_LIBRARY_SEARCH_ENGINE
        logger.info(f"重新将搜索引擎改为:{search_engine_config}")
    elif not open_literature_summary and settings.SEARCH_ENGINE == "google_scholar":
        logger.warning("没有开启参考文献总结但是搜索引擎用了google_scholar会导致没有搜索网页而搜索了专业数据库")
        search_engine_config = settings.WEB_SEARCH_ENGINE
        logger.info(f"重新将搜索引擎改为:{search_engine_config}")
    logger.info(f"search_engine_config: {search_engine_config}")
    # 获得初始的google查询关键词列表(使用project_configs表里面的配置模型生成搜索关键词)
    initial_keywords_list = await generate_search_queries(
        title=config_response.name,
        api_key=model_conclude_useful.api_key,
        api_url=model_conclude_useful.api_url,
        model=model_conclude_useful.model_name,
        search_engine=search_engine_config,
        related_id=config_response.id
    )
    if not initial_keywords_list:
        error_msg = f"{ProjectReportError.NOT_KEY_WORDS.value}"
        raise Exception(error_msg)

    # 打印关键词列表
    queries_list = '\n'.join([f"- {query}" for query in initial_keywords_list])
    progress_msg = f"{ProjectReportError.KEY_WORDS_LIST.value}：\n\n{queries_list}\n\n"
    logger.info(progress_msg)

    # 已经迭代的次数
    iteration = 0
    # 最大迭代次数
    max_iterations = settings.RESEARCH_ITERATION_LIMIT
    # 添加最大搜索查询次数限制
    max_search_queries = settings.MAX_SEARCH_QUERIES  # 使用配置的最大搜索查询数
    max_contexts = settings.MAX_LITERATURE_AND_CONTEXTS if open_literature_summary else settings.MAX_CONTEXTS  # 使用配置的最大上下文数量
    # 已经处理的关键词个数
    total_processed_queries = 0
    # 所有的关键词列表
    keywords_list = initial_keywords_list.copy()
    # 关键词的计位数字
    keyword_index = 0
    logger.info(f"判断有用性用的大模型是：{model_conclude_useful.model_name}")
    while iteration < max_iterations:
        # 检查是否已处理所有查询或达到查询上限
        remaining_queries = keywords_list[keyword_index:]
        if not remaining_queries or total_processed_queries >= max_search_queries:
            done_msg = "✅ 所有查询已处理完毕或达到查询上限\n\n"
            logger.info(done_msg)
            break

        # 处理每个查询，但确保不超过最大查询次数
        for query in remaining_queries:
            # 检查是否达到查询上限
            if total_processed_queries >= max_search_queries:
                limit_msg = f"⚠️ 已达到查询上限({max_search_queries}个)，停止搜索\n\n"
                logger.info(limit_msg)
                # yield f"data: {json.dumps({'content': limit_msg, 'type': 'warning'})}\n\n".encode('utf-8')
                break

            search_msg = f"正在搜索关键词: \"{query}\"\n\n"
            logger.info(search_msg)
            # yield f"data: {json.dumps({'content': search_msg, 'type': 'status'})}\n\n".encode('utf-8')

            # 处理搜索查询
            search_engine_site_url = ""
            if search_method_flag:
                result = await get_one_dict_by_category_and_value(
                    category="参考文献库",
                    value=search_method_flag
                )
                if result:
                    search_engine_site_url = result.remark
            contexts = await process_search_query(
                title=config_response.name,
                query=query,
                research_id=str(research.id),
                api_key=model_conclude_useful.api_key,
                api_url=model_conclude_useful.api_url,
                model=model_conclude_useful.model_name,
                is_summary_literature=open_literature_summary,
                search_method_flag=search_method_flag,
                search_engine_site_url=search_engine_site_url,
                related_id=config_response.id
            )
            logger.info(f"关键词：【{query}】的上下文收集完毕")
            keyword_index += 1
            # 增加迭代计数和处理查询计数
            research.iterations += 1
            research.contexts += contexts
            total_processed_queries += 1
            await research.save()

            # 如果开启了有用的context总结文献的话，
            # 就context的长度为有用的context加上文献条数，
            # 否则仅是有用的context长度
            reference_length = await get_literature_length(research_id=research.id)
            other_length = await get_context_length(research_id=research.id)
            context_length = reference_length + other_length

            # 检查上下文数量，如果已经收集足够多的上下文，则提前结束
            if context_length >= max_contexts:  # 使用配置的最大上下文数量
                enough_msg = f"✅ 已收集足够的信息（{len(research.contexts)}条上下文）\n\n（318）"
                logger.info(enough_msg)
                # yield f"data: {json.dumps({'content': enough_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
            # 报告进度
            if contexts:
                success_msg = f"✅ 已找到 {len(contexts)} 条相关信息\n\n"
                logger.info(success_msg)
                # yield f"data: {json.dumps({'content': success_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            else:
                warning_msg = "⚠️ 未找到相关信息\n\n"
                logger.info(warning_msg)
                # yield f"data: {json.dumps({'content': warning_msg, 'type': 'warning'})}\n\n".encode('utf-8')
        # 如果开启了有用的context总结文献的话，
        # 就context的长度为有用的context加上文献条数，
        # 否则仅是有用的context长度
        reference_length = await get_literature_length(research_id=research.id)
        other_length = await get_context_length(research_id=research.id)
        context_length = len(research.contexts) if not open_literature_summary else reference_length + other_length

        # 检查上下文数量，如果已经收集足够多的上下文，则提前结束
        if context_length >= max_contexts:  # 使用配置的最大上下文数量
            enough_msg = f"✅ 已收集足够的信息：（{context_length}条背景信息（文献加context））max_contexts：{max_contexts}\n\n（334）"
            logger.info(enough_msg)
            # yield f"data: {json.dumps({'content': enough_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            break

        # 分析已收集的信息并获取新的搜索查询
        analyzing_msg = "正在分析已收集的信息...\n\n"
        logger.info(analyzing_msg)
        # yield f"data: {json.dumps({'content': analyzing_msg, 'type': 'status'})}\n\n".encode('utf-8')

        research.status = ResearchStatus.ANALYZING
        await research.save()

        try:
            new_queries = await get_new_search_queries(
                research=research,
                api_key=model_conclude_useful.api_key,
                api_url=model_conclude_useful.api_url,
                model=model_conclude_useful.model_name,
                max_contexts=max_contexts,
                related_id=config_response.id
            )

            # 如果不需要更多查询，退出循环
            if new_queries is None:
                complete_msg = "✅ 已收集足够的信息\n\n（357）"
                logger.info(complete_msg)
                # yield f"data: {json.dumps({'content': complete_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break

            # 更新搜索查询并继续
            if new_queries and len(new_queries) > 0:
                # 限制新增查询数量，避免无限增长
                new_queries = new_queries[:5]  # 每轮最多添加5个新查询
                research.search_queries = research.search_queries + new_queries
                research.status = ResearchStatus.SEARCHING
                await research.save()
                
                # 增加提示词
                keywords_list = keywords_list + new_queries
                new_queries_list = '\n'.join(
                    [f"- {query}" for query in new_queries])
                new_queries_msg = f"已生成新的搜索查询：\n\n{new_queries_list}\n\n"
                logger.info(new_queries_msg)
                # yield f"data: {json.dumps({'content': new_queries_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            else:
                # 无新查询，退出循环
                done_msg = "✅ 搜索完成，未生成新的查询\n\n"
                logger.info(done_msg)
                # yield f"data: {json.dumps({'content': done_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
        except Exception as e:
            error_msg = f"⚠️ 生成新查询时出错: {str(e)}\n\n"
            # yield f"data: {json.dumps({'content': error_msg, 'type': 'error'})}\n\n".encode('utf-8')
            logger.error(f"材料{config_response.id}: 生成新查询时出错: {str(e)}")
            # 出错时也要继续，避免整个过程中断
            break

        iteration += 1
    logger.info(f"材料{config_response.id}搜索网页的内容的目前的循环次数：{iteration}")
    await search_user_upload_url(
        config_response=config_response,
        research=research,
        current_user=current_user,
        open_literature_summary=open_literature_summary
    )

"""获取上传参考资料的信息"""
async def get_additional_file_data(
    project_id: str
):    
    # 获取该项目的所有参考资料关联（只查询未删除的文件）
    reference_relations = await FileBizRelation.filter(
        product_type=ProductType.DOCGEN,
        biz_id=str(project_id),
        file__is_deleted=False  # 通过关联查询过滤已删除的文件
    ).prefetch_related("file").all()
    # 汇总所有AI分析结果
    all_analysis_results = []
    for relation in reference_relations:
        file = relation.file
        # 🔧 关键修复：添加文件存在性和软删除检查
        if not file:
            logger.warning(f"关联记录 {relation.id} 的文件已被删除，跳过处理")
            continue
        if getattr(file, 'is_deleted', False):
            logger.warning(f"关联记录 {relation.id} 的文件已被软删除，跳过处理，文件名: {file.file_name}")
            continue
        file_name = file.file_name           # 文件名称
        word_count = file.word_count         # 字数（不是file_size）
        analysis_result = relation.ai_analysis_summary  # 从关联表获取AI分析结果
        
        logger.info(f"处理参考资料文件: {file_name}, 字数: {word_count}, AI分析结果长度: {len(analysis_result) if analysis_result else 0}")
        
        if analysis_result:
            all_analysis_results.append(f"\n{analysis_result}")
    
    # 合并所有分析结果
    final_analysis_result = "\n\n".join(all_analysis_results) if all_analysis_results else ""
    logger.info(f"项目 {project_id} 的参考资料总结完成，共 {len(reference_relations)} 个文件，合并后分析结果长度: {len(final_analysis_result)}")
    return final_analysis_result

async def get_report_content(
    current_user: UserResponse,
    research: Research,
    outline: str,
    config_response: ProjectConfigResponse2,
    open_literature_summary = False
):
    """
    流式生成报告内容
    
    执行真实的搜索-总结-迭代-搜索流程，并流式返回生成结果
    """
    # 生成报告提示词
    team_members = [] if not config_response.team_members else [
      ProjectMemberBase(
        name=join.member.name,
        title=join.member.title,
        representative_works=join.member.representative_works,
        organization=join.member.organization
      )
      for join in config_response.team_members
    ]
    model_config = await get_user_model(
        current_user=current_user,
        use_case=UseCase.PROJECT_CONFIG_NEED.value
    )
    prompt = None
    try:
        # 这里面可能是science、nature、pubmed
        search_engine_list = []
        if config_response.literature_library:
            search_engine_list = config_response.literature_library.split(",")
        else:
        # 这里就是没有选择science、nature、pubmed等，直接搜索，搜索引擎是使用默认的SEARCH_ENGINE
            search_engine_list = ['']
        # 搜索参考文献库
        try:
            for search_method_flag in search_engine_list:
                await search_to_carry_info(
                    research=research,
                    current_user=current_user,
                    config_response=config_response,
                    # 搜索关键词的引擎的标识符
                    search_method_flag=search_method_flag
                )
        except Exception as e:
            logger.error(f"材料：{config_response.id}的搜索步骤出现错误跳过搜索步骤：{str(e)}")
        
        logger.info(f"材料{config_response.id}搜索网页内容结束，开始正常流式生成报告内容")
        research.status = ResearchStatus.ANALYZING
        await research.save()
        
        # 还可以添加日志记录
        logger.info(f"研究id {research.id}: 查询: {research.query}")
        logger.info(f"研究id {research.id}: 收集到 {len(research.contexts)} 条上下文")
        messages = None
        try:
            config = generate_project_config_prompt(
                name=config_response.name,
                leader=config_response.leader,
                team_members=team_members,
                word_count_requirement=config_response.word_count_requirement or 0,
                team_introduction=config_response.team_introduction or "",
                flag='REPORT',
                leader_introduction=config_response.ai_leader_introduction
            )
            participants = "、".join([
                f"{item.name}{item.title or ''}" + 
                (f"，就职于{item.organization}" if item.organization else "") + 
                (f"，{item.education + '学历'}" if item.education else "") + 
                (f"，代表性成就有{item.representative_works}" if item.representative_works else "")
                for item in team_members
            ])
            main = "" if not config_response.leader else f"{config_response.leader.name}, institution established date {config_response.leader.founded_date}, related projects: {config_response.leader.related_projects}"
            literatures = await get_literature_by_id(research.id)

            additional_data = await get_additional_file_data(project_id=config_response.id)
            # 准备生成报告的提示词
            prompt = None;
            logger.info(f"材料 {config_response.id} 的文档类型: {config_response.doc_type}")
            #智库报告
            if config_response.doc_type == DOC_TYPE.THINK_TANK.value:
                think_tank_meta = await get_think_tank_meta(
                    meta_id=config_response.extend_id
                )
                #todo 暂时只有战略规划
                prompt = get_think_tank_content_prompt(
                    name=config_response.name,
                    word_count=config_response.word_count_requirement or 0,
                    document_type=config_response.document_type,
                    target=think_tank_meta.target,
                    user_prompt=config_response.user_add_prompt,
                    analysis_result=additional_data,
                    outline=outline,
                    contexts=research.contexts,
                    literatures=literatures
                )   
                logger.info(f"智库报告的大纲： {outline}")
                logger.info(f"智库报告的提示词：\n{prompt}")
            # 市场行业投资研习报告
            elif config_response.doc_type == DOC_TYPE.MARKET_INVESTMENT.value:
                prompt =  get_market_investment_content_prompt(
                    outline=outline,
                    document_type=config_response.doc_type,
                    name=config_response.name,
                    word_count=config_response.word_count_requirement or 0,
                    literatures=literatures,
                    contexts=research.contexts,
                    analysis_result=additional_data,  # 使用合并后的分析结果
                    is_summary_literature=open_literature_summary,
                    user_prompt=config_response.user_add_prompt
                )

                   
            # 可行性报告   
            elif config_response.doc_type == DOC_TYPE.FEASIBILITY.value:

                open_literature_summary = False #默认文献不开启
                logger.info(f"可行性报告元数据ID: {config_response.extend_id}")
                feasibility_meta = None
                if config_response.extend_id:
                    feasibility_meta = await get_feasibility_meta(
                        meta_id=config_response.extend_id
                    )
                logger.info(f"可行性报告元数据: {feasibility_meta}")
                prompt =  get_feasibility_content_prompt(
                    outline=outline,
                    document_type=config_response.doc_type,
                    name=config_response.name,
                    word_count=config_response.word_count_requirement or 0,
                    literatures=literatures,
                    contexts=research.contexts,
                    analysis_result=additional_data,  # 使用合并后的分析结果
                    is_summary_literature=open_literature_summary,
                    user_prompt=config_response.user_add_prompt,
                    investment_amount=feasibility_meta.investment_amount if feasibility_meta else "",
                    expect_return_rate=feasibility_meta.expect_return_rate if feasibility_meta else "",
                    payback_period=feasibility_meta.payback_period if feasibility_meta else "",
                    risk_assessment=feasibility_meta.risk_assessment if feasibility_meta else "",
                )
            # 文献综述  
            elif config_response.doc_type == DOC_TYPE.LITERATURE_REVIEW.value:
                prompt =  get_literature_review_content_prompt(
                    outline=outline,
                    document_type=config_response.doc_type,
                    name=config_response.name,
                    word_count=config_response.word_count_requirement or 0,
                    literatures=literatures,
                    contexts=research.contexts,
                    analysis_result=additional_data,  # 使用合并后的分析结果
                    is_summary_literature=open_literature_summary,
                    user_prompt=config_response.user_add_prompt
                )
            else:
                prompt = await final_report_prompt(
                    outline=outline,
                    language_style=config_response.language_style or "",
                    name=config_response.name,
                    application_category=config_response.application_category or "",
                    config=config,
                    word_count=config_response.word_count_requirement or 0,
                    team_introduction=config_response.team_introduction or "",
                    participants=participants,
                    main=main,
                    literatures=literatures,
                    contexts=research.contexts,
                    analysis_result=additional_data,  # 使用合并后的分析结果
                    is_summary_literature=open_literature_summary,
                    user_prompt=config_response.user_add_prompt
                )

            if open_literature_summary:
                reference_prompt = await final_report_reference_prompt(
                    literatures=literatures,
                    is_summary_literature=open_literature_summary
                )
                logger.info(f"参考文献的提示词：\n{reference_prompt}")
                prompt = reference_prompt + prompt

            system_prompt = await final_report_prompt_system(
                word_count=config_response.word_count_requirement or 0,
                is_summary_literature=open_literature_summary
            )
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]
            logger.info(f"最终报告的系统提示词：\n{system_prompt}")
            logger.info(f"最终报告的用户提示词：\n{prompt}")
        except Exception as e:
            msg = f"生成报告提示词失败: {str(e)}"
            logger.error(msg)
            raise Exception(msg)
        return messages, CallLLMFlag.GENERATE_REPORT.value, "report"
    except Exception as e:
        error_msg = f"报告流式生成错误: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)

def yield_self(
    chunk: Data,
    send_length: int,
    current_user: UserResponse
):
    if send_length >= settings.TRIAL_USER_MAX_TEXT and current_user.is_trial:
        return f"data: {json.dumps({'status': 'completed'})}\n\n"
    else:
        return f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
 
def calculate_length(chunk: Data):
    if chunk.status == ContentStatus.NORMAL:
        return len(chunk.content) if chunk.content else 0
    return 0
# 流式返回
def stream_fn(text):
    yield f"data: {json.dumps({'content': text, 'status': ContentStatus.NORMAL})}\n\n"
        # 发送完成事件
    yield f"data: {json.dumps({'status': 'completed'})}\n\n"
def send_trail_text(project_id: str, flag: str):
    data = report_content_manager.get_project_content(project_id) if flag == 'report' else outline_content_manager.get_project_content(project_id)
    if data:
        list_data = data.read_chunks + data.unread_chunks
        content = ""
        for item in list_data:
            if item.status == ContentStatus.NORMAL and len(content) <= settings.TRIAL_USER_MAX_TEXT:
                content += item.content
        if len(content) >= settings.TRIAL_USER_MAX_TEXT:
            return stream_handle_before_send(content)
        else:
            return ""
    else:
        return ""
async def remove_storage(project_id: str, flag='outline'):
    # 直接从数据库获取ProjectConfig模型
    config_db = await get_project_data(
        project_id=project_id
    )
    if flag == 'outline':
        # 获取该项目的内容对象
        project_content = outline_content_manager.get_project_content(project_id)
            
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                # error = f"已取消大纲生成任务"
                logger.info(f"已取消项目 {project_id} 的大纲生成任务")
                # outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
            except Exception as e:
                error = f"取消大纲生成任务时出错: {str(e)}"
                # outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                logger.error(error)
            # 更新项目状态为已取消
            config_db.status = ProjectConfigStatus.OUTLINE_CANCELED.value
            config_db.ai_generated_outline = None
            await config_db.save()
        elif flag == 'report':

            project_content = report_content_manager.get_project_content(project_id)
                
            # 如果存在异步任务，尝试取消
            if project_content and project_content.asyncioInstance:
                try:
                    project_content.asyncioInstance.cancel()
                    # error = f"已取消大纲生成任务"
                    logger.info(f"已取消项目 {project_id} 的报告生成任务")
                    # outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                except Exception as e:
                    error = f"取消报告生成任务时出错: {str(e)}"
                    # outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                    logger.error(error)
                # 更新项目状态为已取消
                config_db.status = ProjectConfigStatus.REPORT_CANCELED.value
                config_db.ai_generated_report = None
                await config_db.save()
async def report_complete_callback(all_content: str, project_id: str, starttime: datetime, prefix: Optional[str]="report"): # 存储大纲内容
    logger.info("准备把正文写到文件里面了。")
    config_db = await get_project_data(project_id=project_id)
    report_content_manager.clear_project(project_id)
    logger.info(all_content)
    relative_path = generate_storage_path(
        project_id=project_id,
        name=config_db.name,
        prefix=prefix
    )
    save_text_to_file(handle_before_save(remove_markdown_h1_and_text(all_content, config_db.name)), relative_path)
    logger.info("正文已经写到文件里面了。")
    # 增加机构的
    config_db.status = ProjectConfigStatus.REPORT_GENERATED.value
    config_db.report_generation_time = datetime.now()  # 确保使用不带时区的日期时间
    config_db.manual_modified_report_time = None
    config_db.manual_modified_report = None
    config_db.ai_generated_report = relative_path
    await config_db.save()
    endtime = datetime.now()
    logger.info(f"报告：{config_db.id}生成结束时间: {endtime.strftime('%Y-%m-%d %H:%M:%S')}，总共用时: {(endtime - starttime).total_seconds()}秒")
    
async def report_error_callback(error: str, project_id: str):
    # 处理错误
    logger.error(f"生成报告时发生错误: {error}")
    config_db = await get_project_data(project_id=project_id)
    report_content_manager.add_content(project_id, error, ContentStatus.ERROR)
    config_db.status = ProjectConfigStatus.REPORT_FAILED.value
    await config_db.save()
async def report_callback(
    content: str,
    project_id: str,
    user_id: str,
    is_diff_count: bool,
    cb: Optional[Callable[[str], None]] = None
):
    if not is_diff_count:
        logger.info("更新用户使用次数")
        user_report_usage = await UserReportUsage.filter(user_id_id=user_id, is_deleted=False).first()
        # 更新用户使用次数
        user_report_usage.used_count += 1
        await user_report_usage.save()
        cb and cb()
    report_content_manager.add_content(project_id, content, ContentStatus.NORMAL)

async def outline_complete_callback(all_content: str, project_id: str, prefix: Optional[str]="outline"): # 存储大纲内容
    config_db = await ProjectConfig.filter(id=project_id).first()
    relative_path = generate_storage_path(
       name=config_db.name,
       project_id=project_id,
       prefix=prefix
    )
    config_db.status = ProjectConfigStatus.OUTLINE_GENERATED.value
    config_db.outline_generated_time = datetime.now()
    # 生成大纲的时候要把之前的内容清空
    config_db.manual_modified_outline = None
    config_db.manual_modified_outline_time = None
    config_db.ai_generated_report = None
    config_db.report_generation_time = None
    config_db.manual_modified_report_time = None
    config_db.manual_modified_report = None
   
    save_text_to_file(handle_before_save(remove_markdown_h1_and_text(all_content, config_db.name)), relative_path)
    config_db.ai_generated_outline = relative_path
    await config_db.save()
async def outline_error_callback(error: str, project_id: str):
    config_db = await ProjectConfig.filter(id=project_id).first()
    # 处理错误
    logger.error(f"生成大纲时发生错误: {error}")
    outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
    config_db.status = ProjectConfigStatus.OUTLINE_FAILED.value
    await config_db.save()
async def outline_callback(content: str, project_id: str):
  outline_content_manager.add_content(project_id, content, ContentStatus.NORMAL)
async def get_project_data(
    project_id: str,
    current_user: Optional[UserResponse] = None
):
    project_config = await ProjectConfig.filter(
        id=project_id,
        is_deleted=0
    ).prefetch_related("model", "leader").first()
    if not project_config:
        raise Exception("材料配置不存在")
    if current_user and not is_user_authed(
        resource_belong_user=project_config.user_id,
        operator=current_user.id
    ):
        raise Exception("材料配置不存在")
    if not project_config.model:
        raise Exception("没有配置模型")
    return project_config

async def get_special_one_data(
    project_config: ProjectConfig
):
    document_type = await get_label_by_value(
        value=project_config.document_type
    ) if project_config.document_type else ""
    logger.info(f"项目 {project_config.id} 的文档类型: {project_config.doc_type}")
    if project_config.doc_type == DOC_TYPE.MEETING_GEN.value:
        if not project_config.extend_id:
            raise Exception("会议录音记录为空")
        voice_data = await get_voice_text(
            voice_id=project_config.extend_id
        )
        messages = [
            {
                "role": "user",
                "content": get_chapter_review_prompt(
                    content=voice_data.text_content
                )
            }
        ]
        return messages, CallLLMFlag.MEETING_REVIEW.value, "meeting_chapter"
    #智库报告
    elif project_config.doc_type == DOC_TYPE.THINK_TANK.value: 
        if not project_config.extend_id:   
            raise Exception("智库报告元数据记录为空")
        meta_data = await get_think_tank_meta(
            meta_id=project_config.extend_id
        )
        if not meta_data:
            raise Exception("智库报告元数据记录为空")
        
        demo = ""
        try:
            if project_config.user_add_demo_id:
                file_data = await get_file_content_by_id(project_config.user_add_demo_id)
                file_path = file_data.file_path
                if file_path.endswith(".doc") or file_path.endswith(".docx"):
                    demo = docx_file_to_markdown(file_path)
                else:
                    demo = read_file_content(file_path)
        except Exception as e:
            error_msg = f"{ProjectConfigError.GET_USER_ADD_DEMO_FAIL.value}: {str(e)}"
            logger.error(error_msg)
            raise e
      
        messages = [
            {
                "role": "user",
                "content": get_think_tank_outline_prompt(
                    name=project_config.name,
                    doc_type=project_config.doc_type,
                    target=meta_data.target,
                    analysis_method=meta_data.analysis_method,
                    language_style=project_config.language_style or "",
                    demo = demo,
                    user_prompt=project_config.user_add_prompt,
                    document_type=document_type
                )
            }
        ]
        return messages, CallLLMFlag.THINK_TANK.value, "think_tank"
    #市场行业投资研习报告
    elif project_config.doc_type == DOC_TYPE.MARKET_INVESTMENT.value:
       
        demo = ""
        try:
            if project_config.user_add_demo_id:
                file_data = await get_file_content_by_id(project_config.user_add_demo_id)
                file_path = file_data.file_path
                if file_path.endswith(".doc") or file_path.endswith(".docx"):
                    demo = docx_file_to_markdown(file_path)
                else:
                    demo = read_file_content(file_path)
        except Exception as e:
            error_msg = f"{ProjectConfigError.GET_USER_ADD_DEMO_FAIL.value}: {str(e)}"
            logger.error(error_msg)
            raise e
      
        messages = [
            {
                "role": "user",
                "content": get_market_investment_outline_prompt(
                    name=project_config.name,
                    document_type=document_type,
                    language_style=project_config.language_style or "",
                    demo = demo,
                    user_prompt=project_config.user_add_prompt
                )
            }
        ]
        return messages, CallLLMFlag.MARKET_INVESTMENT.value, "market_investment"
    #可行性研究报告
    elif project_config.doc_type == DOC_TYPE.FEASIBILITY.value:
        demo = ""
        try:
            if project_config.user_add_demo_id:
                file_data = await get_file_content_by_id(project_config.user_add_demo_id)
                file_path = file_data.file_path
                if file_path.endswith(".doc") or file_path.endswith(".docx"):
                    demo = docx_file_to_markdown(file_path)
                else:
                    demo = read_file_content(file_path)
        except Exception as e:
            error_msg = f"{ProjectConfigError.GET_USER_ADD_DEMO_FAIL.value}: {str(e)}"
            logger.error(error_msg)
            raise e
        feasibility_meta = None
        if project_config.extend_id:
            feasibility_meta = await get_feasibility_meta(
                meta_id=project_config.extend_id
            )
            # if not meta_data:
            #     raise Exception("可行性研究报告元数据记录为空")
        messages = [
            {
                "role": "user",
                "content": get_feasibility_outline_prompt(
                    name=project_config.name,
                    document_type=document_type,
                    language_style=project_config.language_style or "",
                    demo = demo,
                    investment_amount=f"{feasibility_meta.investment_amount}万" if feasibility_meta else "",
                    expect_return_rate=f"{feasibility_meta.expect_return_rate}%" if feasibility_meta else "",
                    payback_period=f"{feasibility_meta.payback_period}年" if feasibility_meta else "",
                    risk_assessment=feasibility_meta.risk_assessment if feasibility_meta else "",
                    user_prompt=project_config.user_add_prompt
                )
            }
        ]
        return messages, CallLLMFlag.FEASIBILITY.value, "feasibility"
    #文献综述
    elif project_config.doc_type == DOC_TYPE.LITERATURE_REVIEW.value:
        logger.info(f"文献综述：{project_config.doc_type}")
        demo = ""
        try:
            if project_config.user_add_demo_id:
                file_data = await get_file_content_by_id(project_config.user_add_demo_id)
                file_path = file_data.file_path
                if file_path.endswith(".doc") or file_path.endswith(".docx"):
                    demo = docx_file_to_markdown(file_path)
                else:
                    demo = read_file_content(file_path)
        except Exception as e:
            error_msg = f"{ProjectConfigError.GET_USER_ADD_DEMO_FAIL.value}: {str(e)}"
            logger.error(error_msg)
            raise e
        logger.info(f"文献综述大纲提示词：{project_config.doc_type}")
        messages = [
            {
                "role": "user",
                "content": get_literature_review_outline_prompt(
                    name=project_config.name,
                    document_type=document_type,
                    language_style=project_config.language_style or "",
                    demo=demo,
                    user_prompt=project_config.user_add_prompt
                )
            }
        ]
        logger.info(f"文献综述大纲提示词：{messages}")
        return messages, CallLLMFlag.LITERATURE_REVIEW.value, "literature_review"
    else:
        additional_data = await get_additional_file_data(project_id=project_config.id)
            # 重新计算预估时间
        try:
            logger.info(f"项目 {project_config.id} 预估时间重新计算开始")
            estimated_time = await update_estimated_time(project_config, UpdateEstimatedTimeProcessingMode.GENERATE_OUTLINE)
            project_config.estimated_time = estimated_time
            project_config.updated_at = datetime.now()
            await project_config.save()
            logger.info(f"项目 {project_config.id} 预估时间重新计算完成: {project_config.estimated_time}")
        except Exception as e:
            logger.warning(f"项目 {project_config.id} 预估时间重新计算失败: {e}")

        demo = ""
        try:
            if project_config.user_add_demo_id:
                file_data = await get_file_content_by_id(project_config.user_add_demo_id)
                file_path = file_data.file_path
                if file_path.endswith(".doc") or file_path.endswith(".docx"):
                    demo = docx_file_to_markdown(file_path)
                else:
                    demo = read_file_content(file_path)
        except Exception as e:
            error_msg = f"{ProjectConfigError.GET_USER_ADD_DEMO_FAIL.value}: {str(e)}"
            logger.error(error_msg)
            raise e
        team_members = []
        if project_config.team_members:
            team_members = await ProjectMemberJoin.filter(
                join_id=project_config.team_members,
                is_deleted=0
            ).prefetch_related("member").all()
            logger.info(f"team_members: {team_members}")
            team_members = [] if not team_members else [
                ProjectMemberBase(
                    name=join.member.name,
                    title=join.member.title,
                    representative_works=join.member.representative_works,
                    organization=join.member.organization
                )
                for join in team_members
                ]
        prompt = generate_outline_prompt(
            name=project_config.name or "",
            application_category=project_config.application_category or "",
            leader=project_config.leader,
            team_members=team_members,
            language_style=project_config.language_style or "",
            leader_introduction=project_config.ai_leader_introduction,
            demo=demo,
            user_prompt=project_config.user_add_prompt,
            analysis_result=additional_data
        )
        # 调用LLM生成大纲 - 流式处理并保存到文件
        messages = [
            {"role": "system", "content": OUTLINE_SYSTEM_PROMPT},
            {"role": "user", "content": prompt}
        ]
        return messages, CallLLMFlag.GENERATE_OUTLINE.value, "outline"
      
async def generate_step_one(
    current_user: UserResponse,
    project_id: str
) -> ProjectConfigResponse2:  
    """生成内容的第一步，比如会议纪要的章节概览"""
    project_config = await get_project_data(
        project_id=project_id,
        current_user=current_user
    )
    messages, flag, prefix = await get_special_one_data(
        project_config=project_config
    )
    # relative_path = generate_storage_path(
    #     project_id=project_id,
    #     name=project_config.name,
    #     prefix=prefix
    # )
    # save_text_to_file(
    #     content="",
    #     file_path=relative_path
    # )
    model = project_config.model
    await remove_storage(project_id=project_id, flag='outline')
    async def callback(content:str):
        await outline_callback(content=content, project_id=project_id)
    async def complete_callback(id: str, content: str):
        await outline_complete_callback(
            all_content=content,
            project_id=project_id,
            prefix=prefix
        )
    async def error_callback(content: str):
        await outline_error_callback(
            error=content,
            project_id=project_id
        )
    # 添加一个空字符串，用于触发流式处理
    outline_content_manager.add_content(project_id, "", ContentStatus.NORMAL)
    asyncioInstance = asyncio.create_task(stream_llm_and_save(
      messages=messages,
      user=current_user, 
      callback=callback,
      complete_callback=complete_callback,
      error_callback=error_callback,
      flag=flag,
      model=model.model_name,
      apiKey=model.api_key,
      apiUrl=model.api_url,
      related_id=project_config.id
    ))
    # 将任务实例保存到内容管理器
    outline_content_manager.add_asyncio(project_id, asyncioInstance)
    project_config.status = ProjectConfigStatus.OUTLINE_GENERATING.value
    
    await project_config.save()
    return await get_one_project_config(project_id=project_id)
async def stream_step_one(
    current_user: UserResponse,
    project_id: str
):
    """流式获取第一步的内容"""
    # 检查项目是否存在并验证权限
    try:
        config_db = await get_project_data(
            project_id=project_id,
            current_user=current_user
        )
    except Exception as e:
        logger.error(str(e))
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目配置不存在', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 检查正在生成的内容
    if config_db.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
        logger.info(f'{config_db.id}处于生成状态')
        async def stream_realtime_content():
            # 已读取的内容计数
            read_count = 0
            project_content = outline_content_manager.get_project_content(project_id)
            
            if not project_content:
                logger.info("找不到正在生成的内容")
                config_db.status = ProjectConfigStatus.OUTLINE_FAILED.value
                await config_db.save()
                yield f"data: {json.dumps({'content': '找不到正在生成的内容', 'status': ContentStatus.ERROR})}\n\n"
                
            # 先发送已有的内容
            for chunk in project_content.read_chunks:
                yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
            # 然后持续检查是否有新内容
            while config_db.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
                chunk = outline_content_manager.read_next_chunk(project_id)
                if chunk:
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                    read_count += 1
                else:
                    # 暂时没有新内容，等待一会再检查
                    await asyncio.sleep(0.5)
                    # 重新获取数据库状态，检查是否仍在生成中
                    await config_db.refresh_from_db()
                    yield f"data: {json.dumps({'content': 'thinking', 'status': ContentStatus.HEART_BEAT.value})}\n\n"
            
            # 获取剩余未读取的内容
            project_content = outline_content_manager.get_project_content(project_id)
            if project_content and project_content.unread_chunks:
                for item in project_content.unread_chunks:
                    yield f"data: {json.dumps({'content': item.content, 'status': item.status})}\n\n"
                read_count += len(project_content.unread_chunks)
            
            # 生成已完成，发送完成状态
            yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
        return StreamingResponse(
            content=stream_realtime_content(),
            media_type="text/event-stream"
        )
    logger.info(f'{config_db.id}不处于生成状态')
    # 如果不是正在生成状态，则回退到从文件读取
    outline_path = config_db.manual_modified_outline or config_db.ai_generated_outline
    if not outline_path:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目尚未生成大纲', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    # 如果用户没有一直在页面等到流式结束就会导致内存里面的内容不会被清除
    outline_content_manager.clear_project(project_id)
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(outline_path, current_user), 
        media_type="text/event-stream"
    )

async def stream_step_two(
    project_id: str,
    current_user: UserResponse
):
    """
    流式返回项目报告内容（SSE格式）
    """
    logger.info(f"流式返回项目报告内容（SSE格式），项目ID: {project_id}")
    # 检查项目是否存在并验证权限
    try:
        config_db = await get_project_data(
            project_id=project_id,
            current_user=current_user
        )
    except Exception as e:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目配置不存在', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 检查正在生成的内容
    if config_db.status == ProjectConfigStatus.REPORT_GENERATING.value:
        # 如果是体验用户
        logger.info(f"{current_user.username}是否是体验用户：{current_user.is_trial}")
        if current_user.is_trial:
            already_generated_text = send_trail_text(config_db.id, 'report')
            if already_generated_text:
                logger.info(f"体验用户{current_user.username}的报告截断啦！")
                return StreamingResponse(
                    content=stream_fn(already_generated_text),
                    media_type="text/event-stream"
                )   
        async def stream_realtime_content():
            send_length = 0
            # 已读取的内容计数
            read_count = 0
            project_content = report_content_manager.get_project_content(project_id)
            
            if not project_content:
                config_db.status = ProjectConfigStatus.REPORT_FAILED.value
                await config_db.save()
                yield f"data: {json.dumps({'content': '找不到正在生成的内容', 'status': ContentStatus.ERROR})}\n\n"
                return  
            # 先发送已有的内容
            for chunk in project_content.read_chunks:
                send_length += calculate_length(chunk)
                yield yield_self(
                    chunk,
                    send_length,
                    current_user
                )
                # await asyncio.sleep(0.02)
            
            # 然后持续检查是否有新内容
            while config_db.status == ProjectConfigStatus.REPORT_GENERATING.value:
                chunk = report_content_manager.read_next_chunk(project_id)
                if chunk:
                    send_length += calculate_length(chunk)
                    yield yield_self(
                        chunk,
                        send_length,
                        current_user
                    )
                    read_count += 1
                else:
                    # 暂时没有新内容，等待一会再检查
                    await asyncio.sleep(5)
                    # 重新获取数据库状态，检查是否仍在生成中
                    await config_db.refresh_from_db()
                    yield f"data: {json.dumps({'content': 'thinking', 'status': ContentStatus.HEART_BEAT.value})}\n\n"
            
            # 获取剩余未读取的内容
            project_content = report_content_manager.get_project_content(project_id)
            if project_content and project_content.unread_chunks:
                for chunk in project_content.unread_chunks:
                    yield yield_self(
                        chunk=chunk,
                        send_length=send_length,
                        current_user=current_user
                    )
                read_count += len(project_content.unread_chunks)
            logger.info("\n\n\n我要清空本地数据啦！\n\n\n")
            # 生成已完成，发送完成状态
            yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
            report_content_manager.clear_project(project_id)
        return StreamingResponse(
            content=stream_realtime_content(),
            media_type="text/event-stream"
        )
    
    # 如果不是正在生成状态，则回退到从文件读取
    report_path = config_db.manual_modified_report or config_db.ai_generated_report
    if not report_path:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目生成报告失败', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    logger.info("\n\n\n我要清空本地数据啦2！\n\n\n")
    # 如果用户没有一直在页面等到流式结束就会导致内存里面的内容不会被清除
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(report_path, current_user, True), 
        media_type="text/event-stream",
        background=BackgroundTask(lambda: report_content_manager.clear_project(project_id))
    )

"""这是将已经生成正文的内容进行内容压缩"""
async def comprise_text(
    text: str,
    current_user: UserResponse,
    related_id: str
):
    try:
        model = await get_user_model(
            use_case=UseCase.PROJECT_CONFIG_NEED.value,
            current_user=current_user
        )
        messages = [
            {
                "role": "user",
                "content": get_comprise_content_prompt(text=text)
            }
        ]
        response = await call_llm(
            messages=messages,
            apiKey=model.api_key,
            apiUrl=model.api_url,
            flag="压缩段章节段落",
            model=model.model_name,
            stream=False,
            user=current_user,
            related_id=related_id
        )
        logger.info(f"本段压缩的内容：{response}")
        return response
    except Exception as e:
        logger.error(f"压缩已经生成的内容报错：{str(e)}")
        return ""

"""下载大纲、正文或者幻觉审查的docx文档"""
async def download_file(
    project_id: str,
    current_user: UserResponse,
    # outline report
    flag: Optional[str] = "outline"
):
    if not project_id:
        raise Exception("项目ID不能为空")
    project = await ProjectConfig.filter(id=project_id,is_deleted=False).prefetch_related("user", "user__organization").first()
    if not project:
        raise Exception(ProjectConfigError.NOT_RECORD.value)
    authed = await is_user_authed(project.user.id, current_user.id)
    if not authed:
        raise Exception(ErrorType.FORBIDDEN.value)
    file_path = ""
    if flag == 'outline':
        file_path = project.manual_modified_outline or project.ai_generated_outline
    elif flag == 'report':
        file_path = project.manual_modified_report or project.ai_generated_report
    elif flag == 'hallucination':
        file_path = project.hallucination_report
    if not file_path:
        raise Exception(ErrorType.FILE_NOT_EXIST.value)
    try:
        return convert_markdown_to_docx(file_path)
    except Exception as e:
        logger.error(f"markdown转word报错:{str(e)}")
        raise e

"""将参考文献段落进行拼接成文本进行返回"""
def normal_literature(literatures: List[LiteratureResponse]):
    logger.info(f"参考文献列表：{literatures}")
    reference_list = []
    for index,item in enumerate(literatures):
        url = ""
        if item.doi:
            url = f"https://doi.org/{item.doi}"
        elif item.url:
            url = item.url
        else:
            url = "#"
        reference_list.append(f"[{index+1}] [{item.citation_format}]({url})")
    reference_list = ("\n\n").join(reference_list)
    return f"\n\n## 参考文献\n\n{reference_list}"
"""摘要段落的字数"""
ABSTRACT_PARAGRAPH_COUNT = 500
"""关键词段落的字数"""
KEYWORD_PARAGRAPH_COUNT = 20
"""除去摘要和关键词的之后的剩余段落里面占比60%字数的是前几个段落"""
PERCENT_60_CHAPTER_NUM = 2
"""将markdown格式文本按照标题格式形成一颗树"""
def markdown_to_tree(text: str, total_count: int) -> List[OutlineNode]:
    lines = text.splitlines()
    nodes = []
    stack: List[OutlineNode] = []

    # 正则匹配标题行，如 "# 一级标题"、"## 二级标题"
    header_pattern = re.compile(r'^(#{1,6})\s+(.*)')

    # 按行遍历读取内容
    for line in lines:
        header_match = header_pattern.match(line)
        # 如果是md的标题
        if header_match:
            level = len(header_match.group(1))
            title = header_match.group(2).strip()
            node = OutlineNode(**{
                "title": title,
                "content": "",
                "children": [],
                "count": 0,
                "level": level  # 仅内部使用，最后会删除
            })

            while stack and stack[-1].level >= level:
                stack.pop()
            if stack:
                stack[-1].children.append(node)
            else:
                nodes.append(node)

            stack.append(node)
        # 如果不是标题且内容不为空
        elif line.strip() != "":
            if stack:
                if stack[-1].content:
                    stack[-1].content += "\n" + line
                else:
                    stack[-1].content = line

    # 分配字数
    def assign_count(nodes: List[OutlineNode], total: int):
        if not nodes:
            return
        # 摘要段落
        abstract_nodes = [n for n in nodes if "摘要" in n.title.strip().upper() and not n.children]
        # 关键词段落
        keyword_nodes = [n for n in nodes if "关键词" in n.title.strip().upper() and n.children]
        # 关键词和摘要之外的段落
        other_nodes = [n for n in nodes if not (("摘要" in n.title.strip().upper() and not n.children) or ("关键词" in n.title.strip().upper() and not n.children))]

        # 摘要段落占的字数，每个摘要段落占500字
        abstract_total = ABSTRACT_PARAGRAPH_COUNT * len(abstract_nodes)
        # 每个关键词段落占20个字。
        keyword_total = KEYWORD_PARAGRAPH_COUNT * len(keyword_nodes)
        # 剩余的数字
        remaining_total = max(total - abstract_total - keyword_total, 0)

        # 分出40%和60%的数字
        percent_60 = int(remaining_total * 0.6)
        left_40 = remaining_total - percent_60
        percent_60_chapter_num = PERCENT_60_CHAPTER_NUM

        # 分配摘要
        for node in abstract_nodes:
            node.count = ABSTRACT_PARAGRAPH_COUNT
        # 分配关键词
        for node in keyword_nodes:
            node.count = KEYWORD_PARAGRAPH_COUNT
        # 平均分配剩余段落
        if other_nodes:
            for i in range(len(other_nodes)):
                node = other_nodes[i]
                # 前percent_60_chapter_num个大章节占比60%，然后percent_60_chapter_num个章节平分字数
                if i <= percent_60_chapter_num - 1:
                    node.count = percent_60 // percent_60_chapter_num
                # 剩余章节平分剩下的40%
                else:
                    node.count = left_40 // (len(other_nodes) - percent_60_chapter_num)
                assign_to_children(node)
            # 把余数补给最后一个节点，避免丢掉
            assigned_sum = sum(n.count for n in other_nodes)
            if assigned_sum < total:
                other_nodes[-1].count += total - assigned_sum
    # 大章节的子章节则是完全的平分分得的大章节字数
    def assign_to_children(node: OutlineNode):
        if not node.children:
            return
        per = node.count // len(node.children)
        for child in node.children:
            child.count = per
            assign_to_children(child)

    assign_count(nodes, total_count)
    # 子章节把字数分完之后将父章节的字数修改为0
    def set_count_to_0_for_parents(nodes: List[OutlineNode]):
        for node in nodes:
            if node.children:
                node.count = 0
                set_count_to_0_for_parents(node.children)

    set_count_to_0_for_parents(nodes)
    return nodes

"""验证用户的条件并且开启异步生成材料正文（泛化正文）的任务"""
async def generate_step_two(
    project_id: str,
    current_user: UserResponse
):
    """生成正文"""
    project = await get_project_data(project_id=project_id)
    open_literature_summary = project.doc_type != DOC_TYPE.FEASIBILITY.value
    starttime = datetime.now()
    logger.info(f"报告开始时间：{starttime.strftime('%Y-%m-%d %H:%M:%S')}")
    # 检查用户的可用次数情况
    await check_user_usage_limit(current_user.id)
    # 移除内存里面的缓存数据
    await remove_storage(project_id=project_id, flag='report')
    # 读取大纲并且将大纲转化为可遍历的树
    outline_path = project.ai_generated_outline or project.manual_modified_outline
    outline = remove_markdown_h1_and_text(
        all_text=read_file_content(outline_path),
        title=project.name,
        combine_title=False
    )
    paragraph_tree = markdown_to_tree(outline, project.word_count_requirement)
    research = None
    # 会议纪要不需要进行搜索所以不需要research
    if project.doc_type != DOC_TYPE.MEETING_GEN.value:
        # 创建一个研究实例来处理报告生成
        research = await Research.create(
            query=project.name,
            search_queries=[],
            contexts=[],
            status=ResearchStatus.PENDING
        )
        project.research = research
    project.status = ProjectConfigStatus.REPORT_GENERATING.value
    await project.save()
    await project.refresh_from_db()
    project_config = await get_one_project_config(project_id=project.id)
    
    # 是否已经扣除过使用次数
    is_diff_count = False

    async def error_callback(error: str):
        await report_error_callback(error=error, project_id=project_id)
    def cb():
        nonlocal is_diff_count
        is_diff_count = True
    async def callback(content: str):
        nonlocal is_diff_count
        await report_callback(
            content=content,
            project_id=project_id,
            user_id=current_user.id,
            is_diff_count=is_diff_count,
            cb=cb
        )
    # 添加一个空字符串，用于触发流式处理
    report_content_manager.add_content(
        str(project.id), "")
    await research.refresh_from_db()
    task = asyncio.create_task(async_generate_report(
        research=research,
        project_config=project_config,
        project=project,
        paragraph_tree=paragraph_tree,
        current_user=current_user,
        callback=callback,
        error_callback=error_callback,
        starttime=starttime
    ))
    # 将任务保存到内容管理器中
    report_content_manager.add_asyncio(project_id, task)
    logger.info(f"大纲生成的树：{paragraph_tree}")
    return project_config
    
"""生成正文前的前置动作，比如搜索网页"""
async def action_before_generate_content(
    project_config: ProjectConfig,
    project: ProjectConfigResponse2,
    current_user: UserResponse,
    open_literature_summary: Optional[bool] = True,
    research: Optional[Research] = None
):
    # 估算完成时间
    try:
        logger.info(f"项目 {project_config.id} 预估时间重新计算开始")
        estimated_time = await update_estimated_time(
            project_config=project_config,
            processing_mode=UpdateEstimatedTimeProcessingMode.GENERATE_CONTENT
        )
        project_config.estimated_time = estimated_time
        project_config.updated_at = datetime.now()
        await project_config.save()
        logger.info(f"项目 {project_config.id} 预估时间重新计算完成: {project_config.estimated_time}")
    except Exception as e:
        logger.warning(f"项目 {project_config.id} 预估时间重新计算失败: {e}")

    # 历史数据的doc_type可能为空
    if project.doc_type != DOC_TYPE.MEETING_GEN.value:
        # 可行性研究报告不开启文献总结
        open_literature_summary = project.doc_type != DOC_TYPE.FEASIBILITY.value
        # 这里面可能是science、nature、pubmed
        search_engine_list = []
        if project.literature_library:
            search_engine_list = project.literature_library.split(",")
        else:
        # 这里就是没有选择science、nature、pubmed等，直接搜索，搜索引擎是使用默认的SEARCH_ENGINE
            search_engine_list = ['']
        # 搜索参考文献库
        try:
            for search_method_flag in search_engine_list:
                await search_to_carry_info(
                    research=research,
                    current_user=current_user,
                    config_response=project,
                    # 搜索关键词的引擎的标识符
                    search_method_flag=search_method_flag,
                    open_literature_summary=open_literature_summary
                )
        except Exception as e:
            logger.error(f"材料：{project.id}的搜索步骤出现错误跳过搜索步骤：{str(e)}")
async def async_generate_report(
    research: Research,
    project: ProjectConfig,
    project_config: ProjectConfigResponse2,
    paragraph_tree: List[OutlineNode],
    current_user: UserResponse,
    callback: Callable,
    error_callback: Callable,
    starttime: datetime
):
    # 暂存的文档内容
    temp_text = ""
    # 压缩后已生成文档
    comprised_text = ""
    await action_before_generate_content(
        project=project_config,
        current_user=current_user,
        project_config=project,
        research=research
    )
    call_llm_flag, prefix = get_flag_and_prefix(
        doc_type=project.doc_type,
        step="two"
    )
    
    literatures = await get_literature_by_project(str(project.id))
    logger.info(f"材料ID：{project.id}，材料研究主题：{project.name}总共收集到：{len(literatures)}条文献。")
    model = project_config.model
    api_url = model.api_url
    api_key = model.api_key
    model = model.model_name
    token_consume = {
        "input": 0,
        "output": 0
    }
    relative_path = generate_storage_path(
       name=project.name,
       project_id=project.id,
       prefix=prefix
    )

    async def traverse_tree(node: OutlineNode):
        nonlocal temp_text
        nonlocal callback
        nonlocal error_callback
        nonlocal current_user
        nonlocal project
        nonlocal token_consume
        nonlocal comprised_text
        nonlocal call_llm_flag
        if node is None:
            return
        elif node.title:
            period_start_time = datetime.now()
            logger.info(f"【{node.title}】段落的开始时间：{period_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            title_level = "#" * node.level
            report_content_manager.add_content(
                str(project.id), f"{title_level} {node.title}\n\n")
            if node.count > 0:
                async def middle_error_callback(error: str):
                    await error_callback(error)
                    raise Exception(error)
                async def middle_callback(token_data: dict, content: str):
                    logger.info(f"本阶段生成的内容（未处理前）：{content}")
                    nonlocal node
                    nonlocal title_level
                    nonlocal temp_text
                    nonlocal current_user
                    nonlocal project
                    nonlocal token_consume
                    nonlocal comprised_text
                    pipeline = [
                        remove_word_count_tag,
                        remove_after_reference,
                        lambda text: remove_markdown_heading(text, node.title),
                        lambda text: adjust_markdown_heading_levels(text, node.level + 1),
                        remove_markdown_code_fence,
                        remove_paragraph_bold,
                        modify_series_num
                    ]
                    for fn in pipeline:
                        content = fn(content)
                    try:
                        section_text = f"\n\n{title_level} {node.title}\n\n{content}\n\n"
                        logger.info(f"本阶段生成的内容（处理后）：{section_text}")
                        temp_text += section_text
                        comprised = await comprise_text(
                            current_user=current_user,
                            text=section_text,
                            related_id=project.id
                        )
                        comprised_text += f"\n\n{comprised}\n\n"
                        logger.info(f"目前为止的压缩文章: \n\n{comprised_text}")
                        logger.info(f"目前为止的完整文章：\n\n{temp_text}")
                        token_consume["input"] += token_data.get("input")
                        token_consume["output"] += token_data.get("output")
                        logger.info(f"本段生成消耗token：{token_data}")
                        diff_count = node.count - len(section_text)
                        logger.info(f"材料：{project.id}，段落{node.title}，本段的字数相差：{diff_count}（生成字数：{len(section_text)}，要求字数：{node.count}）")
                    except Exception as e:
                        logger.error(f"压缩内容报错：{str(e)}")
                messages = await get_report_prompt(
                    project_id=project.id,
                    literatures=literatures,
                    comprised_text=comprised_text,
                    research=research,
                    node=node
                )
                await stream_llm_and_save(
                    messages=messages,
                    user=current_user,
                    callback=callback,
                    complete_callback=middle_callback,
                    error_callback=middle_error_callback,
                    flag=call_llm_flag,
                    model=model,
                    apiKey=api_key,
                    apiUrl=api_url,
                    related_id=project.id
                )
            else:
                temp_text += remove_contain_outline_headings(f"{title_level} {node.title}\n\n")
            period_end_time = datetime.now()
            logger.info(f"【{node.title}】段落的结束时间：{period_end_time.strftime('%Y-%m-%d %H:%M:%S')}，总共用时: {(period_end_time - period_start_time).total_seconds()}秒")
            report_content_manager.add_content(
                str(project.id), "\n\n")
        for child in node.children:
            await traverse_tree(child)
    try:
        await traverse_tree(OutlineNode(**{
            "title": "",
            "content": "",
            "children": paragraph_tree,
            "count": 0,
            "level": 0  # 仅内部使用，最后会删除
        }))
        # 如果不是可行报告就添加参考文献段落
        if project_config.doc_type != DOC_TYPE.FEASIBILITY.value:
            reference_data = normal_literature(literatures)
            temp_text += reference_data
            report_content_manager.add_content(str(project.id), reference_data)
        logger.info("材料ID：{project.id}准备把正文写到文件里面了。")
        logger.info(temp_text)
        replace_title = remove_markdown_h1_and_text(
            all_text=temp_text,
            title=project_config.name
        )
        completed_text = handle_text_before_use(replace_title)
        save_text_to_file(file_path=relative_path, content=completed_text)
        logger.info("材料ID：{project.id}的正文已经写到文件里面了。")
        logger.info(f"材料ID：{project.id} 材料名：{project.name}的正文消耗token：{token_consume}")
        query_token = await get_token_data(project.id)
        logger.info(f"材料ID：{project.id} 材料名：{project.name}的搜索消耗token：{str(query_token)}")
        await del_token_data(project.id)
        endtime = datetime.now()
        logger.info(
            f"材料ID：{project.id} 材料名：{project.name}的报告生成结束时间: {endtime.strftime('%Y-%m-%d %H:%M:%S')}，总共用时: {(endtime - starttime).total_seconds()}秒")
        logger.info(f"材料ID：{project.id}的正文长度：{len(temp_text)}")
        # 增加机构的
        project.status = ProjectConfigStatus.REPORT_GENERATED.value
        project.report_generation_time = datetime.now()  # 确保使用不带时区的日期时间
        project.manual_modified_report_time = None
        project.manual_modified_report = None
        project.ai_generated_report = relative_path
        await project.save()
    except Exception as e:
        project.status = ProjectConfigStatus.REPORT_FAILED.value
        await project.save()
        logger.error(f"生成报告报错：{str(e)}")
    report_content_manager.clear_project(str(project.id))
"""获取大模型调用的标识符和正文文档的前缀"""
def get_flag_and_prefix(
    doc_type: str,
    step: Optional[str] = "two"
):
    two_dict = {
        "DOC_GEN": (
            CallLLMFlag.GENERATE_REPORT.value,
            "report"
        ),
        "MEETING_GEN": (
            CallLLMFlag.MEETING_SUMMARY.value,
            "meeting_report"
        ),
        # 智库报告
        "THINK_TANK": (
            CallLLMFlag.THINK_TANK.value,
            "tank_report"
        ),
        # 市场行业投资研习报告
        "MARKET_INVESTMENT": (
            CallLLMFlag.MARKET_INVESTMENT.value,
            "market_report"
        ),
        # 可行性报告
        "FEASIBILITY": (
            CallLLMFlag.FEASIBILITY.value,
            "feasibility_report"
        ),
        # 文献综述
        "LITERATURE_REVIEW": (
            CallLLMFlag.LITERATURE_REVIEW.value,
            "literature_report"
        )
    }
    one_dict = {
        "DOC_GEN": (
            CallLLMFlag.GENERATE_REPORT.value,
            "outline"
        ),
        "MEETING_GEN": (
            CallLLMFlag.MEETING_SUMMARY.value,
            "meeting_outline"
        ),
        # 智库报告
        "THINK_TANK": (
            CallLLMFlag.THINK_TANK.value,
            "tank_outline"
        ),
        # 市场行业投资研习报告
        "MARKET_INVESTMENT": (
            CallLLMFlag.MARKET_INVESTMENT.value,
            "market_outline"
        ),
        # 可行性报告
        "FEASIBILITY": (
            CallLLMFlag.FEASIBILITY.value,
            "feasibility_outline"
        ),
        # 文献综述
        "LITERATURE_REVIEW": (
            CallLLMFlag.LITERATURE_REVIEW.value,
            "literature_outline"
        )
    }
    return one_dict.get(doc_type or "DOC_GEN") if step == 'one' else two_dict.get(doc_type or "DOC_GEN")

async def get_report_prompt(
    project_id: str,
    literatures: List[LiteratureResponse],
    comprised_text: str,
    node: OutlineNode,
    research: Research
):
    project = await get_one_project_config(
        project_id=project_id
    )
    user_prompt = await generate_prompt(
        project=project,
        literatures=literatures,
        generated_text=comprised_text,
        title=node.title,
        content=node.content,
        contexts=research.contexts,
        text_count=node.count
    )
    messages = [
        {
            "role": "user",
            "content": user_prompt
        }
    ]
    logger.info(f"段落：{node.title}的正文生成提示词：{user_prompt}")
    if not project.doc_type or project.doc_type == DOC_TYPE.DOC_GEN.value:
        messages.append({
            "role": "system",
            "content": REPORT_SYSTEM_PROMPT
        })
    return messages

async def generate_prompt(
    project: ProjectConfigResponse2,
    contexts: List[str],
    literatures: List[LiteratureResponse],
    generated_text: str,
    title: str,
    content: str,
    text_count: int
):
    literature_text = None
    background_text = None
    additional_data = await get_additional_file_data(project_id=project.id)
    outline_path = project.manual_modified_outline or project.ai_generated_outline
    outline = read_file_content(outline_path)

    if literatures:
        literature_text = final_report_reference_prompt(literatures=literatures)
    target = iteration_prompt(
        title=title,
        content=f"，以及对应的这个标题的大纲要求:{content}" if content else '',
        generated=generated_text,
        count=text_count,
        doc_type=project.doc_type
    )
    if not project.doc_type or project.doc_type == DOC_TYPE.DOC_GEN.value:
        background_text = await final_report_background_prompt(
            contexts=contexts,
            project=project,
            num_count=text_count,
            outline=outline,
            analysis_result=additional_data,
            team_introduction=project.team_introduction
        )
    elif project.doc_type == DOC_TYPE.THINK_TANK.value:
        think_tank_meta = await get_think_tank_meta(
            meta_id=project.extend_id
        )
        background_text = await final_think_background_prompt(
            name=project.name,
            target=think_tank_meta.target,
            user_prompt=project.user_add_prompt,
            analysis_result=additional_data,
            outline=outline,
            contexts=contexts,
        )
    elif project.doc_type == DOC_TYPE.FEASIBILITY.value:
        feasibility_meta = await get_feasibility_meta(
            meta_id=project.extend_id
        )
        logger.info(f"可行性报告元数据: {feasibility_meta}")
        
        background_text = await final_feasibility_background_prompt(
            name=project.name,
            user_prompt=project.user_add_prompt,
            analysis_result=additional_data,
            outline=outline,
            contexts=contexts,
            investment_amount=f"{feasibility_meta.investment_amount}万" if feasibility_meta else "",
            expect_return_rate=f"{feasibility_meta.expect_return_rate}%" if feasibility_meta else "",
            payback_period=f"{feasibility_meta.payback_period}年" if feasibility_meta else "",
            risk_assessment=feasibility_meta.risk_assessment if feasibility_meta else ""
        )
    elif project.doc_type == DOC_TYPE.MARKET_INVESTMENT.value:
        background_text = await final_market_background_prompt(
            name=project.name,
            user_prompt=project.user_add_prompt,
            analysis_result=additional_data,
            outline=outline,
            contexts=contexts,
            document_type=project.document_type
        )
    elif project.doc_type == DOC_TYPE.LITERATURE_REVIEW.value:
        background_text = await final_literature_background_prompt(
            name=project.name,
            user_prompt=project.user_add_prompt,
            analysis_result=additional_data,
            outline=outline,
            contexts=contexts
        )
    
    if literature_text:
        result = f"{target}\n{literature_text}\n{background_text}"
    else:
        result = f"{target}\n{background_text}"
    logger.info(f"本阶段提示词：{result}")
    return result

"""分步过过程中某一段的个性化提示词"""
def iteration_prompt(
    title: str,
    content: str,
    generated: str,
    count: int,
    doc_type: Optional[str] = None
):
    dict_data = {
        "DOC_GEN": THIS_TIME_INFO_PROMPT,
        "THINK_TANK": THINK_TANK_ITER_PROMPT,
        "MARKET_INVESTMENT": MARKET_INVESTMENT_ITER_PROMPT,
        "FEASIBILITY": FEASIBILITY_ITER_PROMPT,
        "LITERATURE_REVIEW": LITERATURE_REVIEW_ITER_PROMPT
    }
    data_dict = {
        "para_title": title,
        "para_content": content,
        "generated_text": generated,
        "min_num_count": count,
        "max_num_count": int(count*1.1)
    }
    return dict_data.get(doc_type or "DOC_GEN").format(**data_dict)

async def final_report_background_prompt(
    project: ProjectConfigResponse2,
    contexts: List[str],
    num_count: int,
    outline: str,
    team_introduction: str,
    analysis_result: str
):
    return FINAL_REPORT_BACKGROUND_PROMPT.format(
        literature_prompt=LITERATURE_PROMPT,
        name=project.name,
        outline=outline,
        contexts=contexts,
        min_num_count=num_count,
        max_num_count=int(num_count*1.1),
        team_introduction=team_introduction,
        analysis_result=analysis_result
    )
async def final_think_background_prompt(
    name: str,
    target: str,
    user_prompt: str,
    analysis_result: str,
    outline: str,
    contexts: list[str]
):
    return THINK_TANK_BACKGROUND_PROMPT.format(
        literature_prompt=LITERATURE_PROMPT,
        target=target,
        name=name,
        outline=outline,
        user_prompt=user_prompt,
        analysis_result=analysis_result,    
        contexts =contexts,
    )
async def final_feasibility_background_prompt(
    name: str,
    user_prompt: str,
    analysis_result: str,
    outline: str,
    contexts: list[str],
    investment_amount: str,
    expect_return_rate: str,
    payback_period: str,
    risk_assessment: str
):
    return FEASIBILITY_BACKGROUND_PROMPT.format(
        name=name,
        outline=outline,
        contexts=contexts,
        analysis_result=analysis_result,
        user_prompt=user_prompt,
        investment_amount=investment_amount,
        expect_return_rate=expect_return_rate,
        payback_period=payback_period,
        risk_assessment=risk_assessment
    )
async def final_market_background_prompt(
    name: str,
    user_prompt: str,
    analysis_result: str,
    outline: str,
    contexts: list[str],
    document_type: str
):
    return MARKET_INVESTMENT_BACKGROUND_PROMPT.format(
        name=name,
        user_prompt=user_prompt,
        contexts=contexts,
        outline=outline,
        document_type=document_type,
        analysis_result=analysis_result,
        literature_prompt=LITERATURE_PROMPT
    )
async def final_literature_background_prompt(
    name: str,
    user_prompt: str,
    analysis_result: str,
    outline: str,
    contexts: list[str]
):
    return LITERATURE_REVIEW_BACKGROUND_PROMPT.format(
        outline=outline,
        name=name,
        contexts=contexts,
        analysis_result=analysis_result,
        user_prompt=user_prompt,
        literature_prompt=LITERATURE_PROMPT
    )

async def generate_meeting_step_two(
    project_id: str,
    current_user: UserResponse
):
    """生成正文"""
    config_db = await get_project_data(project_id=project_id)
    starttime = datetime.now()
    logger.info(f"报告开始时间：{starttime.strftime('%Y-%m-%d %H:%M:%S')}")

    await check_user_usage_limit(current_user.id)
    
    model = config_db.model
    
    await remove_storage(project_id=project_id, flag='report')
    # 获取config_response用于生成提示词
    outer_prefix = ""
    
    async def generate_data(
        callback: Callable[[str], None],
        complete_callback: Callable[[str, str], None],
        error_callback: Callable[[str], None]
    ):
        nonlocal outer_prefix
        # 检查是否已有大纲
        messages, flag, prefix = await get_special_two_data(
            project_config=config_db
        )
        outer_prefix = prefix
        await stream_llm_and_save(
            messages=messages,
            user=current_user, 
            callback=callback,
            complete_callback=complete_callback,
            error_callback=error_callback,
            flag=flag,
            model=model.model_name,
            apiKey=model.api_key,
            apiUrl=model.api_url,
            related_id=project_id
        )
    async def error_callback(error: str):
        await report_error_callback(error=error, project_id=project_id)
    async def complete_callback(
        id: str,
        all_content: str
    ):
        await report_complete_callback(
            all_content=all_content,
            project_id=project_id,
            prefix=outer_prefix,
            starttime=starttime
        )
    is_diff_count = False
    def cb():
        nonlocal is_diff_count
        is_diff_count = True
    async def callback(content: str):
        nonlocal is_diff_count
        await report_callback(
            content=content,
            project_id=project_id,
            user_id=current_user.id,
            is_diff_count=is_diff_count,
            cb=cb
        )
    # 创建异步任务
    task = asyncio.create_task(generate_data(
        complete_callback=complete_callback,
        callback=callback,
        error_callback=error_callback
    ))
    # 添加一个空字符串，用于触发流式处理
    report_content_manager.add_content(project_id, "", ContentStatus.NORMAL)
    # 将任务保存到内容管理器中
    report_content_manager.add_asyncio(project_id, task)
    config_db.status = ProjectConfigStatus.REPORT_GENERATING.value
    await config_db.save()
    
    return await get_one_project_config(project_id)
"""这里目前只对会议纪要进行处理。其他泛化模块不需要"""
async def get_special_two_data(
    project_config: ProjectConfig
):
    logger.info(f"项目 {project_config.id} 的文档类型: {project_config.doc_type}")
    if project_config.doc_type == DOC_TYPE.MEETING_GEN.value:
        outline_path = project_config.manual_modified_outline or project_config.ai_generated_outline
        if not outline_path:
            raise Exception("请先生成会议纪要章节概览")
        outline_content = read_file_content(file_path=outline_path)
        messages = [
            {
                "role": "user",
                "content": get_meeting_summary_prompt(
                    content=outline_content,
                    name=project_config.name
                )
            }
        ]
        return messages, CallLLMFlag.MEETING_SUMMARY.value, "meeting_summary"