from enum import Enum
from pydantic import BaseModel, UUID4, Field
from typing import Dict, Optional, List
from datetime import datetime
from app.utils.utils import PageQuery
from app.api.schemas.insight.inspiration import InspirationSource
from app.models.insight.inspirations_canvas_report_relation import InspirationSourceType


class CanvasType(str, Enum):
    """知识卡片类型枚举"""
    LITERATURE = "LITERATURE"  # 文献
    NOTE = "NOTE"    # 知识
    ARTICLE = "ARTICLE"       # 文本
    VIDEO = "VIDEO"           # 视频
    INSPIRATION = "INSPIRATION" # 灵感


class CanvasSourceType(str, Enum):
    """知识卡片来源类型枚举"""
    NOTE = "NOTE"           # 泛知识平台
    KNOWLEDGE = "KNOWLEDGE" # 知识库-RAG
    LITERATURE = "LITERATURE" # 文献库
    INSPIRATION = "INSPIRATION" # 灵感


class RelatedNote(BaseModel):
    """关联笔记模型"""
    basicId: str = Field(..., description="笔记ID")
    title: str = Field(..., description="笔记标题")
    summary: Optional[str] = Field(None, description="笔记概述")
    editTime: Optional[datetime] = Field(None, description="笔记修改时间")
    commonTags: Optional[List[str]] = Field(default=[], description="共同标签")


class AIQuestion(BaseModel):
    """AI问答模型"""
    question: str = Field(..., description="AI提问内容")
    answer: str = Field(..., description="AI回答内容")


class KeyNote(BaseModel):
    """重点注释模型"""
    keynote_sub_title: List[str] = Field(default=[], description="重点注释子标题列表")
    keynote_title: str = Field(..., description="重点注释标题")


class KnowledgeCanvasBase(BaseModel):
    """知识画布基础模型"""
    name: str = Field(..., description="知识画布名称")
    source_type: Optional[CanvasSourceType] = Field(None, description="来源类型：笔记、知识、文献")
    type: Optional[CanvasType] = Field(None, description="画布类型：文献、知识、短文、公式")
    summary: str = Field(..., description="知识画布概要")
    key_notes: List[str] = Field(default=[], description="重点注释列表")
    related_notes: List[RelatedNote] = Field(default=[], description="关联笔记列表")
    ai_questions: List[AIQuestion] = Field(default=[], description="AI问答列表")
    image_url: Optional[str] = Field(None, description="知识画布图片URL")
    original_article_truncated: Optional[str] = Field(None, description="原始文章截取")
    tags: List[str] = Field(default=[], description="标签列表")
    original_article: Optional[str] = Field(None, description="原始文章")
    ai_outline: Optional[str] = Field(None, description="AI大纲")
    user_notes: Optional[str] = Field(None, description="用户注释")
    note_update_at: Optional[datetime] = Field(None, description="笔记修改时间")
    author: Optional[str] = Field(None, description="作者")
    reference: Optional[str] = Field(None, description="引用")
    source: Optional[str] = Field(None, description="来源")
    inspiration_source: List[InspirationSource] = Field(default=[], description="灵感来源")
    detailed_description: Optional[str] = Field(None, description="详细描述")
    ai_expanded: Optional[str] = Field(None, description="AI扩写")
    ai_analysis: Optional[str] = Field(None, description="AI分析")
    ai_keynotes: Optional[str] = Field(None, description="AI重点注释")
    ai_outline: Optional[str] = Field(None, description="AI大纲")
    user_id: Optional[UUID4] = Field(None, description="创建用户ID")
    note_type: Optional[int] = Field(None, description="笔记类型")


class KnowledgeCanvasCreate(KnowledgeCanvasBase):
    """创建知识画布请求模型"""
    pass


class KnowledgeCanvasUpdate(BaseModel):
    """更新知识画布请求模型"""
    name: Optional[str] = Field(None, description="知识画布名称")
    source_type: Optional[CanvasSourceType] = Field(None, description="来源类型：笔记、知识、文献")
    type: Optional[CanvasType] = Field(None, description="画布类型：文献、知识、短文、公式")
    summary: Optional[str] = Field(None, description="知识画布概要")
    key_notes: Optional[List[str]] = Field(None, description="重点注释列表")
    related_notes: Optional[List[RelatedNote]] = Field(None, description="关联笔记列表")
    ai_questions: Optional[List[AIQuestion]] = Field(None, description="AI问答列表")
    image_url: Optional[str] = Field(None, description="知识画布图片URL")
    original_article_truncated: Optional[str] = Field(None, description="原始文章截取")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    original_article: Optional[str] = Field(None, description="原始文章")
    ai_outline: Optional[str] = Field(None, description="AI大纲")
    user_notes: Optional[str] = Field(None, description="用户注释")
    note_update_at: Optional[datetime] = Field(None, description="笔记修改时间")
    author: Optional[str] = Field(None, description="作者")
    reference: Optional[str] = Field(None, description="引用")
    source: Optional[str] = Field(None, description="来源")
    inspiration_source: Optional[List[InspirationSource]] = Field(None, description="灵感来源")
    detailed_description: Optional[str] = Field(None, description="详细描述")
    ai_expanded: Optional[str] = Field(None, description="AI扩写")
    ai_analysis: Optional[str] = Field(None, description="AI分析")
    ai_outline: Optional[str] = Field(None, description="AI大纲")
    ai_keynotes: Optional[str] = Field(None, description="AI重点注释")
    user_id: Optional[UUID4] = Field(None, description="创建用户ID")


class KnowledgeCanvasInDB(KnowledgeCanvasBase):
    """数据库中的知识画布模型"""
    id: UUID4 = Field(..., description="知识画布唯一标识符")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    is_deleted: bool = Field(..., description="是否已删除")

    class Config:
        orm_mode = True


class KnowledgeCanvasResponse(KnowledgeCanvasInDB):
    """知识画布响应模型"""
    ai_keynotes: List[KeyNote] = Field(default=[], description="AI重点注释")
    insight_report_id: Optional[UUID4] = Field(None, description="关联的insight报告ID")
    cleaned_outline: List[Dict] = Field(None, description="清洗后的AI大纲")
    pass


class KnowledgeCanvasListResponse(BaseModel):
    """知识画布列表响应模型（精简版）"""
    id: UUID4 = Field(..., description="知识画布唯一标识符")
    name: str = Field(..., description="知识画布名称")
    source_type: Optional[CanvasSourceType] = Field(None, description="来源类型：笔记、知识、文献")
    type: Optional[CanvasType] = Field(None, description="画布类型：文献、知识、短文、公式")
    summary: str = Field(..., description="知识画布概要")
    key_notes: List[str] = Field(default=[], description="重点注释列表")
    related_notes: List[RelatedNote] = Field(default=[], description="关联笔记列表")
    image_url: Optional[str] = Field(None, description="知识画布图片URL")
    original_article_truncated: Optional[str] = Field(None, description="原始文章截取")
    tags: List[str] = Field(default=[], description="标签列表")
    user_notes: Optional[str] = Field(None, description="用户注释")
    note_update_at: Optional[datetime] = Field(None, description="笔记修改时间")
    author: Optional[str] = Field(None, description="作者")
    reference: Optional[str] = Field(None, description="引用")
    source: Optional[str] = Field(None, description="来源")
    inspiration_source: List[InspirationSource] = Field(default=[], description="灵感来源")
    detailed_description: Optional[str] = Field(None, description="详细描述")
    ai_expanded: Optional[str] = Field(None, description="AI扩写")
    ai_analysis: Optional[str] = Field(None, description="AI分析")
    ai_outline: Optional[str] = Field(None, description="AI大纲")
    ai_outline_nojson: Optional[str] = Field(None, description="AI大纲非JSON格式内容，如果ai_outline是JSON格式则为空")
    ai_keynotes: List[KeyNote] = Field(default=[], description="AI重点注释")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    is_deleted: bool = Field(..., description="是否已删除")
    insight_report_id: Optional[UUID4] = Field(None, description="报告ID")
    class Config:
        orm_mode = True


class KnowledgeCanvasQueryParams(PageQuery):
    """知识画布查询参数"""
    keyword: Optional[str] = Field(None, description="关键词搜索，支持名称、概要、重点注释、关联笔记、AI问答的模糊搜索")
    source_type: Optional[str] = Field(None, description="来源类型筛选：笔记、知识、文献")
    type: Optional[str] = Field(None, description="画布类型筛选：文献、知识、短文、公式")
    sort_field: Optional[str] = Field(None, description="排序字段")
    sort_order: Optional[str] = Field(None, description="排序方式：asc（升序）或desc（降序）")


# 定义请求体模型
class CreateKnowledgeCanvasRequest(BaseModel):
    basic_ids: List[str]

# 定义泛知识列表项响应模型
class KnowledgeNoteItem(BaseModel):
    basicId: str
    title: str
    url: Optional[str] = None
    update_time: Optional[str] = None
    tags: List[str] = []
    summary: Optional[str] = None
    type: Optional[str] = None


class SaveInsightRequest(BaseModel):
    """保存知识卡片请求模型"""
    name: str = Field(..., description="知识卡片名称")
    inspiration_source: Optional[List[InspirationSource]] = Field(None, description="知识卡片来源")
    tags: Optional[List[str]] = Field(None, description="知识卡片标签列表")
    summary: Optional[str] = Field(None, description="知识卡片综述")
    


class SaveInsightBatchRequest(BaseModel):
    """批量保存知识卡片请求模型"""
    items: List[SaveInsightRequest] = Field(..., description="知识卡片列表")


class GenerateOutlineRequest(BaseModel):
    """生成大纲请求模型"""
    content_id: UUID4 = Field(..., description="需要生成大纲的内容ID（知识卡片ID或灵感库ID）")
    content_type: InspirationSourceType = Field(..., description="内容类型：CANVAS-知识卡片，INSPIRATION-灵感库")
    report_type: str = Field(..., description="报告类型，对应 ReportType 枚举值")


class GenerateOutlineResponse(BaseModel):
    """生成大纲响应模型"""
    outline_content: str = Field(..., description="生成的大纲内容")
    report_id: UUID4 = Field(..., description="创建的报告ID")
    content_name: str = Field(..., description="内容名称")
    report_type: str = Field(..., description="报告类型")
