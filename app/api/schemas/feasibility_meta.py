from pydantic import BaseModel, Field
from typing import Optional
from decimal import Decimal
from datetime import datetime
from uuid import UUID


class FeasibilityMetaCreate(BaseModel):
    """可行性报告创建请求模型"""
    expect_return_rate: Optional[float] = Field(None, description="预期内部收益率", ge=0, le=100)
    payback_period: Optional[float] = Field(None, description="投资回收期（年）", ge=0)
    analysis_method: Optional[str] = Field(None, max_length=50, description="分析方法")
    investment_amount: Optional[Decimal] = Field(None, description="总投资额（元）", ge=0)
    risk_assessment: Optional[str] = Field(None, description="风险评估")
    
    class Config:
        json_schema_extra = {
            "example": {
                "expect_return_rate": 15.5,
                "payback_period": 3.5,
                "analysis_method": "净现值法",
                "investment_amount": 1000000.00,
                "risk_assessment": "项目风险较低，市场前景良好"
            }
        }


class FeasibilityMetaResponse(BaseModel):
    """可行性报告响应模型"""
    id: UUID = Field(description="可行性报告配置ID")
    expect_return_rate: Optional[float] = Field(None, description="预期内部收益率")
    payback_period: Optional[float] = Field(None, description="投资回收期（年）")
    analysis_method: Optional[str] = Field(None, description="分析方法")
    investment_amount: Optional[Decimal] = Field(None, description="总投资额（元）")
    risk_assessment: Optional[str] = Field(None, description="风险评估")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "expect_return_rate": 15.5,
                "payback_period": 3.5,
                "analysis_method": "净现值法",
                "investment_amount": 1000000.00,
                "risk_assessment": "项目风险较低，市场前景良好",
                "created_at": "2024-01-01T10:00:00",
                "updated_at": "2024-01-01T10:00:00"
            }
        }
