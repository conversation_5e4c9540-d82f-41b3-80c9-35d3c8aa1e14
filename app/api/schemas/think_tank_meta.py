from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from uuid import UUID

class ThinkTankMetaQuery(BaseModel):
    """智库元数据查询参数"""
    contain_deleted: Optional[bool] = Field(False, description="是否包含已删除的记录")

class ThinkTankMetaBase(BaseModel):
    """智库元数据基础模型"""
    target: Optional[str] = Field(None, max_length=50, description="目标受众")
    analysis_method: Optional[str] = Field(None, max_length=50, description="分析方法（选填）")

class ThinkTankMetaCreate(ThinkTankMetaBase):
    """创建智库元数据请求模型"""
    pass

class ThinkTankMetaUpdate(ThinkTankMetaBase):
    """更新智库元数据请求模型"""
    target: Optional[str] = Field(None, max_length=50, description="目标受众")
    analysis_method: Optional[str] = Field(None, max_length=50, description="分析方法（选填）")

class ThinkTankMetaResponse(ThinkTankMetaBase):
    """智库元数据响应模型"""
    id: UUID = Field(..., description="唯一标识")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    is_deleted: bool = Field(..., description="是否已删除")
    deleted_at: Optional[datetime] = Field(None, description="删除时间")

    class Config:
        from_attributes = True