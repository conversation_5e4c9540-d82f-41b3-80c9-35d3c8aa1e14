"""
PaperToX Agent Schema 定义
用于 API 请求和响应的数据验证和序列化
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
import json


class PaperToXAgentStatus(str, Enum):
    """PaperToX Agent 状态枚举"""
    AVAILABLE = "available"              # 可用
    IN_DEVELOPMENT = "in_development"    # 开发中
    COMING_SOON = "coming_soon"          # 即将推出


class PaperToXAgentVersion(str, Enum):
    """PaperToX Agent 版本分类枚举"""
    BASIC = "BASIC"        # 基础版本
    ADVANCED = "ADVANCED"  # 进阶版本
    EXPERT = "EXPERT"      # 专家版本


class PaperToXAgentCategory(str, Enum):
    """PaperToX Agent 分类枚举"""
    CORE_TOOLS = "CORE_TOOLS"                # 核心工具
    ENHANCEMENT_MODULES = "ENHANCEMENT_MODULES"  # 增强模块
    CLINICAL_SPECIALTY = "CLINICAL_SPECIALTY"    # 临床专业
    ANALYSIS_TOOLS = "ANALYSIS_TOOLS"            # 分析工具
    VISUALIZATION = "VISUALIZATION"              # 可视化
    RESEARCH_ASSISTANCE = "RESEARCH_ASSISTANCE"  # 研究辅助


class PaperToXAgentBase(BaseModel):
    """PaperToX Agent 基础 Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="Agent名称")
    short_description: str = Field(..., min_length=1, max_length=200, description="简短功能描述")
    long_description: Optional[str] = Field(None, description="详细功能描述")
    icon_identifier: Optional[str] = Field(None, max_length=50, description="图标标识符")
    url: Optional[str] = Field(None, max_length=500, description="跳转页面URL")
    status: PaperToXAgentStatus = Field(PaperToXAgentStatus.IN_DEVELOPMENT, description="状态")
    usage_count: int = Field(0, ge=0, description="使用次数")
    category: Optional[str] = Field(None, max_length=50, description="分类")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    version_type: Optional[str] = Field(None, max_length=20, description="版本分类：BASIC, ADVANCED, EXPERT")
    sort_order: int = Field(0, description="排序权重")
    is_active: bool = Field(True, description="是否启用")
    
    @validator('tags', pre=True)
    def validate_tags(cls, v):
        """验证标签格式"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return [v]
        if isinstance(v, list):
            return v
        return None


class PaperToXAgentCreate(PaperToXAgentBase):
    """创建 PaperToX Agent 请求 Schema"""
    organization_id: Optional[str] = Field(None, description="所属机构ID（可选，不指定则为无机构Agent）")

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Paper2Code",
                "short_description": "代码生成转换",
                "long_description": "从文献中提取算法并生成可执行代码,支持多语言和自动化单元测试",
                "icon_identifier": "code",
                "url": "https://example.com/paper2code",
                "status": "available",
                "usage_count": 0,
                "category": "代码生成",
                "tags": ["代码", "算法", "多语言"],
                "sort_order": 1,
                "is_active": True
            }
        }


class PaperToXAgentUpdate(BaseModel):
    """更新 PaperToX Agent 请求 Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Agent名称")
    short_description: Optional[str] = Field(None, min_length=1, max_length=200, description="简短功能描述")
    long_description: Optional[str] = Field(None, description="详细功能描述")
    icon_identifier: Optional[str] = Field(None, max_length=50, description="图标标识符")
    url: Optional[str] = Field(None, max_length=500, description="跳转页面URL")
    status: Optional[PaperToXAgentStatus] = Field(None, description="状态")
    usage_count: Optional[int] = Field(None, ge=0, description="使用次数")
    category: Optional[str] = Field(None, max_length=50, description="分类")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    version_type: Optional[str] = Field(None, max_length=20, description="版本分类：BASIC, ADVANCED, EXPERT")
    sort_order: Optional[int] = Field(None, description="排序权重")
    is_active: Optional[bool] = Field(None, description="是否启用")
    
    @validator('tags', pre=True)
    def validate_tags(cls, v):
        """验证标签格式"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return [v]
        if isinstance(v, list):
            return v
        return None
    
    class Config:
        json_schema_extra = {
            "example": {
                "name": "Paper2Code Updated",
                "short_description": "代码生成转换 - 更新版",
                "url": "https://example.com/paper2code-updated",
                "status": "available",
                "usage_count": 1000,
                "tags": ["代码", "算法", "多语言", "更新"]
            }
        }


class PaperToXAgentResponse(PaperToXAgentBase):
    """PaperToX Agent 响应 Schema"""
    id: str = Field(..., description="Agent ID")
    organization_id: Optional[str] = Field(None, description="所属机构ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    created_by: Optional[str] = Field(None, description="创建人ID")
    updated_by: Optional[str] = Field(None, description="更新人ID")
    is_deleted: bool = Field(False, description="是否逻辑删除")
    deleted_at: Optional[datetime] = Field(None, description="删除时间")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "name": "Paper2Code",
                "short_description": "代码生成转换",
                "long_description": "从文献中提取算法并生成可执行代码,支持多语言和自动化单元测试",
                "icon_identifier": "code",
                "url": "https://example.com/paper2code",
                "status": "available",
                "usage_count": 1000,
                "category": "代码生成",
                "tags": ["代码", "算法", "多语言"],
                "sort_order": 1,
                "is_active": True,
                "organization_id": "550e8400-e29b-41d4-a716-446655440001",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "created_by": "550e8400-e29b-41d4-a716-446655440002",
                "updated_by": "550e8400-e29b-41d4-a716-446655440003",
                "is_deleted": False,
                "deleted_at": None
            }
        }


class PaperToXAgentListResponse(BaseModel):
    """PaperToX Agent 列表响应 Schema"""
    items: List[PaperToXAgentResponse] = Field(..., description="Agent列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")
    
    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "550e8400-e29b-41d4-a716-446655440000",
                        "name": "Paper2Code",
                        "short_description": "代码生成转换",
                        "url": "https://example.com/paper2code",
                        "status": "available",
                        "usage_count": 1000,
                        "category": "代码生成",
                        "tags": ["代码", "算法", "多语言"],
                        "sort_order": 1,
                        "is_active": True,
                        "created_at": "2024-01-01T00:00:00Z",
                        "updated_at": "2024-01-01T00:00:00Z"
                    }
                ],
                "total": 1,
                "page": 1,
                "page_size": 10,
                "total_pages": 1
            }
        }


class PaperToXAgentQueryParams(BaseModel):
    """PaperToX Agent 查询参数 Schema"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")
    name: Optional[str] = Field(None, description="按名称搜索")
    status: Optional[PaperToXAgentStatus] = Field(None, description="按状态筛选")
    category: Optional[str] = Field(None, description="按分类筛选")
    tags: Optional[str] = Field(None, description="按标签搜索")
    is_active: Optional[bool] = Field(None, description="按启用状态筛选")
    sort_by: str = Field("sort_order", description="排序字段")
    sort_order: str = Field("asc", pattern="^(asc|desc)$", description="排序方向")
    
    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "page_size": 10,
                "name": "Paper2",
                "status": "available",
                "category": "代码生成",
                "tags": "代码",
                "is_active": True,
                "sort_by": "sort_order",
                "sort_order": "asc"
            }
        }


