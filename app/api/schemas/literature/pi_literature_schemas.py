"""
PI文献管理相关 Schema 模型
"""

from pydantic import BaseModel
from datetime import datetime
from typing import List, Optional, Dict, Any
from decimal import Decimal
from uuid import UUID

from app.models.literature.pi_literatures import (
    LiteratureSource,
    FileParseStatus
)


class AuthorInfo(BaseModel):
    """作者信息"""
    name: str
    affiliation: Optional[str] = None
    email: Optional[str] = None


class PiLiteraturesResponse(BaseModel):
    """文献响应模型"""
    id: UUID
    name: str
    doi: Optional[str] = None
    authors: List[AuthorInfo] = []
    keywords: List[str] = []
    abstract: Optional[str] = None
    journal: Optional[str] = None
    reference_count: int
    literature_date: Optional[str] = None
    article_type: str
    literature_source: str
    impact_factor: Optional[Decimal] = None
    file_parse_status: str
    parse_error_message: Optional[str] = None
    original_file_path: Optional[str] = None
    parsed_content: Optional[str] = None  # 文件路径
    parsed_content_text: Optional[str] = None  # 文件内容
    upload_file_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    organization_id: Optional[UUID] = None
    is_deleted: bool
    deleted_time: Optional[datetime] = None
    created_time: datetime
    updated_time: datetime
    view_count: int
    download_count: int
    ai_analysis_count: int

    class Config:
        from_attributes = True


class PiLiteraturesAssistantResponse(BaseModel):
    """AI助手响应模型"""
    id: UUID
    literature_id: UUID
    ai_summary: Optional[str] = None
    follow_up_questions: Optional[str] = None
    analysis_content: Optional[str] = None
    key_points: Optional[str] = None
    outline: Optional[str] = None
    cleaned_outline: Optional[List[Dict[str, Any]]] = None
    combined: Optional[str] = None
    creator_id: UUID
    created_time: datetime
    updated_time: datetime

    class Config:
        from_attributes = True


class PiLiteraturesCreateRequest(BaseModel):
    """创建文献请求模型"""
    name: Optional[str] = None
    doi: Optional[str] = None
    authors: Optional[List[AuthorInfo]] = []
    keywords: Optional[List[str]] = []
    abstract: Optional[str] = None
    journal: Optional[str] = None
    literature_date: Optional[str] = None
    literature_source: Optional[LiteratureSource] = LiteratureSource.MANUAL_UPLOAD
    impact_factor: Optional[Decimal] = None


class PiLiteraturesAssistantCreateRequest(BaseModel):
    """创建AI助手请求模型"""
    literature_id: UUID
    generate_summary: bool = True
    generate_questions: bool = True
    generate_analysis: bool = True
    generate_key_points: bool = True
    generate_outline: bool = True


class PiLiteraturesUpdateRequest(BaseModel):
    """更新文献请求模型"""
    name: Optional[str] = None
    doi: Optional[str] = None
    authors: Optional[List[AuthorInfo]] = None
    keywords: Optional[List[str]] = None
    abstract: Optional[str] = None
    journal: Optional[str] = None
    literature_date: Optional[str] = None
    literature_source: Optional[LiteratureSource] = None
    impact_factor: Optional[Decimal] = None
    file_parse_status: Optional[FileParseStatus] = None


# 响应模型
class LiteratureUploadResponse(BaseModel):
    """文献上传响应模型"""
    literature_id: UUID
    ai_assistant_id: UUID
    filename: str
    parse_status: str


class PaginationInfo(BaseModel):
    """分页信息模型"""
    page: int
    size: int
    total: int
    pages: int


class LiteratureListItem(BaseModel):
    """文献列表项模型"""
    id: UUID
    name: str
    doi: Optional[str] = None
    authors: List[AuthorInfo] = []
    keywords: List[str] = []
    abstract: Optional[str] = None
    journal: Optional[str] = None
    reference_count: int
    literature_date: Optional[str] = None
    article_type: str
    literature_source: str
    impact_factor: Optional[Decimal] = None
    file_parse_status: str
    creator_name: str
    created_time: str
    view_count: int
    download_count: int
    ai_analysis_count: int


class LiteratureListResponse(BaseModel):
    """文献列表响应模型"""
    items: List[LiteratureListItem]
    pagination: PaginationInfo


class LiteratureDetailResponse(BaseModel):
    """文献详情响应模型"""
    literature: PiLiteraturesResponse
    ai_assistant: Optional[PiLiteraturesAssistantResponse] = None


class LiteratureDeleteResponse(BaseModel):
    """文献删除响应模型"""
    message: str
    deleted_id: UUID


class LiteratureStatusUpdateResponse(BaseModel):
    """文献状态更新响应模型"""
    literature_id: UUID
    old_status: str
    new_status: str
    message: str


class LiteratureStatsResponse(BaseModel):
    """文献统计响应模型"""
    total_literatures: int
    parsed_literatures: int
    parsing_literatures: int
    failed_literatures: int
    total_views: int
    total_downloads: int
    total_ai_analyses: int
    recent_uploads: int  # 最近7天上传数量


# AI接口请求模型
class LiteratureAIRequest(BaseModel):
    """AI接口通用请求模型"""
    literature_id: UUID


class LiteratureProbeAnswerRequest(BaseModel):
    """AI追问回答请求模型"""
    literature_id: UUID
    question: str