from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field

from app.models.paper2code import Paper2CodeStatus


class Paper2CodeJobCreate(BaseModel):
    """创建Paper2Code任务请求"""
    job_name: str = Field(..., description="任务名称", max_length=255)
    description: Optional[str] = Field(None, description="任务描述")


class Paper2CodeJobResponse(BaseModel):
    """Paper2Code任务响应"""
    id: UUID
    job_name: str
    description: Optional[str]
    status: Paper2CodeStatus

    # 文件信息
    original_file_name: str
    file_type: str
    original_file_size: int

    # 进度信息
    progress_percentage: int
    current_stage: Optional[str]

    # 结果路径
    zip_file_path: Optional[str]

    # 统计信息
    total_files_generated: int
    processing_time_seconds: Optional[int]

    # 错误信息
    error_message: Optional[str]

    # 时间戳
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]

    class Config:
        from_attributes = True


class Paper2CodeJobList(BaseModel):
    """Paper2Code任务列表响应"""
    id: UUID
    job_name: str
    status: Paper2CodeStatus
    original_file_name: str
    file_type: str
    progress_percentage: int
    current_stage: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Paper2CodeFileResponse(BaseModel):
    """生成文件响应"""
    id: UUID
    file_name: str
    relative_path: str
    file_size: int
    file_type: str
    content_preview: Optional[str]
    line_count: Optional[int]
    generation_stage: str
    created_at: datetime

    class Config:
        from_attributes = True


class Paper2CodeLogResponse(BaseModel):
    """处理日志响应"""
    id: UUID
    stage: str
    level: str
    message: str
    details: Optional[Dict[str, Any]]
    created_at: datetime

    class Config:
        from_attributes = True


class Paper2CodeJobProgress(BaseModel):
    """任务进度更新"""
    status: Paper2CodeStatus
    progress_percentage: int
    current_stage: str
    message: Optional[str] = None


class Paper2CodeJobDetail(Paper2CodeJobResponse):
    """任务详情响应"""
    generated_files: List[Paper2CodeFileResponse] = []
    processing_logs: List[Paper2CodeLogResponse] = []

    # 处理结果详情
    planning_result: Optional[Dict[str, Any]]
    analyzing_result: Optional[Dict[str, Any]]
    coding_result: Optional[Dict[str, Any]]

    class Config:
        from_attributes = True


class Paper2CodeUploadResponse(BaseModel):
    """文件上传响应"""
    success: bool
    job_id: UUID
    message: str
    file_name: str
    file_size: int


class Paper2CodeDownloadInfo(BaseModel):
    """下载信息响应"""
    download_url: str
    file_name: str
    file_size: int
    expires_at: datetime