from pydantic import BaseModel, UUID4
from typing import Optional
from datetime import datetime

class VoiceTextResponse(BaseModel):
    """录音转文字响应模型"""
    id: UUID4
    text_content: Optional[str] = None
    upload_files_id: Optional[UUID4] = None
    user_id: Optional[UUID4] = None
    voice_time: Optional[str] = None  # 字符串类型，存储整数秒数
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class VoiceTextUpdate(BaseModel):
    """录音转文字更新模型"""
    text_content: Optional[str] = None
    upload_file_id: Optional[UUID4] = None
    voice_time: Optional[str] = None  # 字符串类型，存储整数秒数

class VoiceTextEnd(BaseModel):
    """结束录音请求模型"""
    voice_id: UUID4 