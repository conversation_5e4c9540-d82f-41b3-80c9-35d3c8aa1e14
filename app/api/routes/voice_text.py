from fastapi import APIRouter, Request
from app.api.repository.voice_text import create_voice_text,end_voice_text
from app.api.schemas.voice_text import VoiceTextResponse, VoiceTextEnd
from app.utils.utils import (
    send_data,
    ResponseModel
)
from app.api.deps import get_current_user_from_state
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.post("/start-voice", response_model=ResponseModel[VoiceTextResponse], summary="开始录音创建录音ID")
async def create_data(
    request: Request
):
    try:
        current_user = get_current_user_from_state(request=request)
        result = await create_voice_text(
            user_id=current_user.id
        )
        return send_data(True, result)
    except Exception as e:
        logger.error(f"创建会议纪要录音失败:{str(e)}")
        return send_data(False, None, str(e))
@router.post("/end-voice", response_model=ResponseModel[bool], summary="开始录音创建录音ID")
async def end_record(
    data: VoiceTextEnd,
    request: Request
):
    try:
        current_user = get_current_user_from_state(request=request)  
        result = await end_voice_text(voice_id=data.voice_id, user_id=current_user.id)
        return send_data(True, result)
    except Exception as e:
        logger.error(f"结束录音失败：{str(e)}")
        return send_data(False, None, str(e))
