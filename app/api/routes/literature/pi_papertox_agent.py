"""
PaperToX Agent 路由接口
提供 PaperToX Agent 广场的 API 接口
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from typing import List, Optional
from uuid import UUID
import logging
import json
from datetime import datetime

from app.api.deps import get_current_user
from app.api.schemas.literature.pi_papertox_agent_schemas import (
    PaperToXAgentCreate,
    PaperToXAgentUpdate,
    PaperToXAgentResponse,
    PaperToXAgentListResponse,
    PaperToXAgentStatus
)
from app.models.user import User
from app.models.pi_papertox_agent import PiPaperToXAgent, PaperToXAgentStatus as ModelStatus
from app.api.schemas.role import InsetRole
from app.utils.utils import send_data
from app.core.logging import get_logger

router = APIRouter(prefix="/pi-papertox-agent", tags=["PI PaperToX Agent"])
logger = get_logger(__name__)


async def agent_to_dict(agent: PiPaperToXAgent) -> dict:
    """将Agent对象转换为字典"""
    # 处理tags字段
    tags = None
    if agent.tags:
        try:
            tags = json.loads(agent.tags) if isinstance(agent.tags, str) else agent.tags
        except (json.JSONDecodeError, TypeError):
            tags = [agent.tags] if agent.tags else None

    return {
        "id": str(agent.id),
        "name": agent.name,
        "short_description": agent.short_description,
        "long_description": agent.long_description,
        "icon_identifier": agent.icon_identifier,
        "url": agent.url,
        "status": agent.status,
        "usage_count": agent.usage_count,
        "category": agent.category,
        "tags": tags,
        "version_type": agent.version_type,
        "sort_order": agent.sort_order,
        "is_active": agent.is_active,
        "organization_id": str(agent.organization_id) if agent.organization_id else None,
        "created_at": agent.created_at.isoformat() if agent.created_at else None,
        "updated_at": agent.updated_at.isoformat() if agent.updated_at else None,
        "created_by": str(agent.created_by_id) if agent.created_by_id else None,
        "updated_by": str(agent.updated_by_id) if agent.updated_by_id else None,
        "is_deleted": agent.is_deleted,
        "deleted_at": agent.deleted_at.isoformat() if agent.deleted_at else None
    }



@router.get(
    "/",
    summary="获取 PaperToX Agent 列表",
    description="分页获取 PaperToX Agent 列表，支持搜索、筛选和排序"
)
async def get_agent_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    keyword: Optional[str] = Query(None, description="关键词搜索（name、标签、分类、状态、版本）"),
    name: Optional[str] = Query(None, description="按名称搜索"),
    status: Optional[PaperToXAgentStatus] = Query(None, description="按状态筛选"),
    category: Optional[str] = Query(None, description="按分类筛选"),
    tags: Optional[str] = Query(None, description="按标签搜索"),
    version_type: Optional[str] = Query(None, description="按版本筛选"),
    is_active: Optional[bool] = Query(None, description="按启用状态筛选"),
    sort_by: str = Query("sort_order", description="排序字段"),
    sort_order: str = Query("asc", regex="^(asc|desc)$", description="排序方向"),
    current_user: User = Depends(get_current_user)
):
    """
    获取 PaperToX Agent 列表

    支持多种搜索和筛选条件，返回分页的 Agent 列表
    
    关键词搜索说明：
    - keyword参数会同时搜索name、tags、category、status、version_type字段
    - 可以与其他单独的筛选条件组合使用
    """
    try:
        # 构建查询
        query = PiPaperToXAgent.filter(is_deleted=False)

        # 所有用户都可以查看所有Agent，无机构权限验证
        
        from tortoise.expressions import Q

        # 关键词搜索 - 搜索name、tags、category、status、version_type
        if keyword:
            keyword_query = Q(name__icontains=keyword) | \
                          Q(tags__icontains=keyword) | \
                          Q(category__icontains=keyword) | \
                          Q(status__icontains=keyword) | \
                          Q(version_type__icontains=keyword) | \
                          Q(short_description__icontains=keyword) | \
                          Q(long_description__icontains=keyword)
            query = query.filter(keyword_query)

        # 单独的搜索条件（与keyword可以并存）
        if name:
            query = query.filter(
                Q(name__icontains=name) |
                Q(short_description__icontains=name) |
                Q(long_description__icontains=name)
            )

        if status:
            query = query.filter(status=status)

        if category:
            query = query.filter(category=category)

        if tags:
            query = query.filter(tags__icontains=tags)

        if version_type:
            query = query.filter(version_type=version_type)

        if is_active is not None:
            query = query.filter(is_active=is_active)

        # 获取总数
        total = await query.count()

        # 排序
        order_field = sort_by if sort_by in ["sort_order", "name", "created_at", "updated_at", "usage_count"] else "sort_order"
        if sort_order == "desc":
            order_field = f"-{order_field}"

        # 分页
        offset = (page - 1) * page_size
        agents = await query.order_by(order_field).offset(offset).limit(page_size)

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        # 构建响应数据
        response_data = {
            "items": [await agent_to_dict(agent) for agent in agents],
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages
        }

        logger.info(f"用户 {current_user.username} 获取 PaperToX Agent 列表成功: {len(agents)}/{total} 条记录")
        return send_data(True, response_data, "获取列表成功")

    except Exception as e:
        logger.error(f"获取 PaperToX Agent 列表失败: {str(e)}")
        return send_data(False, None, "获取列表失败")


@router.get(
    "/{agent_id}",
    summary="获取 PaperToX Agent 详情",
    description="根据 ID 获取 PaperToX Agent 的详细信息"
)
async def get_agent_detail(
    agent_id: UUID = Path(..., description="Agent ID"),
    current_user: User = Depends(get_current_user)
):
    """
    获取 PaperToX Agent 详情

    根据 Agent ID 获取详细信息
    """
    try:
        agent = await PiPaperToXAgent.get_or_none(id=agent_id, is_deleted=False)

        if not agent:
            return send_data(False, None, "Agent 不存在")

        # 所有用户都可以查看Agent详情，无机构权限验证

        logger.info(f"用户 {current_user.username} 获取 PaperToX Agent 详情成功: {agent.name}")
        return send_data(True, await agent_to_dict(agent), "获取详情成功")

    except Exception as e:
        logger.error(f"获取 PaperToX Agent 详情失败: {str(e)}")
        return send_data(False, None, "获取详情失败")


@router.post(
    "/",
    summary="创建 PaperToX Agent",
    description="创建新的 PaperToX Agent"
)
async def create_agent(
    agent_data: PaperToXAgentCreate,
    current_user: User = Depends(get_current_user)
):
    """
    创建 PaperToX Agent

    只有超级管理员可以创建 Agent
    """
    try:
        # 权限检查 - 只有超级管理员可以创建
        if current_user.role.identifier != InsetRole.SUPER_ADMIN:
            return send_data(False, None, "权限不足，只有超级管理员可以创建 Agent")

        # 检查名称是否已存在
        existing_agent = await PiPaperToXAgent.get_or_none(name=agent_data.name, is_deleted=False)
        if existing_agent:
            return send_data(False, None, "Agent 名称已存在")

        # 确定机构ID - 优先使用前端传递的值，允许为空
        organization_id = None
        if agent_data.organization_id:
            # 前端传递了机构ID，使用它
            organization_id = UUID(agent_data.organization_id)
        # 如果前端未传递机构ID，则organization_id保持为None，表示不属于任何机构

        # 处理标签数据
        tags_json = None
        if agent_data.tags:
            tags_json = json.dumps(agent_data.tags, ensure_ascii=False)

        # 创建 Agent
        agent = await PiPaperToXAgent.create(
            name=agent_data.name,
            short_description=agent_data.short_description,
            long_description=agent_data.long_description,
            icon_identifier=agent_data.icon_identifier,
            url=agent_data.url,
            status=agent_data.status,
            usage_count=agent_data.usage_count,
            category=agent_data.category,
            tags=tags_json,
            version_type=agent_data.version_type,
            sort_order=agent_data.sort_order,
            is_active=agent_data.is_active,
            organization_id=organization_id,
            created_by_id=current_user.id,
            updated_by_id=current_user.id
        )

        logger.info(f"用户 {current_user.username} 创建 PaperToX Agent 成功: {agent.name}")
        return send_data(True, await agent_to_dict(agent), "创建成功")

    except Exception as e:
        logger.error(f"创建 PaperToX Agent 失败: {str(e)}")
        return send_data(False, None, "创建失败")


@router.put(
    "/{agent_id}",
    summary="更新 PaperToX Agent",
    description="更新 PaperToX Agent 信息"
)
async def update_agent(
    agent_id: UUID = Path(..., description="Agent ID"),
    agent_data: PaperToXAgentUpdate = ...,
    current_user: User = Depends(get_current_user)
):
    """
    更新 PaperToX Agent

    只有超级管理员可以更新 Agent
    """
    try:
        # 权限检查 - 只有超级管理员可以更新
        if current_user.role.identifier != InsetRole.SUPER_ADMIN:
            return send_data(False, None, "权限不足，只有超级管理员可以更新 Agent")

        # 获取现有 Agent
        agent = await PiPaperToXAgent.get_or_none(id=agent_id, is_deleted=False)
        if not agent:
            return send_data(False, None, "Agent 不存在")

        # 超级管理员可以更新所有Agent，无机构权限验证

        # 检查名称是否重复（如果要更新名称）
        if agent_data.name and agent_data.name != agent.name:
            existing_agent = await PiPaperToXAgent.get_or_none(name=agent_data.name, is_deleted=False)
            if existing_agent and existing_agent.id != agent_id:
                return send_data(False, None, "Agent 名称已存在")

        # 更新字段
        update_data = agent_data.dict(exclude_unset=True)

        # 处理标签数据
        if 'tags' in update_data and update_data['tags'] is not None:
            update_data['tags'] = json.dumps(update_data['tags'], ensure_ascii=False)

        # 更新字段
        for field, value in update_data.items():
            if hasattr(agent, field):
                setattr(agent, field, value)

        agent.updated_by_id = current_user.id
        await agent.save()

        logger.info(f"用户 {current_user.username} 更新 PaperToX Agent 成功: {agent.name}")
        return send_data(True, await agent_to_dict(agent), "更新成功")

    except Exception as e:
        logger.error(f"更新 PaperToX Agent 失败: {str(e)}")
        return send_data(False, None, "更新失败")


@router.delete(
    "/{agent_id}",
    summary="删除 PaperToX Agent",
    description="逻辑删除 PaperToX Agent"
)
async def delete_agent(
    agent_id: UUID = Path(..., description="Agent ID"),
    current_user: User = Depends(get_current_user)
):
    """
    删除 PaperToX Agent

    只有超级管理员可以删除 Agent，执行逻辑删除
    """
    try:
        # 权限检查 - 只有超级管理员可以删除
        if current_user.role.identifier != InsetRole.SUPER_ADMIN:
            return send_data(False, None, "权限不足，只有超级管理员可以删除 Agent")

        # 获取现有 Agent
        agent = await PiPaperToXAgent.get_or_none(id=agent_id, is_deleted=False)
        if not agent:
            return send_data(False, None, "Agent 不存在")

        # 超级管理员可以删除所有Agent，无机构权限验证

        # 逻辑删除
        agent.is_deleted = True
        agent.deleted_at = datetime.now()
        agent.updated_by_id = current_user.id
        await agent.save()

        logger.info(f"用户 {current_user.username} 删除 PaperToX Agent 成功: {agent.name}")
        return send_data(True, None, "删除成功")

    except Exception as e:
        logger.error(f"删除 PaperToX Agent 失败: {str(e)}")
        return send_data(False, None, "删除失败")




