"""
PI文献管理路由
"""

from app.api.schemas.role import InsetRole
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, Query, Path
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
import uuid
from uuid import UUID
import os
import time
import aiofiles
import json
from pathlib import Path as PathLib
from datetime import datetime, date
from decimal import Decimal

from app.models.literature import (
    PiLiteratures,
    PiLiteraturesAssistant,
    FileParseStatus,
    LiteratureSource
)
from app.models.user import User
from app.models.upload_file import UploadFile as UploadFileModel
from app.api.deps import get_current_user
from app.utils.utils import send_data, send_page_data, ResponseModel, ResponsePageModel, PageQuery, PageInfo
from app.core.logging import get_logger
from app.api.repository.upload_file import save_upload_file
from app.api.repository.grobid_client import grobid_client
from app.services.literature_enhancement_service import (
    search_literature_by_doi_or_title
    # generate_tags_with_llm  # 暂时不使用AI标签生成
)
# AI服务导入 - 使用repository层
from app.api.repository.pi_literature_ai_service import (
    pi_literature_ai_analyze,
    pi_literature_ai_outline,
    pi_literature_ai_keynotes,
    pi_literature_ai_probe_questions,
    pi_literature_ai_probe_answer
)
from app.api.schemas.literature.pi_literature_schemas import (
    PiLiteraturesResponse,
    PiLiteraturesAssistantResponse,
    PiLiteraturesCreateRequest,
    PiLiteraturesAssistantCreateRequest,
    PiLiteraturesUpdateRequest,
    LiteratureUploadResponse,
    LiteratureListResponse,
    LiteratureDetailResponse,
    LiteratureDeleteResponse,
    LiteratureAIRequest,
    LiteratureProbeAnswerRequest,
    LiteratureStatusUpdateResponse,
    LiteratureStatsResponse,
    AuthorInfo
)

logger = get_logger(__name__)


async def read_parsed_content_from_file(file_path: str) -> str:
    """
    从文件中读取解析内容

    Args:
        file_path: 解析内容文件路径

    Returns:
        解析内容文本，如果文件不存在或读取失败则返回空字符串
    """
    if not file_path or not PathLib(file_path).exists():
        return ""

    try:
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            return content
    except Exception as e:
        logger.error(f"读取解析内容文件失败: {file_path}, 错误: {str(e)}")
        return ""


router = APIRouter(prefix="/pi-literature", tags=["PI文献管理"])

# 上传文件配置
ALLOWED_EXTENSIONS = ['.pdf']
MAX_FILE_SIZE = 20 * 1024 * 1024  # 100MB

async def parse_literature_async(literature_id: uuid.UUID, file_path: str):
    """异步解析文献内容"""
    try:
        logger.info(f"开始解析文献ID: {literature_id}, 文件: {file_path}")

        # 获取文献记录
        literature = await PiLiteratures.get_or_none(id=literature_id)
        if not literature:
            logger.error(f"文献记录不存在: {literature_id}")
            return

        # 更新状态为解析中
        literature.file_parse_status = FileParseStatus.PARSING
        await literature.save()

        # 使用GROBID解析PDF全文
        result = await grobid_client.parse_pdf_fulltext(file_path)

        if result:
            # 解析成功，更新文献信息
            literature.name = result.get('title', '未识别标题')[:500]
            literature.doi = result.get('doi')
            literature.abstract = result.get('abstract')
            literature.journal = result.get('journal')

            # 处理日期信息
            publication_date = result.get('publication_date')
            if publication_date:
                try:
                    # 直接保存日期字符串
                    if isinstance(publication_date, str):
                        literature.literature_date = publication_date.strip()
                        logger.info(f"文献日期解析成功: {literature.literature_date}")
                    else:
                        logger.warning(f"文献日期格式不正确，期望字符串类型，实际类型: {type(publication_date)}")
                        literature.literature_date = None
                except Exception as e:
                    logger.warning(f"文献日期解析失败: {publication_date}, 错误: {str(e)}")
                    literature.literature_date = None
            else:
                logger.info("文献日期为空，跳过解析")
                literature.literature_date = None

            # 处理作者信息
            authors_data = result.get('authors', [])
            literature.authors = [
                {
                    'name': author.get('name', ''),
                    'affiliation': author.get('affiliation', '')
                }
                for author in authors_data
            ]

            # 处理关键词
            literature.keywords = result.get('keywords', [])

            # 参考文献数量
            references = result.get('references', [])
            literature.reference_count = len(references)

            # 将解析结果存储到文件中，使用与上传文件相同的目录结构
            today = datetime.now().strftime("%Y%m%d")
            parsed_content_dir = PathLib(f"attachments/{today}")
            parsed_content_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成解析内容文件名，使用时间戳避免冲突
            timestamp = int(time.time() * 1000)
            parsed_content_filename = f"{timestamp}_{literature_id}.md"
            parsed_content_path = parsed_content_dir / parsed_content_filename
            
            # 提取正文内容并写入文件
            fulltext_content = result.get('fulltext', '')
            if fulltext_content:
                logger.info(f"====正文内容: {fulltext_content}")
                # 异步写入正文内容到文件
                async with aiofiles.open(parsed_content_path, 'w', encoding='utf-8') as f:
                    await f.write(fulltext_content)
            else:
                logger.info(f"====没有正文内容")
                # 如果没有正文内容，写入空文件
                async with aiofiles.open(parsed_content_path, 'w', encoding='utf-8') as f:
                    await f.write("")
            
            # 存储文件路径到数据库
            literature.parsed_content = str(parsed_content_path)

            # 更新状态为已完成
            literature.file_parse_status = FileParseStatus.COMPLETED
            literature.parse_error_message = None

            # Google学术搜索和标签处理
            try:
                logger.info(f"开始Google学术搜索: {literature_id}")

                # Google学术搜索 (使用DOI或标题)
                search_result = await search_literature_by_doi_or_title(
                    doi=literature.doi,
                    title=literature.name,
                    limit=1
                )

                if search_result.get('success'):
                    logger.info(f"Google学术搜索成功: {literature_id}")
                    search_data = search_result.get('data', {})
                    results = search_data.get('results', [])

                    # 如果找到结果，处理并打印详细信息
                    if results:
                        first_result = results[0]

                        # 打印所有搜索结果字段信息
                        logger.info(f"=== Google学术搜索结果详细信息 ===")
                        logger.info(f"标题: {first_result.get('title', 'N/A')}")
                        logger.info(f"年份: {first_result.get('year', 'N/A')}")
                        logger.info(f"作者: {first_result.get('authors', 'N/A')}")
                        logger.info(f"作者摘要: {first_result.get('authors_summary', 'N/A')}")
                        logger.info(f"发表信息: {first_result.get('publication', 'N/A')}")
                        logger.info(f"摘要片段: {first_result.get('snippet', 'N/A')}")
                        logger.info(f"被引用次数: {first_result.get('cited_by', 0)}")
                        logger.info(f"Google Scholar ID: {first_result.get('result_id', 'N/A')}")
                        logger.info(f"PDF链接: {first_result.get('pdf_links', [])}")
                        logger.info(f"版本数量: {first_result.get('versions_total', 0)}")
                        logger.info(f"URL: {first_result.get('url', 'N/A')}")

                        # 更新年份信息（如果原来没有的话）
                        year = first_result.get('year')
                        if year and not literature.literature_date:
                            try:
                                literature.literature_date = str(year)
                                logger.info(f"从Google学术更新年份: {year}")
                            except (ValueError, TypeError):
                                logger.warning(f"Google学术年份格式错误: {year}")

                        # 检查是否可以从搜索结果中提取其他信息作为关键词
                        # 注意：Google学术搜索结果通常不包含keywords，但可以从摘要片段中提取
                        snippet = first_result.get('snippet', '')
                        if snippet:
                            logger.info(f"摘要片段可用于关键词提取: {snippet[:100]}...")
                else:
                    logger.warning(f"Google学术搜索失败: {search_result.get('error')}")

                # 标签处理：使用文献本身包含的关键词作为标签
                # 注释：暂时不使用AI生成标签，直接使用文献解析出的关键词
                """
                # AI标签生成 (使用标题、摘要和正文) - 暂时注释
                tags_result = await generate_tags_with_llm(
                    title=literature.name,
                    abstract=literature.abstract,
                    content=fulltext_content[:2000] if fulltext_content else None,
                    current_user=current_user,
                    tag_count=5
                )
                """

                # 直接使用文献解析出的关键词作为标签
                if literature.keywords:
                    logger.info(f"使用文献解析出的关键词作为标签: {len(literature.keywords)} 个")
                    logger.info(f"关键词列表: {literature.keywords}")
                else:
                    logger.info(f"文献没有解析出关键词，标签为空")

            except Exception as enhancement_error:
                logger.error(f"文献增强处理失败: {literature_id}, 错误: {str(enhancement_error)}")
                # 不影响主流程，继续保存

            await literature.save()

            logger.info(f"文献解析成功: {literature_id}")

        else:
            # 解析失败
            literature.file_parse_status = FileParseStatus.ERROR
            literature.parse_error_message = "GROBID解析失败"
            await literature.save()

            logger.error(f"文献解析失败: {literature_id}")

    except Exception as e:
        # 解析出错
        logger.error(f"文献解析异常: {literature_id}, 错误: {str(e)}")

        try:
            literature = await PiLiteratures.get_or_none(id=literature_id)
            if literature:
                literature.file_parse_status = FileParseStatus.ERROR
                literature.parse_error_message = str(e)
                await literature.save()
        except Exception as save_error:
            logger.error(f"保存错误状态失败: {save_error}")


@router.post("/upload", response_model=ResponseModel[LiteratureUploadResponse])
async def upload_literature(
    file: UploadFile = File(..., description="PDF文件"),
    current_user: User = Depends(get_current_user)
):
    """
    上传文献PDF文件并异步解析

    - **file**: PDF文件
    - **article_type**: 文章类型
    - **literature_source**: 文献来源
    """
    logger.info(f"上传文献: {file.filename}")
    # 验证文件类型
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="仅支持PDF文件")

    # 验证文件大小
    if file.size > MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail=f"文件大小不能超过{MAX_FILE_SIZE // 1024 // 1024}MB")

    try:
        # 1. 保存上传文件并创建上传记录
        upload_record = await save_upload_file(file)
        
        # 获取绝对路径用于解析
        file_path = os.path.join(os.getcwd(), upload_record.file_path)

        # 2. 获取数据库User对象（current_user是UserResponse，需要获取真正的User实例）
        db_user = await User.get(id=current_user.id).prefetch_related('organization')

        # 3. 获取数据库中的UploadFile对象
        db_upload_file = await UploadFileModel.get(id=upload_record.id)

        # 4. 创建文献记录
        logger.info(f"创建文献记录，upload_file_id: {upload_record.id}, 类型: {type(upload_record.id)}")
        literature = await PiLiteratures.create(
            name=file.filename or "未命名文献",
            file_parse_status=FileParseStatus.PARSING,
            original_file_path=upload_record.file_path,
            upload_file=db_upload_file,  # 传递数据库对象
            user=db_user,  # 传递数据库User对象
            organization=db_user.organization if hasattr(db_user, 'organization') and db_user.organization else None
        )

        # 5. 创建文献AI助手记录（初始状态为空）
        logger.info(f"创建文献AI助手记录，literature_id: {literature.id}")
        ai_assistant = await PiLiteraturesAssistant.create(
            literature=literature,
            user=db_user,
            ai_summary=None,
            follow_up_questions=None,
            analysis_content=None,
            key_points=None,
            outline=None
        )

        # 6. 启动异步解析任务
        import asyncio
        asyncio.create_task(parse_literature_async(literature.id, file_path))

        return send_data(
            is_success=True,
            data={
                "literature_id": str(literature.id),
                "ai_assistant_id": str(ai_assistant.id),  # AI助手ID
                "filename": file.filename,
                "parse_status": literature.file_parse_status.value  # 确保返回字符串值
            }
        )

    except Exception as e:
        logger.error(f"文献上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")


@router.get("/list", response_model=ResponsePageModel[PiLiteraturesResponse], summary="分页获取文献列表")
async def get_literature_list(
    query: PageQuery = Depends(),
    search: Optional[str] = Query(None, description="搜索关键词"),
    parse_status: Optional[FileParseStatus] = Query(None, description="解析状态筛选"),
    literature_source: Optional[LiteratureSource] = Query(None, description="文献来源筛选"),
    user_id: Optional[UUID] = Query(None, description="创建人ID筛选"),
    date_from: Optional[date] = Query(None, description="文献发表开始日期"),
    date_to: Optional[date] = Query(None, description="文献发表结束日期"),
    current_user: User = Depends(get_current_user)
):
    """
    获取文献列表（分页查询）

    - **search**: 搜索关键词（标题、作者、摘要）
    - **parse_status**: 解析状态筛选
    - **literature_source**: 文献来源筛选
    - **user_id**: 创建人ID筛选
    - **date_from**: 文献发表开始日期
    - **date_to**: 文献发表结束日期
    """

    try:
        # 构建基础查询
        base_query = PiLiteratures.filter(is_deleted=False)

        # 权限控制 - 需要获取数据库User对象进行外键查询
        db_user = await User.get(id=current_user.id).prefetch_related('organization')

        if current_user.role.identifier == "SUPER_ADMIN":
            # 超级管理员可查看所有数据
            pass
        elif current_user.role.identifier == "ADMIN":
            # 管理员只能查看同机构数据
            if db_user.organization:
                base_query = base_query.filter(organization=db_user.organization)
            else:
                # 无机构的管理员只能看自己的
                base_query = base_query.filter(user=db_user)
        else:
            # 普通用户只能看自己的数据
            base_query = base_query.filter(user=db_user)

        # 搜索条件
        if search:
            from tortoise.expressions import Q
            search_queries = Q()
            for field in ["name", "authors", "abstract", "journal"]:
                search_queries |= Q(**{f"{field}__icontains": search})
            base_query = base_query.filter(search_queries)

        # 其他筛选条件
        if parse_status:
            base_query = base_query.filter(file_parse_status=parse_status)
        if literature_source:
            base_query = base_query.filter(literature_source=literature_source)
        if user_id:
            base_query = base_query.filter(user_id=user_id)
        # 注意：由于literature_date现在是字符串类型，日期范围过滤需要特殊处理
        # 这里简化处理，只做字符串匹配，如需精确日期比较需要额外的日期解析逻辑
        if date_from:
            # 简单的字符串比较，假设日期格式为YYYY或YYYY-MM-DD
            base_query = base_query.filter(literature_date__contains=str(date_from.year))
        if date_to:
            base_query = base_query.filter(literature_date__contains=str(date_to.year))

        # 获取总数
        total = await base_query.count()

        # 获取分页数据
        literatures = await base_query.order_by("-created_time").offset((query.page-1)*query.size).limit(query.size).all()

        # 转换响应格式
        items = []
        for lit in literatures:
            # 获取创建人信息(通过外键关联)
            await lit.fetch_related('user')
            creator = lit.user

            # 构建作者信息列表
            authors = []
            if lit.authors:
                for author in lit.authors[:5]:  # ·只显示前5个作者
                    if isinstance(author, dict):
                        authors.append(AuthorInfo(
                            name=author.get('name', ''),
                            affiliation=author.get('affiliation'),
                            email=author.get('email')
                        ))
                    else:
                        authors.append(AuthorInfo(name=str(author)))

            items.append(PiLiteraturesResponse(
                id=lit.id,  # 直接使用 UUID
                name=lit.name,
                doi=lit.doi,
                authors=authors,
                keywords=lit.keywords[:5] if lit.keywords else [],  # 只显示前5个关键词
                abstract=lit.abstract[:200] + "..." if lit.abstract and len(lit.abstract) > 200 else lit.abstract,
                journal=lit.journal,
                reference_count=lit.reference_count,
                literature_date=lit.literature_date,
                article_type="research_paper",  # Default article type since model doesn't have this field
                literature_source=lit.literature_source.value if hasattr(lit.literature_source, 'value') else str(lit.literature_source),
                impact_factor=lit.impact_factor,
                file_parse_status=lit.file_parse_status.value if hasattr(lit.file_parse_status, 'value') else str(lit.file_parse_status),
                parse_error_message=lit.parse_error_message,
                original_file_path=lit.original_file_path,
                parsed_content=lit.parsed_content,
                upload_file_id=lit.upload_file_id if lit.upload_file else None,
                user_id=lit.user_id if lit.user else None,  # 使用 UUID 或 None
                organization_id=lit.organization_id,
                is_deleted=lit.is_deleted,
                deleted_time=lit.deleted_time,
                created_time=lit.created_time,
                updated_time=lit.updated_time,
                view_count=lit.view_count,
                download_count=lit.download_count,
                ai_analysis_count=lit.ai_analysis_count
            ))

        return send_page_data(True, PageInfo(
            items=items,
            total=total,
            page=query.page,
            size=query.size
        ))

    except Exception as e:
        logger.error(f"获取文献列表失败: {str(e)}")
        return send_page_data(False, None, f"获取文献列表失败: {str(e)}")


@router.get("/{literature_id}", response_model=ResponseModel[LiteratureDetailResponse])
async def get_literature_detail(
    literature_id: uuid.UUID = Path(..., description="文献ID"),
    current_user: User = Depends(get_current_user)
):
    """获取文献详细信息"""

    try:
        # 构建查询
        query = PiLiteratures.filter(id=literature_id, is_deleted=False)

        # 权限控制 - 需要获取数据库User对象进行外键查询
        db_user = await User.get(id=current_user.id).prefetch_related('organization')

        if current_user.role.identifier == "SUPER_ADMIN":
            pass
        elif current_user.role.identifier == "ADMIN":
            if db_user.organization:
                query = query.filter(organization=db_user.organization)
            else:
                query = query.filter(user=db_user)
        else:
            query = query.filter(user=db_user)

        literature = await query.first()
        if not literature:
            raise HTTPException(status_code=404, detail="文献不存在或无权访问")

        await literature.save()

        # 获取创建人信息(通过外键关联)
        await literature.fetch_related('user', 'organization')
        creator = literature.user

        # 获取AI助手信息
        ai_assistant = await PiLiteraturesAssistant.get_or_none(literature_id=literature.id)

        # 读取解析内容
        parsed_content = await read_parsed_content_from_file(literature.parsed_content)

        # 获取关联的文件信息
        file_info = None
        if literature.upload_file:
            try:
                # 通过外键关联获取文件信息，需要先fetch_related
                await literature.fetch_related('upload_file')
                upload_file = literature.upload_file
                if upload_file and not upload_file.is_deleted:
                    file_info = {
                        "id": str(upload_file.id),
                        "file_name": upload_file.file_name,
                        "file_path": upload_file.file_path,
                        "file_size": upload_file.file_size,
                        "created_time": upload_file.created_time.isoformat()
                    }
            except Exception as e:
                logger.warning(f"获取文件信息失败: {str(e)}")

        # 构建AI助手响应数据（符合PiLiteraturesAssistantResponse schema）
        ai_assistant_data = None
        if ai_assistant:
            # 导入清洗函数
            from app.api.repository.pi_literature_ai_service import clean_outline_format

            # 生成cleaned_outline和combined字段
            cleaned_outline = clean_outline_format(ai_assistant.outline) if ai_assistant.outline else None
            combined = f"{ai_assistant.ai_summary or ''} {ai_assistant.outline or ''}".strip() if (ai_assistant.ai_summary or ai_assistant.outline) else None

            ai_assistant_data = {
                "id": ai_assistant.id,  # 直接使用 UUID
                "literature_id": literature.id,  # 直接使用 UUID
                "ai_summary": ai_assistant.ai_summary,
                "follow_up_questions": ai_assistant.follow_up_questions,
                "analysis_content": ai_assistant.analysis_content,
                "key_points": ai_assistant.key_points,
                "outline": ai_assistant.outline,
                "cleaned_outline": cleaned_outline,
                "combined": combined,
                "creator_id": creator.id if creator else None,  # 修复：使用creator_id而不是user_id
                "created_time": ai_assistant.created_time.isoformat(),
                "updated_time": ai_assistant.updated_time.isoformat()
            }

        # 构建文献响应数据（符合PiLiteraturesResponse schema）
        literature_data = {
            "id": literature.id,  # 直接使用 UUID
            "name": literature.name,
            "doi": literature.doi,
            "authors": literature.authors,
            "keywords": literature.keywords,
            "abstract": literature.abstract,
            "journal": literature.journal,
            "reference_count": literature.reference_count,
            "literature_date": literature.literature_date,  # Now string type
            "article_type": "research_paper",  # Default article type since model doesn't have this field
            "literature_source": literature.literature_source.value if hasattr(literature.literature_source, 'value') else str(literature.literature_source),
            "impact_factor": literature.impact_factor,  # Keep as Decimal for schema
            "file_parse_status": literature.file_parse_status.value if hasattr(literature.file_parse_status, 'value') else str(literature.file_parse_status),
            "parse_error_message": literature.parse_error_message,
            "original_file_path": literature.original_file_path,
            "parsed_content": literature.parsed_content,  # 返回文件路径
            "parsed_content_text": parsed_content,  # 返回文件内容
            "upload_file_id": literature.upload_file_id,
            "user_id": creator.id if creator else None,  # 直接使用 UUID
            "organization_id": literature.organization.id if literature.organization else None,  # 直接使用 UUID
            "is_deleted": literature.is_deleted,
            "created_time": literature.created_time,  # Keep as datetime for schema
            "updated_time": literature.updated_time,  # Keep as datetime for schema
            "view_count": literature.view_count,
            "download_count": literature.download_count,
            "ai_analysis_count": literature.ai_analysis_count
        }

        # 构建符合LiteratureDetailResponse schema的响应
        detail_response = {
            "literature": literature_data,
            "ai_assistant": ai_assistant_data
        }

        return send_data(
            is_success=True,
            data=detail_response
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文献详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取详情失败")


@router.delete("/{literature_id}", response_model=ResponseModel[LiteratureDeleteResponse])
async def delete_literature(
    literature_id: uuid.UUID = Path(..., description="文献ID"),
    current_user: User = Depends(get_current_user)
):
    """删除文献（软删除）"""

    try:
        # 构建查询
        query = PiLiteratures.filter(id=literature_id, is_deleted=False)

        # 权限控制 - 只有创建者或管理员可以删除
        # 需要获取数据库User对象进行外键查询
        db_user = await User.get(id=current_user.id).prefetch_related('organization')

        if current_user.role.identifier == "SUPER_ADMIN":
            pass
        elif current_user.role.identifier == "ADMIN":
            if db_user.organization:
                query = query.filter(organization=db_user.organization)
            else:
                query = query.filter(user=db_user)
        else:
            query = query.filter(user=db_user)

        literature = await query.first()
        if not literature:
            raise HTTPException(status_code=404, detail="文献不存在或无权删除")

        # 软删除
        literature.is_deleted = True
        literature.deleted_time = datetime.now()
        # 注意：如果需要modifier字段，需要先在模型中添加
        await literature.save()

        # 同时软删除关联的AI助手记录
        await PiLiteraturesAssistant.filter(literature_id=literature.id).delete()

        # 同时软删除关联的文件记录
        if literature.upload_file:
            try:
                # 先fetch_related获取文件信息
                await literature.fetch_related('upload_file')
                upload_file = literature.upload_file
                if upload_file:
                    upload_file.is_deleted = True
                    upload_file.deleted_time = datetime.now()
                    await upload_file.save()
                    logger.info(f"关联文件已软删除: {upload_file.id}")
            except Exception as e:
                logger.warning(f"删除关联文件记录失败: {str(e)}")

        logger.info(f"文献已删除: {literature_id}, 操作人: {current_user.id}")

        return send_data(
            is_success=True,
            data={
                "message": "文献删除成功",
                "deleted_id": str(literature_id)
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文献失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除失败")


# ========== AI相关接口 ==========

@router.post("/ai/analyze", response_model=ResponseModel[dict], summary="文献AI分析接口")
async def analyze_literature_content(
    request: LiteratureAIRequest,
    current_user: User = Depends(get_current_user)
):
    """
    文献AI分析接口 - 基于文献内容生成专业研究分析报告

    该接口会根据文献的标题、摘要和正文内容，生成一份结构化的专业分析报告，包含：
    1. 综合评估概览：多维度评估（3-5个维度）
    2. 详细分析：每个维度的深入分析和关键点提炼

    入参:
    - literature_id: 需要分析的文献ID

    出参:
    - analysis: 生成的分析报告内容（包含评估概览和详细分析）
    """
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求文献AI分析，文献ID: {request.literature_id}")

        # 使用repository层的通用方法
        return await pi_literature_ai_analyze(
            literature_id=request.literature_id,
            current_user=current_user
        )

    except Exception as e:
        logger.error(f"文献AI分析失败: {str(e)}")
        return send_data(False, None, f"AI分析失败: {str(e)}")


@router.post("/ai/outline", response_model=ResponseModel[dict], summary="文献AI大纲接口")
async def generate_literature_outline(
    request: LiteratureAIRequest,
    current_user: User = Depends(get_current_user)
):
    """
    文献AI大纲接口 - 生成文献的结构化大纲

    入参:
    - literature_id: 需要生成大纲的文献ID

    出参:
    - outline: 生成的大纲内容
    - cleaned_outline: 清洗后的结构化大纲
    """
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求文献AI大纲生成，文献ID: {request.literature_id}")

        # 使用repository层的通用方法
        return await pi_literature_ai_outline(
            literature_id=request.literature_id,
            current_user=current_user
        )

    except Exception as e:
        logger.error(f"文献AI大纲生成失败: {str(e)}")
        return send_data(False, None, f"AI大纲生成失败: {str(e)}")


@router.post("/ai/keynotes", response_model=ResponseModel[dict], summary="文献AI重点接口")
async def generate_literature_keynotes(
    request: LiteratureAIRequest,
    current_user: User = Depends(get_current_user)
):
    """
    文献AI重点接口 - 提取文献的关键要点

    入参:
    - literature_id: 需要生成重点的文献ID

    出参:
    - keynotes: 生成的结构化重点内容
    """
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求文献AI重点生成，文献ID: {request.literature_id}")

        # 使用repository层的通用方法
        return await pi_literature_ai_keynotes(
            literature_id=request.literature_id,
            current_user=current_user
        )

    except Exception as e:
        logger.error(f"文献AI重点生成失败: {str(e)}")
        return send_data(False, None, f"AI重点生成失败: {str(e)}")


@router.post("/ai/probe_questions", response_model=ResponseModel[dict], summary="文献AI追问_问题列表接口")
async def generate_literature_probe_questions(
    request: LiteratureAIRequest,
    current_user: User = Depends(get_current_user)
):
    """
    文献AI追问接口 - 生成针对文献的深度问题

    入参:
    - literature_id: 需要生成追问的文献ID

    出参:
    - probes: 生成的追问问题列表
    """
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求文献AI追问问题生成，文献ID: {request.literature_id}")

        # 使用repository层的通用方法
        return await pi_literature_ai_probe_questions(
            literature_id=request.literature_id,
            current_user=current_user
        )

    except Exception as e:
        logger.error(f"文献AI追问生成失败: {str(e)}")
        return send_data(False, None, f"AI追问生成失败: {str(e)}")


@router.post("/ai/probe_answer", response_model=ResponseModel[dict], summary="文献AI追问_问题回答接口")
async def generate_literature_probe_answer(
    request: LiteratureProbeAnswerRequest,
    current_user: User = Depends(get_current_user)
):
    """
    文献AI追问回答接口 - 回答针对文献的具体问题

    入参:
    - literature_id: 需要回答的文献ID
    - question: 需要回答的问题

    出参:
    - answer: 问题的回答内容
    """
    try:
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求文献AI追问回答，文献ID: {request.literature_id}, 问题: {request.question[:50]}...")

        # 使用repository层的通用方法
        return await pi_literature_ai_probe_answer(
            literature_id=request.literature_id,
            current_user=current_user,
            question=request.question
        )

    except Exception as e:
        logger.error(f"文献AI追问回答失败: {str(e)}")
        return send_data(False, None, f"AI追问回答失败: {str(e)}")


# ========== 辅助函数 ==========

async def get_literature_with_check(literature_id: UUID, current_user: User) -> Optional[PiLiteratures]:
    """获取文献并检查权限"""
    literature = await PiLiteratures.get_or_none(id=literature_id, is_deleted=False)
    logger.info(f"===文献 {literature_id} 存在: {literature}")
    if not literature:
        return None

    # 权限检查
    if current_user.role.identifier == InsetRole.SUPER_ADMIN:
        return literature
    elif current_user.role.identifier == InsetRole.ADMIN:
        if current_user.organization and current_user.organization.id:
            if literature.organization_id == current_user.organization.id:
                return literature
        return None
    else:
        if literature.user_id == current_user.id:
            return literature
        return None


async def read_literature_content(literature: PiLiteratures) -> Optional[str]:
    """读取文献内容"""
    if not literature.parsed_content:
        return None

    try:
        async with aiofiles.open(literature.parsed_content, 'r', encoding='utf-8') as f:
            content = await f.read()

        # 组合标题、摘要和正文内容
        content_parts = []
        if literature.name:
            content_parts.append(f"标题: {literature.name}")
        if literature.abstract:
            content_parts.append(f"摘要: {literature.abstract}")
        if content:
            content_parts.append(f"正文: {content}")

        return "\n\n".join(content_parts)

    except Exception as e:
        logger.error(f"读取文献内容失败: {str(e)}")
        return None


