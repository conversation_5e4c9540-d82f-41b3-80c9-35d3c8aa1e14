from fastapi import APIRouter, Request, HTTPException, Query, Depends
from uuid import UUID
from typing import List, Optional
from datetime import datetime
from tortoise.expressions import Q

from app.models.think_tank_meta import ThinkTankMeta
from app.api.schemas.think_tank_meta import (
    ThinkTankMetaCreate, 
    ThinkTankMetaResponse, 
)
from app.utils.utils import (
    send_data,
    ResponseModel
)
from app.api.deps import get_current_user_from_state
from app.core.logging import get_logger
from typing import List

logger = get_logger(__name__)
router = APIRouter()

@router.post("/create", response_model=ResponseModel[ThinkTankMetaResponse], summary="创建智库报告")
async def create_think_tank_meta(
    data: ThinkTankMetaCreate,
    request: Request
):
    """
    创建智库报告记录。
    
    用于配置智库报告的目标受众和分析方法，为后续的智库报告生成提供元数据支持。
    
    Args:
        data: 智库报告创建数据，包含目标受众和分析方法
        request: FastAPI请求对象，用于获取当前用户信息
        
    Returns:
        ResponseModel[ThinkTankMetaResponse]: 包含创建结果的响应数据
        
    Raises:
        HTTPException: 当创建失败时抛出异常
    """
    try:
        current_user = get_current_user_from_state(request=request)
        
        # 验证输入数据
        # if not data.target or not data.analysis_method:
        #     logger.warning(f"用户 {current_user.username} 创建智库报告时缺少必要参数")
        #     return send_data(False, None, "目标受众和分析方法不能为空")
        
        # 创建智库报告记录
        result = await ThinkTankMeta.create(
            target=data.target,
            analysis_method=data.analysis_method
        )
        
        logger.info(f"用户 {current_user.username} 创建智库报告成功，ID: {result.id}")
        return send_data(True, ThinkTankMetaResponse.model_validate(result, from_attributes=True))
        
    except ValueError as e:
        logger.error(f"用户 {current_user.username} 创建智库报告时数据验证失败: {str(e)}")
        return send_data(False, None, f"数据验证失败: {str(e)}")
    except Exception as e:
        logger.error(f"用户 {current_user.username} 创建智库报告失败: {str(e)}")
        return send_data(False, None, "创建智库报告失败，请稍后重试")
