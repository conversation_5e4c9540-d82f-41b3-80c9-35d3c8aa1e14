import os
import tempfile
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Request, HTTPException, status, File, UploadFile, Form, Depends, Query
from fastapi.responses import FileResponse
from app.api.deps import get_current_user_from_state
from app.core.logging import get_logger
from app.utils.utils import send_data, ResponseModel
from app.services.paper2code_service import paper2code_service
from app.api.schemas.paper2code import (
    Paper2CodeJobCreate,
    Paper2CodeJobResponse,
    Paper2CodeJobList,
    Paper2CodeJobDetail,
    Paper2CodeUploadResponse,
    Paper2CodeDownloadInfo
)

router = APIRouter()
logger = get_logger(__name__)

@router.post("/upload", response_model=ResponseModel[Paper2CodeUploadResponse], summary="上传文件并创建Paper2Code任务")
async def upload_file(
    request: Request,
    file: UploadFile = File(..., description="上传的PDF或DOCX文件"),
    job_name: str = Form(..., description="任务名称"),
    description: Optional[str] = Form(None, description="任务描述")
):
    """
    上传文件并创建Paper2Code任务

    支持的文件格式:
    - PDF (.pdf)
    - Word文档 (.docx)
    """
    try:
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 上传Paper2Code文件: {file.filename}")

        # 创建任务
        job = await paper2code_service.create_job(
            file=file,
            job_name=job_name,
            description=description,
            current_user=current_user
        )

        response_data = Paper2CodeUploadResponse(
            success=True,
            job_id=job.id,
            message="文件上传成功，开始处理",
            file_name=job.original_file_name,
            file_size=job.original_file_size
        )

        return send_data(True, response_data, "上传成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文件失败: {str(e)}")
        return send_data(False, None, f"上传失败: {str(e)}")


@router.get("/jobs", response_model=ResponseModel[List[Paper2CodeJobList]], summary="获取任务列表")
async def get_jobs(
    request: Request,
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """获取当前用户的Paper2Code任务列表"""
    try:
        current_user = get_current_user_from_state(request)

        jobs = await paper2code_service.get_user_jobs(
            current_user=current_user,
            limit=limit,
            offset=offset
        )

        job_list = [
            Paper2CodeJobList(
                id=job.id,
                job_name=job.job_name,
                status=job.status,
                original_file_name=job.original_file_name,
                file_type=job.file_type,
                progress_percentage=job.progress_percentage,
                current_stage=job.current_stage,
                created_at=job.created_at,
                updated_at=job.updated_at
            )
            for job in jobs
        ]

        return send_data(True, job_list, "获取成功")

    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        return send_data(False, None, f"获取失败: {str(e)}")


@router.get("/jobs/{job_id}", response_model=ResponseModel[Paper2CodeJobResponse], summary="获取任务详情")
async def get_job_detail(
    request: Request,
    job_id: UUID
):
    """获取指定任务的详细信息"""
    try:
        current_user = get_current_user_from_state(request)

        job = await paper2code_service.get_job_by_id(job_id, current_user)

        job_response = Paper2CodeJobResponse(
            id=job.id,
            job_name=job.job_name,
            description=job.description,
            status=job.status,
            original_file_name=job.original_file_name,
            file_type=job.file_type,
            original_file_size=job.original_file_size,
            progress_percentage=job.progress_percentage,
            current_stage=job.current_stage,
            zip_file_path=job.zip_file_path,
            total_files_generated=job.total_files_generated,
            processing_time_seconds=job.processing_time_seconds,
            error_message=job.error_message,
            created_at=job.created_at,
            updated_at=job.updated_at,
            started_at=job.started_at,
            completed_at=job.completed_at
        )

        return send_data(True, job_response, "获取成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        return send_data(False, None, f"获取失败: {str(e)}")


@router.delete("/jobs/{job_id}", response_model=ResponseModel[bool], summary="删除任务")
async def delete_job(
    request: Request,
    job_id: UUID
):
    """删除指定的Paper2Code任务"""
    try:
        current_user = get_current_user_from_state(request)

        success = await paper2code_service.delete_job(job_id, current_user)

        return send_data(True, success, "删除成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        return send_data(False, None, f"删除失败: {str(e)}")


@router.get("/jobs/{job_id}/download", summary="下载任务结果")
async def download_result(
    request: Request,
    job_id: UUID
):
    """下载Paper2Code任务的结果ZIP文件"""
    try:
        current_user = get_current_user_from_state(request)

        download_info = await paper2code_service.get_download_info(job_id, current_user)

        return FileResponse(
            path=download_info["file_path"],
            filename=download_info["file_name"],
            media_type=download_info["mime_type"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")


@router.get("/jobs/{job_id}/status", response_model=ResponseModel[dict], summary="获取任务状态")
async def get_job_status(
    request: Request,
    job_id: UUID
):
    """获取任务的实时状态信息"""
    try:
        current_user = get_current_user_from_state(request)

        job = await paper2code_service.get_job_by_id(job_id, current_user)

        status_info = {
            "id": job.id,
            "status": job.status,
            "progress_percentage": job.progress_percentage,
            "current_stage": job.current_stage,
            "error_message": job.error_message,
            "updated_at": job.updated_at
        }

        return send_data(True, status_info, "获取成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取状态失败: {str(e)}")
        return send_data(False, None, f"获取失败: {str(e)}")
