from fastapi import APIRouter
from typing import Optional
from fastapi import APIRouter, Request
from app.api.deps import get_current_user_from_state
from app.api.schemas.project_configs import ProjectConfigResponse2, ProjectConfigStatus, GenerateConfig
from app.utils.utils import (
    send_data,
    ResponseModel,
    ContentStatus
)
from app.models.user import User
from app.api.repository.user_report_usage import check_user_usage_limit
from app.core.logging import get_logger
from app.services.memory_storage import (
    outline_content_manager,
    report_content_manager
)
from app.api.repository.report import (
    generate_step_one,
    generate_step_two,
    stream_step_one,
    stream_step_two,
    get_project_data,
    generate_meeting_step_two
)
from app.api.repository.user import is_user_authed
from app.models.project_configs import ProjectConfig
from app.utils.enum import DOC_TYPE

# 获取logger实例
logger = get_logger(__name__)

router = APIRouter()

# 生成项目大纲接口
@router.post("/{project_id}/generate-outline", response_model=ResponseModel[ProjectConfigResponse2])
async def generate_project_outline(
    project_id: str,
    request: Request
):
    try:
        # 检查用户报告使用次数限制
        current_user = get_current_user_from_state(request)
        logger.info(f"生成大纲步骤开始")
        result = await generate_step_one(
            project_id=project_id,
            current_user=current_user
        )
        return send_data(True, result)
    except Exception as e:
        logger.error(f"生成的大纲步骤报错：{str(e)}")
        return send_data(False, None, str(e))
# 生成项目报告接口
@router.post("/{project_id}/generate-report", response_model=ResponseModel[ProjectConfigResponse2])
async def generate_project_report(
    project_id: str,
    request: Request
):
    try:
        current_user = get_current_user_from_state(request)
        result = await generate_step_two(
            project_id=project_id,
            current_user=current_user
        )
        return send_data(True, result)
    except Exception as e:
        logger.error(f"生成的报告步骤报错：{str(e)}")
        return send_data(False, None, str(e))
# 流式返回大纲内容的接口（SSE）
@router.get("/{project_id}/stream-outline")
async def stream_project_outline(
    project_id: str,
    request: Request
):
    """
    流式返回项目大纲内容（SSE格式）
    """
    # 检查项目是否存在并验证权限
    current_user = get_current_user_from_state(request)
    result = await stream_step_one(
        project_id=project_id,
        current_user=current_user
    )
    return result

# 流式返回报告内容的接口（SSE）
@router.get("/{project_id}/stream-report")
async def stream_project_report(
    project_id: str,
    request: Request
):
    """
    流式返回项目报告内容（SSE格式）
    """
    result = await stream_step_two(
        project_id=project_id,
        current_user=get_current_user_from_state(request)
    )
    return result

@router.post("/{project_id}/stop-outline", response_model=ResponseModel[bool])
async def stop_outline_generation(
    project_id: str, 
    request: Request
):
    """终止项目大纲生成"""
    try:
        # 检查项目是否存在并验证权限
        current_user = get_current_user_from_state(request)
        config_db = await get_project_data(project_id=project_id, current_user=current_user)
        # 检查当前状态
        if config_db.status != ProjectConfigStatus.OUTLINE_GENERATING.value:
            return send_data(False, False, "当前没有正在生成大纲")
        # 获取该项目的内容对象
        project_content = outline_content_manager.get_project_content(project_id)
        
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                error = f"已取消大纲生成任务"
                print(f"已取消项目 {project_id} 的大纲生成任务")
                outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
            except Exception as e:
                error = f"取消大纲生成任务时出错: {str(e)}"
                outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                print(error)
    
        
        # 更新项目状态为已取消
        config_db.status = ProjectConfigStatus.OUTLINE_CANCELED.value
        config_db.ai_generated_outline = None
        await config_db.save()
        
        return send_data(True, True)
    except Exception as e:
        return send_data(False, False, f"终止大纲生成失败: {str(e)}")

@router.post("/{project_id}/stop-report", response_model=ResponseModel[bool])
async def stop_report_generation(
    project_id: str,
    request: Request
):
    """终止项目报告生成"""
    try:
        # 检查项目是否存在并验证权限
        current_user = get_current_user_from_state(request)
        config_db = await get_project_data(
            current_user=current_user,
            project_id=project_id
        )
        # 检查当前状态
        if config_db.status != ProjectConfigStatus.REPORT_GENERATING.value:
            raise Exception("当前没有正在生成报告")
        # 获取该项目的内容对象
        project_content = report_content_manager.get_project_content(project_id)
        
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                error = f"已取消报告生成任务"
                report_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                print(f"已取消项目 {project_id} 的报告生成任务")
            except Exception as e:
                error = f"取消报告生成任务时出错: {str(e)}"
                report_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                # 记录错误信息
                print(f"取消报告生成任务时出错: {str(e)}")
        
        # 更新项目状态为已取消
        config_db.status = ProjectConfigStatus.REPORT_CANCELED.value
        config_db.report_generation_time = None
        config_db.ai_generated_report = None
        await config_db.save()
        
        return send_data(True, True)
    except Exception as e:
        return send_data(False, False, f"终止报告生成失败: {str(e)}")
@router.post("/{project_id}/generate-report-step")
async def generate_project_report_step(
    project_id: str,
    request: Request
):
    try:
        current_user = get_current_user_from_state(request)

        # 检查用户报告使用次数限制
        user_obj = await User.filter(id=current_user.id, is_deleted=False).prefetch_related('role', 'organization').first()
        if not user_obj:
            return send_data(False, None, "用户不存在")
        try:
            await check_user_usage_limit(user_obj.id)
        except Exception as e:
            return send_data(False, None, str(e))
         # 直接从数据库获取ProjectConfig模型
        config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user", "model").first()
        is_generating = config_db.status == ProjectConfigStatus.REPORT_GENERATING.value
        if is_generating:
            return send_data(False, None, "材料已经处于生成中，不能再次生成")
        
        if not config_db:
            return send_data(False, None, "项目配置不存在")
        # logger.error(config_db, 'config_db')
        if not await is_user_authed(config_db.user.id, current_user.id):
            return send_data(False, None, "无权访问此项目")
        if config_db.model is None:
            return send_data(False, None, "请先配置模型")
        result = None
        if config_db.doc_type == DOC_TYPE.MEETING_GEN.value:
            logger.info("会议纪要生成开始")
            result = await generate_meeting_step_two(project_id, current_user)
        else:
            logger.info("其他模块生成开始")
            result = await generate_step_two(project_id, current_user)
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, str(e))
