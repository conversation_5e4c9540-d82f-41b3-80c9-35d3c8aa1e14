from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.responses import FileResponse
import os
from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.get("/templates-files/{filename:path}")
async def serve_common_oss_file(
    filename: str,
    current_user: UserResponse = Depends(get_current_user)
):
    """
    提供公共文件下载服务
    
    Args:
        filename: 文件名
        current_user: 当前用户
        
    Returns:
        FileResponse: 文件下载响应
    """
    # 安全检查：防止路径遍历攻击
    if ".." in filename or filename.startswith("/"):
        logger.warning(f"用户 {current_user.username} 尝试访问非法路径: {filename}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="非法的文件路径"
        )
    
    relative_path = f"templates/{filename}"
    
    try:
        # 构建绝对路径
        absolute_path = os.path.join(os.getcwd(), relative_path)
        
        # 检查文件是否存在
        if not os.path.exists(absolute_path):
            logger.error(f"文件不存在: {absolute_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )
        
        # 记录下载日志
        logger.info(f"用户 {current_user.username} 下载文件: {filename}")
        
        # 返回文件响应
        return FileResponse(
            path=absolute_path,
            filename=filename
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文件失败: {str(e)}"
        )
  

