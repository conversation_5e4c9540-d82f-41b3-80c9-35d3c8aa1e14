from fastapi import APIRouter, Request, HTTPException, Query, Depends
from uuid import UUID
from typing import List, Optional
from datetime import datetime
from tortoise.expressions import Q

from app.models.feasibility_meta import FeasibilityMeta
from app.api.schemas.feasibility_meta import (
    FeasibilityMetaCreate, 
    FeasibilityMetaResponse, 
)
from app.utils.utils import (
    send_data,
    ResponseModel
)
from app.api.deps import get_current_user_from_state
from app.core.logging import get_logger
from typing import List

logger = get_logger(__name__)
router = APIRouter()

@router.post("/create", response_model=ResponseModel[FeasibilityMetaResponse], summary="创建可行性报告配置")
async def create_feasibility_meta(
    data: FeasibilityMetaCreate,
    request: Request
):
    """
    创建可行性报告配置记录。
    
    用于配置可行性报告的预期收益率、投资回收期、分析方法、投资金额和风险评估等参数，
    为后续的可行性报告生成提供元数据支持。
    
    Args:
        data: 可行性报告创建数据，包含各项配置参数
        request: FastAPI请求对象，用于获取当前用户信息
        
    Returns:
        ResponseModel[FeasibilityMetaResponse]: 包含创建结果的响应数据
        
    Raises:
        HTTPException: 当创建失败时抛出异常
    """
    try:
        current_user = get_current_user_from_state(request=request)
        
    
        # 创建可行性报告配置记录
        result = await FeasibilityMeta.create(
            expect_return_rate=data.expect_return_rate,
            payback_period=data.payback_period,
            analysis_method=data.analysis_method,
            investment_amount=data.investment_amount,
            risk_assessment=data.risk_assessment
        )
        
        logger.info(f"用户 {current_user.username} 创建可行性报告配置成功，ID: {result.id}")
        return send_data(True, FeasibilityMetaResponse.model_validate(result, from_attributes=True))
        
    except ValueError as e:
        logger.error(f"用户 {current_user.username} 创建可行性报告配置时数据验证失败: {str(e)}")
        return send_data(False, None, f"数据验证失败: {str(e)}")
    except Exception as e:
        logger.error(f"用户 {current_user.username} 创建可行性报告配置失败: {str(e)}")
        return send_data(False, None, "创建可行性报告配置失败，请稍后重试") 