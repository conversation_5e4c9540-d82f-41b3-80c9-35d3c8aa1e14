from app.api.repository.ai_service import clean_outline_format
from app.api.repository.user_default_model import get_user_model
from app.api.schemas.user import UserResponse
from app.models.insight.knowledge_canvas_tag import KnowledgeCanvasTag
from app.models.organization_model_use import UseCase
from fastapi import APIRouter, Depends, Query, HTTPException, status
from typing import List, Optional, Dict, Any
from tortoise.expressions import Q, RawSQL
from datetime import datetime
from uuid import UUID, uuid4
import json
from pydantic import BaseModel
import asyncio
import ast

from app.api.schemas.insight.knowledge_canvas import (
    AIQuestion,
    CanvasSourceType,
    CanvasType,
    KnowledgeCanvasResponse,
    KnowledgeCanvasListResponse,
    KnowledgeCanvasQueryParams,
    KnowledgeCanvasCreate,
    KnowledgeCanvasUpdate,
    RelatedNote,
    SaveInsightRequest,
    SaveInsightBatchRequest,
    KeyNote
)
from app.api.schemas.insight.inspiration import InspirationSource
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.utils.utils import send_data, ResponseModel, send_page_data, ResponsePageModel, PageInfo
from app.core.logging import get_logger
from app.api.deps import get_current_user, oauth2_scheme
from app.services.insight.knowledge_canvas_service import download_and_save_image, generate_tags, process_canvas_summary
from app.models.model_config import ModelConfig
from app.models.user import User

router = APIRouter()
logger = get_logger(__name__)

@router.get(
    "/list",
    response_model=ResponsePageModel[KnowledgeCanvasListResponse],
    summary="获取灵感卡片列表",
    description="""
    获取当前用户的灵感卡片列表，支持关键词、类型等多条件筛选。
    
    关键词搜索支持以下字段的模糊匹配：
    - 名称(name)、原始文章(original_article)、原始文章截取(original_article_truncated)
    - 简介(summary)、关联笔记(related_notes)、用户注释(user_notes)
    - 作者(author)、引用(reference)、来源(source)、灵感来源(inspiration_source)
    - 详细描述(detailed_description)、AI提问(ai_questions)、AI扩写(ai_expanded)
    - AI分析(ai_analysis)、AI重点(ai_keynotes)、AI结构大纲(ai_outline)、AI追问(ai_probe)
    - 标签名称(tags.name)
    
    返回字段说明：
    - items: 灵感卡片列表
        - id: 灵感卡片唯一标识符
        - name: 灵感卡片名称
        - source_type: 来源类型（笔记、知识、文献）
        - type: 卡片类型（文献、知识、短文、公式）
        - summary: 灵感卡片概要
        - key_notes: 重点注释列表
        - related_notes: 关联笔记列表，包含笔记ID和标题
        - image_url: 灵感卡片图片URL
        - original_article_truncated: 原始文章截取
        - tags: 标签列表
        - user_notes: 用户注释
        - note_update_at: 笔记修改时间
        - author: 作者
        - reference: 引用
        - source: 来源
        - inspiration_source: 灵感来源
        - detailed_description: 详细描述
        - ai_expanded: AI扩写
        - ai_analysis: AI分析
        - ai_outline: AI结构大纲原始内容
        - ai_outline_nojson: AI结构大纲非JSON格式内容，如果ai_outline是有效JSON格式则为空字符串
        - created_at: 创建时间
        - updated_at: 更新时间
        - is_deleted: 是否已删除
    - total: 总记录数
    - page: 当前页码
    - size: 每页数量
    """
)
async def get_knowledge_canvas_list(
    params: KnowledgeCanvasQueryParams = Depends(),
    current_user: UserResponse = Depends(get_current_user)
):
    """获取灵感卡片列表"""
    try:
        # 构建基础查询
        base_query = KnowledgeCanvas.filter(
            is_deleted=False,
            user_id=current_user.id
        )
        
        # 关键词搜索 - 支持多字段模糊检索
        if params.keyword:
            logger.info(f"用户 {current_user.username} 执行关键词搜索: {params.keyword}")
            # 构建多字段搜索条件
            search_conditions = [
                Q(name__icontains=params.keyword),                        # 名称
                Q(original_article__icontains=params.keyword),            # 原始文章
                Q(original_article_truncated__icontains=params.keyword),  # 原始文章截取
                Q(summary__icontains=params.keyword),                     # 简介
                Q(related_notes__icontains=params.keyword),               # 关联笔记
                Q(user_notes__icontains=params.keyword),                  # 用户注释
                Q(author__icontains=params.keyword),                      # 作者
                Q(reference__icontains=params.keyword),                   # 引用
                Q(source__icontains=params.keyword),                      # 来源
                Q(inspiration_source__icontains=params.keyword),          # 灵感来源
                Q(detailed_description__icontains=params.keyword),        # 详细描述
                Q(ai_questions__icontains=params.keyword),                # AI提问
                Q(ai_expanded__icontains=params.keyword),                 # AI扩写
                Q(ai_analysis__icontains=params.keyword),                 # AI分析
                Q(ai_keynotes__icontains=params.keyword),                 # AI重点
                Q(ai_outline__icontains=params.keyword),                  # AI结构大纲
                Q(ai_probe__icontains=params.keyword),                    # AI追问
            ]
            
            # 构建标签搜索条件 - 使用子查询
            tag_ids = await KnowledgeCanvas.filter(
                tags__name__icontains=params.keyword
            ).values_list('id', flat=True)
            if tag_ids:
                search_conditions.append(Q(id__in=tag_ids))
            
            # 使用OR组合所有搜索条件
            combined_query = search_conditions[0]
            for condition in search_conditions[1:]:
                combined_query |= condition
            
            base_query = base_query.filter(combined_query)
        
        # 来源类型筛选
        if params.source_type:
            base_query = base_query.filter(source_type=CanvasSourceType(params.source_type).value)
            
        # 类型筛选
        if params.type:
            base_query = base_query.filter(type=CanvasType(params.type).value)
        
        # 获取总数（使用子查询去重）
        total = await base_query.distinct().count()
        
        # 分页查询
        offset = (params.page - 1) * params.size
        canvas_list = await base_query.distinct().prefetch_related(
            'tags'
        ).offset(offset).limit(params.size).order_by('-updated_at')
        
        # 处理返回数据
        processed_list = []
        for canvas in canvas_list:
            # 处理 key_notes
            key_notes = json.loads(canvas.key_notes) if canvas.key_notes else []
            
            # 处理 related_notes
            related_notes = []
            if canvas.related_notes:
                try:
                    notes_data = json.loads(canvas.related_notes)
                    related_notes = [RelatedNote(**note) for note in notes_data]
                except:
                    related_notes = []
            
            # 处理灵感来源
            inspiration_source = []
            if canvas.inspiration_source:
                logger.info(f"灵感来源数据: {canvas.inspiration_source}")
                try:
                    # 先尝试标准JSON解析
                    sources_data = json.loads(canvas.inspiration_source)
                    logger.info(f"灵感来源数据: {sources_data}")
                except json.JSONDecodeError:
                    try:
                        # 如果JSON解析失败，尝试使用ast.literal_eval解析Python字典格式
                        sources_data = ast.literal_eval(canvas.inspiration_source)
                        logger.info(f"使用ast.literal_eval解析灵感来源数据: {sources_data}")
                    except (ValueError, SyntaxError) as e:
                        logger.error(f"解析灵感来源失败 (JSON和Python格式都失败): {str(e)}")
                        sources_data = []
                
                
                # 格式化数据，确保字段名称正确（去除可能的空格）
                for source in sources_data:
                    source_data = {
                        "source_id": source.get("source_id", "").strip(),
                        "source_type": source.get("source_type", "").strip(),
                        "source_name": source.get("source_name", "").strip()
                    }
                    inspiration_source.append(InspirationSource(**source_data))

            # 处理AI重点注释
            ai_keynotes = []
            if canvas.ai_keynotes:
                try:
                    keynotes_data = json.loads(canvas.ai_keynotes)
                    if isinstance(keynotes_data, list):
                        # 将字典列表转换为KeyNote对象列表
                        for keynote_dict in keynotes_data:
                            try:
                                # 为缺失的字段添加默认值
                                if "keynote_title" not in keynote_dict:
                                    keynote_dict["keynote_title"] = "重点注释"
                                if "keynote_sub_title" not in keynote_dict:
                                    keynote_dict["keynote_sub_title"] = []
                                
                                keynote_obj = KeyNote(**keynote_dict)
                                ai_keynotes.append(keynote_obj)
                            except Exception as e:
                                logger.warning(f"转换KeyNote对象失败: {keynote_dict}, 错误: {str(e)}")
                                continue
                except Exception as e:
                    logger.error(f"解析AI重点注释失败: {str(e)}")
                    ai_keynotes = []
            
            # 处理标签
            tags = [tag.name for tag in canvas.tags]
            

            # 清洗ai_outline
            ai_outline_value = canvas.ai_outline or '';
            if canvas.ai_outline:
                try:
                    cleaned_outline = clean_outline_format(canvas.ai_outline)
                    # 只有在cleaned_outline不为空时才赋值，并转换为JSON字符串
                    if cleaned_outline:
                        ai_outline_value = json.dumps(cleaned_outline, ensure_ascii=False)
                        logger.info(f"ai_outline: 清洗后: {cleaned_outline}")
                    else:
                        logger.info(f"ai_outline: 清洗后为空，保持原始值")
                except Exception as e:
                    logger.error(f"解析ai_outline失败: {str(e)}")
            
            # 构建处理后的画布对象 - 使用精简版响应模型
            processed_canvas = KnowledgeCanvasListResponse.model_validate({
                "id": canvas.id,
                "name": canvas.name,
                "source_type": CanvasSourceType(canvas.source_type).value if canvas.source_type else None,
                "type": CanvasType(canvas.type).value if canvas.type else None,
                "summary": canvas.summary or '',
                "key_notes": key_notes,
                "related_notes": related_notes,
                "image_url": canvas.image_url or '',
                "original_article_truncated": canvas.original_article_truncated or '',
                "tags": tags,
                "user_notes": canvas.user_notes or '',
                "note_update_at": canvas.note_update_at,
                "author": canvas.author or '',
                "reference": canvas.reference or '',
                "source": canvas.source or '',
                "inspiration_source": inspiration_source,
                "detailed_description": canvas.detailed_description or '',
                "ai_expanded": canvas.ai_expanded or '',
                "ai_analysis": canvas.ai_analysis or '',
                "ai_outline": ai_outline_value,
                "ai_keynotes": ai_keynotes,
                "created_at": canvas.created_at,
                "updated_at": canvas.updated_at,
                "is_deleted": canvas.is_deleted
            })
            processed_list.append(processed_canvas)

        # 构建分页响应数据
        page_info = PageInfo(
            items=processed_list,
            total=total,
            page=params.page,
            size=params.size
        )
        
        return send_page_data(True, page_info)
    except Exception as e:
        logger.error(f"获取灵感卡片列表失败: {str(e)}")
        return send_page_data(False, PageInfo(items=[], total=0, page=params.page, size=params.size), f"获取灵感卡片列表失败: {str(e)}")

@router.get(
    "/{canvas_id}",
    response_model=ResponseModel[KnowledgeCanvasResponse],
    summary="获取灵感卡片详情",
    description="根据ID获取灵感卡片的详细信息。"
)
async def get_knowledge_canvas_detail(
    canvas_id: UUID,
    current_user: UserResponse = Depends(get_current_user)
):
    """获取灵感卡片详情"""
    try:
        canvas = await KnowledgeCanvas.filter(id=canvas_id, is_deleted=False).prefetch_related('tags').first()
        if not canvas:
            return send_data(False, None, "灵感卡片不存在或已删除")
        
        # 处理 key_notes
        key_notes = json.loads(canvas.key_notes) if canvas.key_notes else []
        
        # 处理 related_notes
        related_notes = []
        if canvas.related_notes:
            try:
                notes_data = json.loads(canvas.related_notes)
                related_notes = [RelatedNote(**note) for note in notes_data]
            except:
                related_notes = []
        
        
        # 处理灵感来源
        inspiration_source = []
        if canvas.inspiration_source:
            try:
                # 先尝试标准JSON解析
                sources_data = json.loads(canvas.inspiration_source)
            except json.JSONDecodeError:
                try:
                    # 如果JSON解析失败，尝试使用ast.literal_eval解析Python字典格式
                    sources_data = ast.literal_eval(canvas.inspiration_source)
                    logger.info(f"使用ast.literal_eval解析灵感来源数据: {sources_data}")
                except (ValueError, SyntaxError) as e:
                    logger.error(f"解析灵感来源失败 (JSON和Python格式都失败): {str(e)}")
                    sources_data = []
            
            # 格式化数据，确保字段名称正确（去除可能的空格）
            for source in sources_data:
                source_data = {
                    "source_id": source.get("source_id", "").strip(),
                    "source_type": source.get("source_type", "").strip(),
                    "source_name": source.get("source_name", "").strip()
                }
                inspiration_source.append(InspirationSource(**source_data))
        
        # 处理AI重点注释
        ai_keynotes = []
        if canvas.ai_keynotes:
            try:
                keynotes_data = json.loads(canvas.ai_keynotes)
                if isinstance(keynotes_data, list):
                    # 将字典列表转换为KeyNote对象列表
                    for keynote_dict in keynotes_data:
                        try:
                            # 为缺失的字段添加默认值
                            if "keynote_title" not in keynote_dict:
                                keynote_dict["keynote_title"] = "重点注释"
                            if "keynote_sub_title" not in keynote_dict:
                                keynote_dict["keynote_sub_title"] = []
                            
                            keynote_obj = KeyNote(**keynote_dict)
                            ai_keynotes.append(keynote_obj)
                        except Exception as e:
                            logger.warning(f"转换KeyNote对象失败: {keynote_dict}, 错误: {str(e)}")
                            continue
            except Exception as e:
                logger.error(f"解析AI重点注释失败: {str(e)}")
                ai_keynotes = []
        
        # 处理标签
        tags = [tag.name for tag in canvas.tags]
        
        # 清洗ai_outline
        cleaned_outline = clean_outline_format(canvas.ai_outline)
        logger.info(f"清洗后的大纲结构: {cleaned_outline}")
            
        # 构建处理后的画布对象
        processed_canvas = KnowledgeCanvasResponse.model_validate({
            "id": canvas.id,
            "name": canvas.name,
            "source_type": CanvasSourceType(canvas.source_type).value if canvas.source_type else None,
            "type": CanvasType(canvas.type).value if canvas.type else None,
            "summary": canvas.summary or '',
            "key_notes": key_notes,
            "related_notes": related_notes,
            "image_url": canvas.image_url or '',
            "original_article_truncated": canvas.original_article_truncated or '',
            "tags": tags,
            "user_notes": canvas.user_notes or '',
            "note_update_at": canvas.note_update_at,
            "note_type": canvas.note_type,
            "author": canvas.author or '',
            "reference": canvas.reference or '',
            "source": canvas.source or '',
            "inspiration_source": inspiration_source,
            "detailed_description": canvas.detailed_description or '',
            "ai_expanded": canvas.ai_expanded or '',
            "ai_analysis": canvas.ai_analysis or '',
            "ai_questions": [],  # 如果有需要，可以添加处理逻辑
            "ai_keynotes": ai_keynotes,
            "original_article": canvas.original_article or '',
            "ai_outline": canvas.ai_outline or '',
            "user_id": canvas.user_id,
            "created_at": canvas.created_at,
            "updated_at": canvas.updated_at,
            "is_deleted": canvas.is_deleted,
            "insight_report_id": canvas.insight_reports_id,
            "cleaned_outline": cleaned_outline
        })
        
        return send_data(True, processed_canvas)
    except Exception as e:
        logger.error(f"获取灵感卡片详情失败: {str(e)}")
        return send_data(False, None, f"获取灵感卡片详情失败: {str(e)}")

@router.delete(
    "/{canvas_id}",
    response_model=ResponseModel[bool],
    summary="软删除灵感卡片",
    description="根据ID软删除灵感卡片。"
)
async def soft_delete_knowledge_canvas(
    canvas_id: UUID,
    current_user: UserResponse = Depends(get_current_user)
):
    """软删除灵感卡片"""
    try:
        canvas = await KnowledgeCanvas.filter(id=canvas_id, is_deleted=False).first()
        if not canvas:
            return send_data(False, False, "灵感卡片不存在或已删除")
        
        # 标记为已删除
        canvas.is_deleted = True
        canvas.deleted_at = datetime.now()
        await canvas.save()
        
        return send_data(True, True)
    except Exception as e:
        logger.error(f"删除灵感卡片失败: {str(e)}")
        return send_data(False, False, f"删除灵感卡片失败: {str(e)}")

@router.post(
    "/create-with-tags",
    response_model=ResponseModel[Dict[str, Any]],
    summary="创建灵感卡片并生成标签",
    description="根据名称创建灵感卡片，并使用AI生成相关标签。"
)
async def create_knowledge_canvas_with_tags(
    name: str,
    original_article_truncated: str,
    current_user: UserResponse = Depends(get_current_user)
):
    """创建灵感卡片并生成标签"""
    try:
        logger.info(f"创建灵感卡片并生成标签: {name}")
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_GENERATE.value)
        logger.info(f"model_config: {model_config}")
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        # 创建灵感卡片
        canvas = await KnowledgeCanvas.create(
            name=name,
            type=CanvasType.ARTICLE,
            original_article_truncated=original_article_truncated,
            original_article=original_article_truncated,
            user_id=current_user.id
        )
        
        
        # 生成标签
        tags = await generate_tags(
            name=name,
            api_key=model_config.api_key,
            api_url=model_config.api_url,
            model=model_config.model_name
        )

        # 保存标签
        for tag_name in tags:
            # 检查标签是否已存在
            tag = await KnowledgeCanvasTag.filter(
                name=tag_name,
                user_id=current_user.id,
                is_deleted=False
            ).first()
            
            if not tag:
                # 创建新标签
                tag = await KnowledgeCanvasTag.create(
                    name=tag_name,
                    user_id=current_user.id
                )
            
            # 添加标签关联
            await canvas.tags.add(tag)

        # 返回简化的响应数据
        response_data = {
            "id": str(canvas.id),
            "tags": tags
        }
        
        return send_data(True, response_data)
    except Exception as e:
        logger.error(f"创建灵感卡片并生成标签失败: {str(e)}")
        return send_data(False, None, f"创建灵感卡片并生成标签失败: {str(e)}")

# 灵感类型卡片保存灵感卡片接口
@router.post("/save", response_model=ResponseModel[bool],summary="灵感类型卡片保存灵感卡片接口", deprecated=True)
async def save_knowledge_canvas(
    req: List[SaveInsightRequest],
    current_user: UserResponse = Depends(get_current_user)
):
    """
    灵感类型卡片保存灵感卡片接口
    
    入参:
    - req: List[SaveInsightRequest]，包含多个知识卡片信息
    
    出参:
    - success: 是否保存成功
    """
    try:
        logger.info(f"批量保存知识卡片请求: {req}")
        
        for item in req:
            # 处理inspiration_source参数 - 使用json.dumps确保标准JSON格式
            source_data = None
            if item.inspiration_source:
                source_list = [source.dict() for source in item.inspiration_source]
                source_data = json.dumps(source_list, ensure_ascii=False)
            
            # 创建知识卡片
            new_canvas = await KnowledgeCanvas.create(
                user_id=current_user.id,
                name=item.name,
                inspiration_source=source_data,
                summary=item.summary,
                source_type=CanvasSourceType.INSPIRATION.value,
                type=CanvasType.INSPIRATION.value
            )
            
            # 处理标签
            if item.tags:
                tag_objs = []
                for tag_name in item.tags:
                    tag, created = await KnowledgeCanvasTag.get_or_create(
                        name=tag_name,
                        user_id=current_user.id,
                        defaults={"user_id": current_user.id}
                    )
                    tag_objs.append(tag)
                await new_canvas.tags.add(*tag_objs)
            
        return send_data(True, True)
    except HTTPException as e:
        return send_data(False, False, str(e.detail))
    except Exception as e:
        logger.error(f"保存知识卡片失败: {str(e)}")
        return send_data(False, False, f"保存知识卡片失败: {str(e)}")


