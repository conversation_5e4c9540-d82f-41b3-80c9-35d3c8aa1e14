{"abstract": "The dominant sequence transduction models are based on complex recurrent or convolutional neural networks that include an encoder and a decoder. The best performing models also connect the encoder and decoder through an attention mechanism. We propose a new simple network architecture, the Transformer, based solely on attention mechanisms, dispensing with recurrence and convolutions entirely. Experiments on two machine translation tasks show these models to be superior in quality while being more parallelizable and requiring significantly less time to train. Our model achieves 28.4 BLEU on the WMT 2014 Englishto-German translation task, improving over the existing best results, including ensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task, our model establishes a new single-model state-of-the-art BLEU score of 41.8 after training for 3.5 days on eight GPUs, a small fraction of the training costs of the best models from the literature. We show that the Transformer generalizes well to other tasks by applying it successfully to English constituency parsing both with large and limited training data. * Equal contribution. Listing order is random. <PERSON> proposed replacing RNNs with self-attention and started the effort to evaluate this idea. <PERSON><PERSON>, with <PERSON><PERSON>, designed and implemented the first Transformer models and has been crucially involved in every aspect of this work. <PERSON><PERSON> proposed scaled dot-product attention, multi-head attention and the parameter-free position representation and became the other person involved in nearly every detail. <PERSON><PERSON> designed, implemented, tuned and evaluated countless model variants in our original codebase and tensor2tensor. <PERSON><PERSON> also experimented with novel model variants, was responsible for our initial codebase, and efficient inference and visualizations. <PERSON><PERSON>z and <PERSON> spent countless long days designing various parts of and implementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating our research.\n† Work performed while at Google Brain.\n‡ Work performed while at Google Research.", "authors": [{"affiliation": {}, "email": "<EMAIL>", "first": "<PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"affiliation": {}, "email": "", "first": "<PERSON>am", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"affiliation": {}, "email": "", "first": "Google", "last": "Brain", "middle": [], "suffix": ""}, {"affiliation": {}, "email": "<EMAIL>", "first": "<PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"affiliation": {}, "email": "", "first": "<PERSON>", "last": "Uszkoreit", "middle": [], "suffix": ""}, {"affiliation": {}, "email": "", "first": "Llion", "last": "<PERSON>", "middle": [], "suffix": ""}, {"affiliation": {}, "email": "", "first": "<PERSON>", "last": "<PERSON>", "middle": ["N"], "suffix": ""}, {"affiliation": {}, "email": "l<PERSON><PERSON><PERSON><PERSON>@google.com", "first": "<PERSON><PERSON><PERSON>", "last": "Kaiser", "middle": [], "suffix": ""}], "header": {"date_generated": "2025-09-28T19:48:59.671445Z", "generated_with": "S2ORC 1.0.0"}, "identifiers": {}, "paper_id": "Transformer.pdf", "pdf_parse": {"_pdf_hash": "a639448e61be3ab2a6fb696c0e910b0a3e5fd57f", "abstract": [{"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "Abstract", "text": "The dominant sequence transduction models are based on complex recurrent or convolutional neural networks that include an encoder and a decoder. The best performing models also connect the encoder and decoder through an attention mechanism. We propose a new simple network architecture, the Transformer, based solely on attention mechanisms, dispensing with recurrence and convolutions entirely. Experiments on two machine translation tasks show these models to be superior in quality while being more parallelizable and requiring significantly less time to train. Our model achieves 28.4 BLEU on the WMT 2014 Englishto-German translation task, improving over the existing best results, including ensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task, our model establishes a new single-model state-of-the-art BLEU score of 41.8 after training for 3.5 days on eight GPUs, a small fraction of the training costs of the best models from the literature. We show that the Transformer generalizes well to other tasks by applying it successfully to English constituency parsing both with large and limited training data. * Equal contribution. Listing order is random. <PERSON> proposed replacing RNNs with self-attention and started the effort to evaluate this idea. <PERSON><PERSON>, with <PERSON><PERSON>, designed and implemented the first Transformer models and has been crucially involved in every aspect of this work. <PERSON><PERSON> proposed scaled dot-product attention, multi-head attention and the parameter-free position representation and became the other person involved in nearly every detail. <PERSON><PERSON> designed, implemented, tuned and evaluated countless model variants in our original codebase and tensor2tensor. <PERSON><PERSON> also experimented with novel model variants, was responsible for our initial codebase, and efficient inference and visualizations. <PERSON><PERSON>z and <PERSON> spent countless long days designing various parts of and implementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating our research."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "Abstract", "text": "† Work performed while at Google Brain."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "Abstract", "text": "‡ Work performed while at Google Research."}], "back_matter": [{"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "acknowledgement", "text": "Acknowledgements We are grateful to <PERSON><PERSON> and <PERSON> for their fruitful comments, corrections and inspiration."}], "bib_entries": {"BIBREF1": {"authors": [{"first": "Dz<PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "last": "Cho", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Neural machine translation by jointly learning to align and translate. CoRR, abs/1409.0473, 2014.", "ref_id": "b1", "title": "Neural machine translation by jointly learning to align and translate", "urls": [], "venue": "", "volume": "", "year": 2014}, "BIBREF10": {"authors": [{"first": "<PERSON><PERSON>", "last": "He", "middle": [], "suffix": ""}, {"first": "Xiangyu", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "Shaoqing", "last": "Ren", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "Sun", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "770--778", "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning for im- age recognition. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, pages 770-778, 2016.", "ref_id": "b10", "title": "Deep residual learning for image recognition", "urls": [], "venue": "Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition", "volume": "", "year": 2016}, "BIBREF11": {"authors": [{"first": "Sepp", "last": "<PERSON><PERSON>reiter", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Gradient flow in recurrent nets: the difficulty of learning long-term dependencies, 2001.", "ref_id": "b11", "title": "Gradient flow in recurrent nets: the difficulty of learning long-term dependencies", "urls": [], "venue": "", "volume": "", "year": 2001}, "BIBREF12": {"authors": [{"first": "Sepp", "last": "<PERSON><PERSON>reiter", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "8", "links": null, "num": null, "other_ids": {}, "pages": "1735--1780", "raw_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>. Long short-term memory. Neural computation, 9(8):1735-1780, 1997.", "ref_id": "b12", "title": "Long short-term memory", "urls": [], "venue": "Neural computation", "volume": "9", "year": 1997}, "BIBREF13": {"authors": [{"first": "Zhongqiang", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "832--841", "raw_text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON>. Self-training PCFG grammars with latent annotations across languages. In Proceedings of the 2009 Conference on Empirical Methods in Natural Language Processing, pages 832-841. ACL, August 2009.", "ref_id": "b13", "title": "Self-training PCFG grammars with latent annotations across languages", "urls": [], "venue": "Proceedings of the 2009 Conference on Empirical Methods in Natural Language Processing", "volume": "", "year": 2009}, "BIBREF14": {"authors": [{"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Oriol", "last": "<PERSON>yal<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>am", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Yonghui", "last": "<PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1602.02410"]}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Exploring the limits of language modeling. arXiv preprint arXiv:1602.02410, 2016.", "ref_id": "b14", "title": "Exploring the limits of language modeling", "urls": [], "venue": "", "volume": "", "year": 2016}, "BIBREF15": {"authors": [{"first": "<PERSON><PERSON><PERSON>", "last": "Kaiser", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. Can active memory replace attention? In Advances in Neural Information Processing Systems, (NIPS), 2016.", "ref_id": "b15", "title": "Can active memory replace attention?", "urls": [], "venue": "Advances in Neural Information Processing Systems, (NIPS)", "volume": "", "year": 2016}, "BIBREF16": {"authors": [{"first": "<PERSON><PERSON><PERSON>", "last": "Kaiser", "middle": [], "suffix": ""}, {"first": "Ilya", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. Neural GPUs learn algorithms. In International Conference on Learning Representations (ICLR), 2016.", "ref_id": "b16", "title": "Neural GPUs learn algorithms", "urls": [], "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "year": 2016}, "BIBREF17": {"authors": [{"first": "<PERSON>l", "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "Espeholt", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "Kavukcuoglu", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1610.10099v2"]}, "pages": "", "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> <PERSON>. Neural machine translation in linear time. arXiv preprint arXiv:1610.10099v2, 2017.", "ref_id": "b17", "title": "Neural machine translation in linear time", "urls": [], "venue": "", "volume": "", "year": 2017}, "BIBREF18": {"authors": [{"first": "<PERSON>on", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": ["M"], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Structured attention networks. In International Conference on Learning Representations, 2017.", "ref_id": "b18", "title": "Structured attention networks", "urls": [], "venue": "International Conference on Learning Representations", "volume": "", "year": 2017}, "BIBREF19": {"authors": [{"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Ba", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>: A method for stochastic optimization. In ICLR, 2015.", "ref_id": "b19", "title": "Adam: A method for stochastic optimization", "urls": [], "venue": "ICLR", "volume": "", "year": 2015}, "BIBREF2": {"authors": [{"first": "<PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "V", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "", "last": "Le", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> <PERSON><PERSON>. Massive exploration of neural machine translation architectures. CoRR, abs/1703.03906, 2017.", "ref_id": "b2", "title": "Massive exploration of neural machine translation architectures", "urls": [], "venue": "", "volume": "", "year": 2017}, "BIBREF20": {"authors": [{"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Ginsburg", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1703.10722"]}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. Factorization tricks for LSTM networks. arXiv preprint arXiv:1703.10722, 2017.", "ref_id": "b20", "title": "Factorization tricks for LSTM networks", "urls": [], "venue": "", "volume": "", "year": 2017}, "BIBREF21": {"authors": [{"first": "<PERSON><PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "Cicero", "last": "<PERSON><PERSON>ira <PERSON> Santos", "middle": [], "suffix": ""}, {"first": "Mo", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Xiang", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1703.03130"]}, "pages": "", "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. A structured self-attentive sentence embedding. arXiv preprint arXiv:1703.03130, 2017.", "ref_id": "b21", "title": "A structured self-attentive sentence embedding", "urls": [], "venue": "", "volume": "", "year": 2017}, "BIBREF22": {"authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "Le", "middle": ["V"], "suffix": ""}, {"first": "Ilya", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Oriol", "last": "<PERSON>yal<PERSON>", "middle": [], "suffix": ""}, {"first": "Lukasz", "last": "Kaiser", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1511.06114"]}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Multi-task sequence to sequence learning. arXiv preprint arXiv:1511.06114, 2015.", "ref_id": "b22", "title": "Multi-task sequence to sequence learning", "urls": [], "venue": "", "volume": "", "year": 2015}, "BIBREF23": {"authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": ["D"], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1508.04025"]}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Effective approaches to attention- based neural machine translation. arXiv preprint arXiv:1508.04025, 2015.", "ref_id": "b23", "title": "Effective approaches to attentionbased neural machine translation", "urls": [], "venue": "", "volume": "", "year": 2015}, "BIBREF24": {"authors": [{"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Santorini", "middle": [], "suffix": ""}], "issue": "2", "links": null, "num": null, "other_ids": {}, "pages": "313--330", "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Building a large annotated corpus of english: The penn treebank. Computational linguistics, 19(2):313-330, 1993.", "ref_id": "b24", "title": "Building a large annotated corpus of english: The penn treebank", "urls": [], "venue": "Computational linguistics", "volume": "19", "year": 1993}, "BIBREF25": {"authors": [{"first": "<PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Eugene", "last": "Charniak", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "152--159", "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Effective self-training for parsing. In Proceedings of the Human Language Technology Conference of the NAACL, Main Conference, pages 152-159. ACL, June 2006.", "ref_id": "b25", "title": "Effective self-training for parsing", "urls": [], "venue": "Proceedings of the Human Language Technology Conference of the NAACL, Main Conference", "volume": "", "year": 2006}, "BIBREF26": {"authors": [{"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Uszkoreit", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A decomposable attention model. In Empirical Methods in Natural Language Processing, 2016.", "ref_id": "b26", "title": "A decomposable attention model", "urls": [], "venue": "Empirical Methods in Natural Language Processing", "volume": "", "year": 2016}, "BIBREF27": {"authors": [{"first": "<PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Caiming", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1705.04304"]}, "pages": "", "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A deep reinforced model for abstractive summarization. arXiv preprint arXiv:1705.04304, 2017.", "ref_id": "b27", "title": "A deep reinforced model for abstractive summarization", "urls": [], "venue": "", "volume": "", "year": 2017}, "BIBREF28": {"authors": [{"first": "Slav", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "433--440", "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Learning accurate, compact, and interpretable tree annotation. In Proceedings of the 21st International Conference on Computational Linguistics and 44th Annual Meeting of the ACL, pages 433-440. ACL, July 2006.", "ref_id": "b28", "title": "Learning accurate, compact, and interpretable tree annotation", "urls": [], "venue": "Proceedings of the 21st International Conference on Computational Linguistics and 44th Annual Meeting of the ACL", "volume": "", "year": 2006}, "BIBREF29": {"authors": [], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1608.05859"]}, "pages": "", "raw_text": "Ofir Press and Lior Wolf. Using the output embedding to improve language models. arXiv preprint arXiv:1608.05859, 2016.", "ref_id": "b29", "title": "Using the output embedding to improve language models", "urls": [], "venue": "", "volume": "", "year": 2016}, "BIBREF3": {"authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "Li", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "Lapata", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1601.06733"]}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Long short-term memory-networks for machine reading. arXiv preprint arXiv:1601.06733, 2016.", "ref_id": "b3", "title": "Long short-term memory-networks for machine reading", "urls": [], "venue": "", "volume": "", "year": 2016}, "BIBREF30": {"authors": [{"first": "Rico", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Haddow", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1508.07909"]}, "pages": "", "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Neural machine translation of rare words with subword units. arXiv preprint arXiv:1508.07909, 2015.", "ref_id": "b30", "title": "Neural machine translation of rare words with subword units", "urls": [], "venue": "", "volume": "", "year": 2015}, "BIBREF31": {"authors": [{"first": "<PERSON>am", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Azalia", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "Le", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1701.06538"]}, "pages": "", "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Outrageously large neural networks: The sparsely-gated mixture-of-experts layer. arXiv preprint arXiv:1701.06538, 2017.", "ref_id": "b31", "title": "Outrageously large neural networks: The sparsely-gated mixture-of-experts layer", "urls": [], "venue": "", "volume": "", "year": 2017}, "BIBREF32": {"authors": [{"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON>", "middle": ["E"], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Ilya", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "1", "links": null, "num": null, "other_ids": {}, "pages": "1929--1958", "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>- nov. Dropout: a simple way to prevent neural networks from overfitting. Journal of Machine Learning Research, 15(1):1929-1958, 2014.", "ref_id": "b32", "title": "Dropout: a simple way to prevent neural networks from overfitting", "urls": [], "venue": "Journal of Machine Learning Research", "volume": "15", "year": 2014}, "BIBREF33": {"authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "last": "Sukhbaatar", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Szlam", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Weston", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "2440--2448", "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. End-to-end memory networks. In <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural Information Processing Systems 28, pages 2440-2448. Curran Associates, Inc., 2015.", "ref_id": "b33", "title": "End-to-end memory networks", "urls": [], "venue": "Advances in Neural Information Processing Systems", "volume": "28", "year": 2015}, "BIBREF34": {"authors": [{"first": "Ilya", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Oriol", "last": "<PERSON>yal<PERSON>", "middle": [], "suffix": ""}, {"first": "Quoc Vv", "last": "Le", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "3104--3112", "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and Quoc VV Le. Sequence to sequence learning with neural networks. In Advances in Neural Information Processing Systems, pages 3104-3112, 2014.", "ref_id": "b34", "title": "Sequence to sequence learning with neural networks", "urls": [], "venue": "Advances in Neural Information Processing Systems", "volume": "", "year": 2014}, "BIBREF35": {"authors": [{"first": "<PERSON>", "last": "Szegedy", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Ioffe", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Rethinking the inception architecture for computer vision. CoRR, abs/1512.00567, 2015.", "ref_id": "b35", "title": "Rethinking the inception architecture for computer vision", "urls": [], "venue": "", "volume": "", "year": 2015}, "BIBREF36": {"authors": [{"first": "", "last": "<PERSON>yal<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "Kaiser", "middle": [], "suffix": ""}, {"first": "", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "Vinyals & Kaiser, Ko<PERSON>, <PERSON>, <PERSON>ever, and Hinton. Grammar as a foreign language. In Advances in Neural Information Processing Systems, 2015.", "ref_id": "b36", "title": "Grammar as a foreign language", "urls": [], "venue": "Advances in Neural Information Processing Systems", "volume": "", "year": 2015}, "BIBREF37": {"authors": [{"first": "Yonghui", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "V", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Le", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Maxim", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Yuan", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "Qin", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Gao", "middle": [], "suffix": ""}, {"first": "", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1609.08144"]}, "pages": "", "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Google's neural machine translation system: Bridging the gap between human and machine translation. arXiv preprint arXiv:1609.08144, 2016.", "ref_id": "b37", "title": "Google's neural machine translation system: Bridging the gap between human and machine translation", "urls": [], "venue": "", "volume": "", "year": 2016}, "BIBREF38": {"authors": [{"first": "<PERSON><PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "Xuguang", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "Li", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Deep recurrent models with fast-forward connections for neural machine translation. CoRR, abs/1606.04199, 2016.", "ref_id": "b38", "title": "Deep recurrent models with fast-forward connections for neural machine translation", "urls": [], "venue": "", "volume": "", "year": 2016}, "BIBREF39": {"authors": [{"first": "Muhua", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "Min", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "Jingbo", "last": "<PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "434--443", "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Fast and accurate shift-reduce constituent parsing. In Proceedings of the 51st Annual Meeting of the ACL (Volume 1: Long Papers), pages 434-443. ACL, August 2013.", "ref_id": "b39", "title": "Fast and accurate shift-reduce constituent parsing", "urls": [], "venue": "Proceedings of the 51st Annual Meeting of the ACL", "volume": "1", "year": 2013}, "BIBREF4": {"authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "last": "Cho", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "Schwenk", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Learning phrase representations using rnn encoder-decoder for statistical machine translation. CoRR, abs/1406.1078, 2014.", "ref_id": "b4", "title": "Learning phrase representations using rnn encoder-decoder for statistical machine translation", "urls": [], "venue": "", "volume": "", "year": 2014}, "BIBREF5": {"authors": [{"first": "<PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1610.02357"]}, "pages": "", "raw_text": "<PERSON><PERSON>. Xception: Deep learning with depthwise separable convolutions. arXiv preprint arXiv:1610.02357, 2016.", "ref_id": "b5", "title": "Xception: Deep learning with depthwise separable convolutions", "urls": [], "venue": "", "volume": "", "year": 2016}, "BIBREF6": {"authors": [{"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "Çaglar", "last": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "last": "Cho", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Empirical evaluation of gated recurrent neural networks on sequence modeling. CoRR, abs/1412.3555, 2014.", "ref_id": "b6", "title": "Empirical evaluation of gated recurrent neural networks on sequence modeling", "urls": [], "venue": "", "volume": "", "year": 2014}, "BIBREF7": {"authors": [{"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Ballesteros", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON>", "middle": ["A"], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {}, "pages": "", "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Recurrent neural network grammars. In Proc. of NAACL, 2016.", "ref_id": "b7", "title": "Recurrent neural network grammars", "urls": [], "venue": "Proc. of NAACL", "volume": "", "year": 2016}, "BIBREF8": {"authors": [{"first": "<PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Grangier", "middle": [], "suffix": ""}, {"first": "<PERSON>", "last": "Ya<PERSON><PERSON>", "middle": [], "suffix": ""}, {"first": "<PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "middle": ["N"], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1705.03122v2"]}, "pages": "", "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Convolu- tional sequence to sequence learning. arXiv preprint arXiv:1705.03122v2, 2017.", "ref_id": "b8", "title": "Convolutional sequence to sequence learning", "urls": [], "venue": "", "volume": "", "year": 2017}, "BIBREF9": {"authors": [{"first": "<PERSON>", "last": "<PERSON>", "middle": [], "suffix": ""}], "issue": "", "links": null, "num": null, "other_ids": {"arXiv": ["arXiv:1308.0850"]}, "pages": "", "raw_text": "<PERSON>. Generating sequences with recurrent neural networks. arXiv preprint arXiv:1308.0850, 2013.", "ref_id": "b9", "title": "Generating sequences with recurrent neural networks", "urls": [], "venue": "", "volume": "", "year": 2013}}, "body_text": [{"cite_spans": [{"end": 54, "ref_id": "BIBREF12", "start": 50, "text": "[13]"}, {"end": 78, "ref_id": "BIBREF6", "start": 75, "text": "[7]"}, {"end": 271, "ref_id": "BIBREF34", "start": 267, "text": "[35,"}, {"end": 274, "ref_id": "BIBREF1", "start": 272, "text": "2,"}, {"end": 277, "ref_id": "BIBREF4", "start": 275, "text": "5]"}, {"end": 408, "ref_id": "BIBREF37", "start": 404, "text": "[38,"}, {"end": 412, "ref_id": "BIBREF23", "start": 409, "text": "24,"}, {"end": 416, "ref_id": "BIBREF14", "start": 413, "text": "15]"}], "eq_spans": [], "ref_spans": [], "sec_num": "1", "section": "Introduction", "text": "Recurrent neural networks, long short-term memory [13] and gated recurrent [7] neural networks in particular, have been firmly established as state of the art approaches in sequence modeling and transduction problems such as language modeling and machine translation [35, 2, 5] . Numerous efforts have since continued to push the boundaries of recurrent language models and encoder-decoder architectures [38, 24, 15] ."}, {"cite_spans": [{"end": 590, "ref_id": "BIBREF20", "start": 586, "text": "[21]"}, {"end": 623, "ref_id": "BIBREF31", "start": 619, "text": "[32]"}], "eq_spans": [], "ref_spans": [], "sec_num": "1", "section": "Introduction", "text": "Recurrent models typically factor computation along the symbol positions of the input and output sequences. Aligning the positions to steps in computation time, they generate a sequence of hidden states h t , as a function of the previous hidden state h t-1 and the input for position t. This inherently sequential nature precludes parallelization within training examples, which becomes critical at longer sequence lengths, as memory constraints limit batching across examples. Recent work has achieved significant improvements in computational efficiency through factorization tricks [21] and conditional computation [32] , while also improving model performance in case of the latter. The fundamental constraint of sequential computation, however, remains."}, {"cite_spans": [{"end": 227, "ref_id": "BIBREF1", "start": 224, "text": "[2,"}, {"end": 231, "ref_id": "BIBREF18", "start": 228, "text": "19]"}, {"end": 261, "ref_id": "BIBREF26", "start": 257, "text": "[27]"}], "eq_spans": [], "ref_spans": [], "sec_num": "1", "section": "Introduction", "text": "Attention mechanisms have become an integral part of compelling sequence modeling and transduction models in various tasks, allowing modeling of dependencies without regard to their distance in the input or output sequences [2, 19] . In all but a few cases [27] , however, such attention mechanisms are used in conjunction with a recurrent network."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "1", "section": "Introduction", "text": "In this work we propose the Transformer, a model architecture eschewing recurrence and instead relying entirely on an attention mechanism to draw global dependencies between input and output. The Transformer allows for significantly more parallelization and can reach a new state of the art in translation quality after being trained for as little as twelve hours on eight P100 GPUs."}, {"cite_spans": [{"end": 101, "ref_id": "BIBREF15", "start": 97, "text": "[16]"}, {"end": 116, "ref_id": "BIBREF17", "start": 112, "text": "[18]"}, {"end": 132, "ref_id": "BIBREF8", "start": 129, "text": "[9]"}, {"end": 578, "ref_id": "BIBREF11", "start": 574, "text": "[12]"}], "eq_spans": [], "ref_spans": [], "sec_num": "2", "section": "Background", "text": "The goal of reducing sequential computation also forms the foundation of the Extended Neural GPU [16] , ByteNet [18] and ConvS2S [9] , all of which use convolutional neural networks as basic building block, computing hidden representations in parallel for all input and output positions. In these models, the number of operations required to relate signals from two arbitrary input or output positions grows in the distance between positions, linearly for ConvS2S and logarithmically for ByteNet. This makes it more difficult to learn dependencies between distant positions [12] . In the Transformer this is reduced to a constant number of operations, albeit at the cost of reduced effective resolution due to averaging attention-weighted positions, an effect we counteract with Multi-Head Attention as described in section 3.2."}, {"cite_spans": [{"end": 380, "ref_id": "BIBREF3", "start": 377, "text": "[4,"}, {"end": 384, "ref_id": "BIBREF26", "start": 381, "text": "27,"}, {"end": 388, "ref_id": "BIBREF27", "start": 385, "text": "28,"}, {"end": 392, "ref_id": "BIBREF21", "start": 389, "text": "22]"}], "eq_spans": [], "ref_spans": [], "sec_num": "2", "section": "Background", "text": "Self-attention, sometimes called intra-attention is an attention mechanism relating different positions of a single sequence in order to compute a representation of the sequence. Self-attention has been used successfully in a variety of tasks including reading comprehension, abstractive summarization, textual entailment and learning task-independent sentence representations [4, 27, 28, 22] ."}, {"cite_spans": [{"end": 216, "ref_id": "BIBREF33", "start": 212, "text": "[34]"}], "eq_spans": [], "ref_spans": [], "sec_num": "2", "section": "Background", "text": "End-to-end memory networks are based on a recurrent attention mechanism instead of sequencealigned recurrence and have been shown to perform well on simple-language question answering and language modeling tasks [34] ."}, {"cite_spans": [{"end": 360, "ref_id": "BIBREF16", "start": 356, "text": "[17,"}, {"end": 364, "ref_id": "BIBREF17", "start": 361, "text": "18]"}, {"end": 372, "ref_id": "BIBREF8", "start": 369, "text": "[9]"}], "eq_spans": [], "ref_spans": [], "sec_num": "2", "section": "Background", "text": "To the best of our knowledge, however, the Transformer is the first transduction model relying entirely on self-attention to compute representations of its input and output without using sequencealigned RNNs or convolution. In the following sections, we will describe the Transformer, motivate self-attention and discuss its advantages over models such as [17, 18] and [9] ."}, {"cite_spans": [{"end": 90, "ref_id": "BIBREF4", "start": 87, "text": "[5,"}, {"end": 93, "ref_id": "BIBREF1", "start": 91, "text": "2,"}, {"end": 97, "ref_id": "BIBREF34", "start": 94, "text": "35]"}, {"end": 405, "ref_id": "BIBREF9", "start": 401, "text": "[10]"}], "eq_spans": [], "ref_spans": [{"end": 695, "ref_id": "FIGREF0", "start": 694, "text": "1"}], "sec_num": "3", "section": "Model Architecture", "text": "Most competitive neural sequence transduction models have an encoder-decoder structure [5, 2, 35] . Here, the encoder maps an input sequence of symbol representations (x 1 , ..., x n ) to a sequence of continuous representations z = (z 1 , ..., z n ). Given z, the decoder then generates an output sequence (y 1 , ..., y m ) of symbols one element at a time. At each step the model is auto-regressive [10] , consuming the previously generated symbols as additional input when generating the next. The Transformer follows this overall architecture using stacked self-attention and point-wise, fully connected layers for both the encoder and decoder, shown in the left and right halves of Figure 1 , respectively."}, {"cite_spans": [{"end": 269, "ref_id": "BIBREF10", "start": 265, "text": "[11]"}, {"end": 340, "ref_id": null, "start": 337, "text": "[1]"}], "eq_spans": [], "ref_spans": [], "sec_num": "3.1", "section": "Encoder and Decoder Stacks", "text": "Encoder: The encoder is composed of a stack of N = 6 identical layers. Each layer has two sub-layers. The first is a multi-head self-attention mechanism, and the second is a simple, positionwise fully connected feed-forward network. We employ a residual connection [11] around each of the two sub-layers, followed by layer normalization [1] . That is, the output of each sub-layer is LayerNorm(x + Sublayer(x)), where Sublayer(x) is the function implemented by the sub-layer itself. To facilitate these residual connections, all sub-layers in the model, as well as the embedding layers, produce outputs of dimension d model = 512."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.1", "section": "Encoder and Decoder Stacks", "text": "Decoder: The decoder is also composed of a stack of N = 6 identical layers. In addition to the two sub-layers in each encoder layer, the decoder inserts a third sub-layer, which performs multi-head attention over the output of the encoder stack. Similar to the encoder, we employ residual connections around each of the sub-layers, followed by layer normalization. We also modify the self-attention sub-layer in the decoder stack to prevent positions from attending to subsequent positions. This masking, combined with fact that the output embeddings are offset by one position, ensures that the predictions for position i can depend only on the known outputs at positions less than i."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2", "section": "Attention", "text": "An attention function can be described as mapping a query and a set of key-value pairs to an output, where the query, keys, values, and output are all vectors. The output is computed as a weighted sum Scaled Dot-Product Attention Multi-Head Attention of the values, where the weight assigned to each value is computed by a compatibility function of the query with the corresponding key."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [{"end": 73, "ref_id": "FIGREF1", "start": 72, "text": "2"}], "sec_num": "3.2.1", "section": "Scaled Dot-Product Attention", "text": "We call our particular attention \"Scaled Dot-Product Attention\" (Figure 2 ). The input consists of queries and keys of dimension d k , and values of dimension d v . We compute the dot products of the query with all keys, divide each by √ d k , and apply a softmax function to obtain the weights on the values."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.1", "section": "Scaled Dot-Product Attention", "text": "In practice, we compute the attention function on a set of queries simultaneously, packed together into a matrix Q. The keys and values are also packed together into matrices K and V . We compute the matrix of outputs as:"}, {"cite_spans": [], "eq_spans": [{"end": 8, "eq_num": "(1)", "raw_str": "Attention(Q, K, V ) = softmax( QK T √ d k )V", "ref_id": "EQREF", "start": 0, "text": "EQUATION"}], "ref_spans": [], "sec_num": "3.2.1", "section": "Scaled Dot-Product Attention", "text": "EQUATION"}, {"cite_spans": [{"end": 73, "ref_id": "BIBREF1", "start": 70, "text": "[2]"}], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.1", "section": "Scaled Dot-Product Attention", "text": "The two most commonly used attention functions are additive attention [2] , and dot-product (multiplicative) attention. Dot-product attention is identical to our algorithm, except for the scaling factor of 1"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.1", "section": "Scaled Dot-Product Attention", "text": "√ d k"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.1", "section": "Scaled Dot-Product Attention", "text": ". Additive attention computes the compatibility function using a feed-forward network with a single hidden layer. While the two are similar in theoretical complexity, dot-product attention is much faster and more space-efficient in practice, since it can be implemented using highly optimized matrix multiplication code."}, {"cite_spans": [{"end": 165, "ref_id": "BIBREF2", "start": 162, "text": "[3]"}], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.1", "section": "Scaled Dot-Product Attention", "text": "While for small values of d k the two mechanisms perform similarly, additive attention outperforms dot product attention without scaling for larger values of d k [3] . We suspect that for large values of d k , the dot products grow large in magnitude, pushing the softmax function into regions where it has extremely small gradients4 . To counteract this effect, we scale the dot products by 1"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.1", "section": "Scaled Dot-Product Attention", "text": "√ d k ."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [{"end": 532, "ref_id": "FIGREF1", "start": 531, "text": "2"}], "sec_num": "3.2.2", "section": "Multi-Head Attention", "text": "Instead of performing a single attention function with d model -dimensional keys, values and queries, we found it beneficial to linearly project the queries, keys and values h times with different, learned linear projections to d k , d k and d v dimensions, respectively. On each of these projected versions of queries, keys and values we then perform the attention function in parallel, yielding d v -dimensional output values. These are concatenated and once again projected, resulting in the final values, as depicted in Figure 2 ."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.2", "section": "Multi-Head Attention", "text": "Multi-head attention allows the model to jointly attend to information from different representation subspaces at different positions. With a single attention head, averaging inhibits this."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.2", "section": "Multi-Head Attention", "text": "MultiHead(Q, K, V ) = Concat(head 1 , ..., head h )W O"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.2", "section": "Multi-Head Attention", "text": "where"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.2", "section": "Multi-Head Attention", "text": "head i = Attention(QW Q i , KW K i , V W V i )"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.2", "section": "Multi-Head Attention", "text": "Where the projections are parameter matrices"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.2", "section": "Multi-Head Attention", "text": "W Q i ∈ R dmodel×d k , W K i ∈ R dmodel×d k , W V i ∈ R dmodel×dv and W O ∈ R hdv×dmodel ."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.2", "section": "Multi-Head Attention", "text": "In this work we employ h = 8 parallel attention layers, or heads. For each of these we use"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.2", "section": "Multi-Head Attention", "text": "d k = d v = d model /h = 64."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.2", "section": "Multi-Head Attention", "text": "Due to the reduced dimension of each head, the total computational cost is similar to that of single-head attention with full dimensionality."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.3", "section": "Applications of Attention in our Model", "text": "The Transformer uses multi-head attention in three different ways:"}, {"cite_spans": [{"end": 357, "ref_id": "BIBREF37", "start": 353, "text": "[38,"}, {"end": 360, "ref_id": "BIBREF1", "start": 358, "text": "2,"}, {"end": 363, "ref_id": "BIBREF8", "start": 361, "text": "9]"}], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.3", "section": "Applications of Attention in our Model", "text": "• In \"encoder-decoder attention\" layers, the queries come from the previous decoder layer, and the memory keys and values come from the output of the encoder. This allows every position in the decoder to attend over all positions in the input sequence. This mimics the typical encoder-decoder attention mechanisms in sequence-to-sequence models such as [38, 2, 9] ."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.2.3", "section": "Applications of Attention in our Model", "text": "• The encoder contains self-attention layers. In a self-attention layer all of the keys, values and queries come from the same place, in this case, the output of the previous layer in the encoder. Each position in the encoder can attend to all positions in the previous layer of the encoder."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [{"end": 444, "ref_id": "FIGREF1", "start": 443, "text": "2"}], "sec_num": "3.2.3", "section": "Applications of Attention in our Model", "text": "• Similarly, self-attention layers in the decoder allow each position in the decoder to attend to all positions in the decoder up to and including that position. We need to prevent leftward information flow in the decoder to preserve the auto-regressive property. We implement this inside of scaled dot-product attention by masking out (setting to -∞) all values in the input of the softmax which correspond to illegal connections. See Figure 2 ."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.3", "section": "Position-wise Feed-Forward Networks", "text": "In addition to attention sub-layers, each of the layers in our encoder and decoder contains a fully connected feed-forward network, which is applied to each position separately and identically. This consists of two linear transformations with a ReLU activation in between."}, {"cite_spans": [], "eq_spans": [{"end": 8, "eq_num": "(2)", "raw_str": "FFN(x) = max(0, xW 1 + b 1 )W 2 + b 2", "ref_id": "EQREF", "start": 0, "text": "EQUATION"}], "ref_spans": [], "sec_num": "3.3", "section": "Position-wise Feed-Forward Networks", "text": "EQUATION"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.3", "section": "Position-wise Feed-Forward Networks", "text": "While the linear transformations are the same across different positions, they use different parameters from layer to layer. Another way of describing this is as two convolutions with kernel size 1."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.3", "section": "Position-wise Feed-Forward Networks", "text": "The dimensionality of input and output is d model = 512, and the inner-layer has dimensionality d f f = 2048."}, {"cite_spans": [{"end": 434, "ref_id": "BIBREF29", "start": 430, "text": "[30]"}], "eq_spans": [], "ref_spans": [], "sec_num": "3.4", "section": "Embeddings and Softmax", "text": "Similarly to other sequence transduction models, we use learned embeddings to convert the input tokens and output tokens to vectors of dimension d model . We also use the usual learned linear transformation and softmax function to convert the decoder output to predicted next-token probabilities. In our model, we share the same weight matrix between the two embedding layers and the pre-softmax linear transformation, similar to [30] . In the embedding layers, we multiply those weights by √ d model . "}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.4", "section": "Embeddings and Softmax", "text": "(n 2 • d) O(1) O(1) Recurrent O(n • d 2 ) O(n) O(n) Convolutional O(k • n • d 2 ) O(1) O(log k (n)) Self-Attention (restricted) O(r • n • d) O(1) O(n/r)"}, {"cite_spans": [{"end": 520, "ref_id": "BIBREF8", "start": 517, "text": "[9]"}], "eq_spans": [], "ref_spans": [], "sec_num": "3.5", "section": "Positional Encoding", "text": "Since our model contains no recurrence and no convolution, in order for the model to make use of the order of the sequence, we must inject some information about the relative or absolute position of the tokens in the sequence. To this end, we add \"positional encodings\" to the input embeddings at the bottoms of the encoder and decoder stacks. The positional encodings have the same dimension d model as the embeddings, so that the two can be summed. There are many choices of positional encodings, learned and fixed [9] ."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.5", "section": "Positional Encoding", "text": "In this work, we use sine and cosine functions of different frequencies:"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.5", "section": "Positional Encoding", "text": "P E (pos,2i) = sin(pos/10000 2i/dmodel ) P E (pos,2i+1) = cos(pos/10000 2i/dmodel )"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "3.5", "section": "Positional Encoding", "text": "where pos is the position and i is the dimension. That is, each dimension of the positional encoding corresponds to a sinusoid. The wavelengths form a geometric progression from 2π to 10000 • 2π. We chose this function because we hypothesized it would allow the model to easily learn to attend by relative positions, since for any fixed offset k, P E pos+k can be represented as a linear function of P E pos ."}, {"cite_spans": [{"end": 65, "ref_id": "BIBREF8", "start": 62, "text": "[9]"}], "eq_spans": [], "ref_spans": [{"end": 153, "ref_id": "TABREF2", "start": 152, "text": "3"}], "sec_num": "3.5", "section": "Positional Encoding", "text": "We also experimented with using learned positional embeddings [9] instead, and found that the two versions produced nearly identical results (see Table 3 row (E)). We chose the sinusoidal version because it may allow the model to extrapolate to sequence lengths longer than the ones encountered during training."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "4", "section": "Why Self-Attention", "text": "In this section we compare various aspects of self-attention layers to the recurrent and convolutional layers commonly used for mapping one variable-length sequence of symbol representations (x 1 , ..., x n ) to another sequence of equal length (z 1 , ..., z n ), with x i , z i ∈ R d , such as a hidden layer in a typical sequence transduction encoder or decoder. Motivating our use of self-attention we consider three desiderata."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "4", "section": "Why Self-Attention", "text": "One is the total computational complexity per layer. Another is the amount of computation that can be parallelized, as measured by the minimum number of sequential operations required."}, {"cite_spans": [{"end": 469, "ref_id": "BIBREF11", "start": 465, "text": "[12]"}], "eq_spans": [], "ref_spans": [], "sec_num": "4", "section": "Why Self-Attention", "text": "The third is the path length between long-range dependencies in the network. Learning long-range dependencies is a key challenge in many sequence transduction tasks. One key factor affecting the ability to learn such dependencies is the length of the paths forward and backward signals have to traverse in the network. The shorter these paths between any combination of positions in the input and output sequences, the easier it is to learn long-range dependencies [12] . Hence we also compare the maximum path length between any two input and output positions in networks composed of the different layer types."}, {"cite_spans": [{"end": 503, "ref_id": "BIBREF37", "start": 499, "text": "[38]"}, {"end": 522, "ref_id": "BIBREF30", "start": 518, "text": "[31]"}], "eq_spans": [], "ref_spans": [{"end": 19, "ref_id": "TABREF0", "start": 18, "text": "1"}], "sec_num": "4", "section": "Why Self-Attention", "text": "As noted in Table 1 , a self-attention layer connects all positions with a constant number of sequentially executed operations, whereas a recurrent layer requires O(n) sequential operations. In terms of computational complexity, self-attention layers are faster than recurrent layers when the sequence length n is smaller than the representation dimensionality d, which is most often the case with sentence representations used by state-of-the-art models in machine translations, such as word-piece [38] and byte-pair [31] representations. To improve computational performance for tasks involving very long sequences, self-attention could be restricted to considering only a neighborhood of size r in the input sequence centered around the respective output position. This would increase the maximum path length to O(n/r). We plan to investigate this approach further in future work."}, {"cite_spans": [{"end": 259, "ref_id": "BIBREF17", "start": 255, "text": "[18]"}, {"end": 464, "ref_id": "BIBREF5", "start": 461, "text": "[6]"}], "eq_spans": [], "ref_spans": [], "sec_num": "4", "section": "Why Self-Attention", "text": "A single convolutional layer with kernel width k < n does not connect all pairs of input and output positions. Doing so requires a stack of O(n/k) convolutional layers in the case of contiguous kernels, or O(log k (n)) in the case of dilated convolutions [18] , increasing the length of the longest paths between any two positions in the network. Convolutional layers are generally more expensive than recurrent layers, by a factor of k. Separable convolutions [6] , however, decrease the complexity considerably, to O(k"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "4", "section": "Why Self-Attention", "text": "• n • d + n • d 2 )."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "4", "section": "Why Self-Attention", "text": "Even with k = n, however, the complexity of a separable convolution is equal to the combination of a self-attention layer and a point-wise feed-forward layer, the approach we take in our model."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "4", "section": "Why Self-Attention", "text": "As side benefit, self-attention could yield more interpretable models. We inspect attention distributions from our models and present and discuss examples in the appendix. Not only do individual attention heads clearly learn to perform different tasks, many appear to exhibit behavior related to the syntactic and semantic structure of the sentences."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "5", "section": "Training", "text": "This section describes the training regime for our models."}, {"cite_spans": [{"end": 158, "ref_id": "BIBREF2", "start": 155, "text": "[3]"}, {"end": 396, "ref_id": "BIBREF37", "start": 392, "text": "[38]"}], "eq_spans": [], "ref_spans": [], "sec_num": "5.1", "section": "Training Data and Batching", "text": "We trained on the standard WMT 2014 English-German dataset consisting of about 4.5 million sentence pairs. Sentences were encoded using byte-pair encoding [3] , which has a shared sourcetarget vocabulary of about 37000 tokens. For English-French, we used the significantly larger WMT 2014 English-French dataset consisting of 36M sentences and split tokens into a 32000 word-piece vocabulary [38] . Sentence pairs were batched together by approximate sequence length. Each training batch contained a set of sentence pairs containing approximately 25000 source tokens and 25000 target tokens."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [{"end": 311, "ref_id": "TABREF2", "start": 310, "text": "3"}], "sec_num": "5.2", "section": "Hardware and Schedule", "text": "We trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models using the hyperparameters described throughout the paper, each training step took about 0.4 seconds. We trained the base models for a total of 100,000 steps or 12 hours. For our big models,(described on the bottom line of table 3 ), step time was 1.0 seconds. The big models were trained for 300,000 steps (3.5 days)."}, {"cite_spans": [{"end": 31, "ref_id": "BIBREF19", "start": 27, "text": "[20]"}], "eq_spans": [], "ref_spans": [], "sec_num": "5.3", "section": "Optimizer", "text": "We used the Adam optimizer [20] with β 1 = 0.9, β 2 = 0.98 and ϵ = 10 -9 . We varied the learning rate over the course of training, according to the formula:"}, {"cite_spans": [], "eq_spans": [{"end": 8, "eq_num": "(3)", "raw_str": "lrate = d -0.5 model • min(step_num -0.5 , step_num • warmup_steps -1.5 )", "ref_id": "EQREF", "start": 0, "text": "EQUATION"}], "ref_spans": [], "sec_num": "5.3", "section": "Optimizer", "text": "EQUATION"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "5.3", "section": "Optimizer", "text": "This corresponds to increasing the learning rate linearly for the first warmup_steps training steps, and decreasing it thereafter proportionally to the inverse square root of the step number. We used warmup_steps = 4000."}, {"cite_spans": [{"end": 95, "ref_id": "BIBREF32", "start": 91, "text": "[33]"}], "eq_spans": [], "ref_spans": [], "sec_num": "5.4", "section": "Regularization", "text": "We employ three types of regularization during training: Residual Dropout We apply dropout [33] to the output of each sub-layer, before it is added to the sub-layer input and normalized. In addition, we apply dropout to the sums of the embeddings and the positional encodings in both the encoder and decoder stacks. For the base model, we use a rate of P drop = 0.1."}, {"cite_spans": [{"end": 69, "ref_id": "BIBREF35", "start": 65, "text": "[36]"}], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "Label Smoothing", "text": "During training, we employed label smoothing of value ϵ ls = 0.1 [36] . This hurts perplexity, as the model learns to be more unsure, but improves accuracy and BLEU score."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "Label Smoothing", "text": "6 Results"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [{"end": 107, "ref_id": "TABREF1", "start": 106, "text": "2"}, {"end": 330, "ref_id": "TABREF2", "start": 329, "text": "3"}], "sec_num": "6.1", "section": "Machine Translation", "text": "On the WMT 2014 English-to-German translation task, the big transformer model (Transformer (big) in Table 2 ) outperforms the best previously reported models (including ensembles) by more than 2.0 BLEU, establishing a new state-of-the-art BLEU score of 28.4. The configuration of this model is listed in the bottom line of Table 3 . Training took 3.5 days on 8 P100 GPUs. Even our base model surpasses all previously published models and ensembles, at a fraction of the training cost of any of the competitive models."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "6.1", "section": "Machine Translation", "text": "On the WMT 2014 English-to-French translation task, our big model achieves a BLEU score of 41.0, outperforming all of the previously published single models, at less than 1/4 the training cost of the previous state-of-the-art model. The Transformer (big) model trained for English-to-French used dropout rate P drop = 0.1, instead of 0.3."}, {"cite_spans": [{"end": 263, "ref_id": "BIBREF37", "start": 259, "text": "[38]"}, {"end": 456, "ref_id": "BIBREF37", "start": 452, "text": "[38]"}], "eq_spans": [], "ref_spans": [], "sec_num": "6.1", "section": "Machine Translation", "text": "For the base models, we used a single model obtained by averaging the last 5 checkpoints, which were written at 10-minute intervals. For the big models, we averaged the last 20 checkpoints. We used beam search with a beam size of 4 and length penalty α = 0.6 [38] . These hyperparameters were chosen after experimentation on the development set. We set the maximum output length during inference to input length + 50, but terminate early when possible [38] ."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [{"end": 7, "ref_id": "TABREF1", "start": 6, "text": "2"}], "sec_num": "6.1", "section": "Machine Translation", "text": "Table 2 summarizes our results and compares our translation quality and training costs to other model architectures from the literature. We estimate the number of floating point operations used to train a model by multiplying the training time, the number of GPUs used, and an estimate of the sustained single-precision floating-point capacity of each GPU5 ."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [{"end": 315, "ref_id": "TABREF2", "start": 314, "text": "3"}], "sec_num": "6.2", "section": "Model Variations", "text": "To evaluate the importance of different components of the Transformer, we varied our base model in different ways, measuring the change in performance on English-to-German translation on the . We used beam search as described in the previous section, but no checkpoint averaging. We present these results in Table 3 ."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [{"end": 10, "ref_id": "TABREF2", "start": 9, "text": "3"}], "sec_num": "6.2", "section": "Model Variations", "text": "In Table 3 rows (A), we vary the number of attention heads and the attention key and value dimensions, keeping the amount of computation constant, as described in Section 3.2.2. While single-head attention is 0.9 BLEU worse than the best setting, quality also drops off with too many heads."}, {"cite_spans": [{"end": 473, "ref_id": "BIBREF8", "start": 470, "text": "[9]"}], "eq_spans": [], "ref_spans": [{"end": 10, "ref_id": "TABREF2", "start": 9, "text": "3"}], "sec_num": "6.2", "section": "Model Variations", "text": "In Table 3 rows (B), we observe that reducing the attention key size d k hurts model quality. This suggests that determining compatibility is not easy and that a more sophisticated compatibility function than dot product may be beneficial. We further observe in rows (C) and (D) that, as expected, bigger models are better, and dropout is very helpful in avoiding over-fitting. In row (E) we replace our sinusoidal positional encoding with learned positional embeddings [9] , and observe nearly identical results to the base model."}, {"cite_spans": [{"end": 383, "ref_id": "BIBREF36", "start": 379, "text": "[37]"}], "eq_spans": [], "ref_spans": [], "sec_num": "6.3", "section": "English Constituency Parsing", "text": "To evaluate if the Transformer can generalize to other tasks we performed experiments on English constituency parsing. This task presents specific challenges: the output is subject to strong structural constraints and is significantly longer than the input. Furthermore, RNN sequence-to-sequence models have not been able to attain state-of-the-art results in small-data regimes [37] ."}, {"cite_spans": [{"end": 119, "ref_id": "BIBREF24", "start": 115, "text": "[25]"}, {"end": 302, "ref_id": "BIBREF36", "start": 298, "text": "[37]"}], "eq_spans": [], "ref_spans": [], "sec_num": "6.3", "section": "English Constituency Parsing", "text": "We trained a 4-layer transformer with d model = 1024 on the Wall Street Journal (WSJ) portion of the Penn Treebank [25] , about 40K training sentences. We also trained it in a semi-supervised setting, using the larger high-confidence and BerkleyParser corpora from with approximately 17M sentences [37] . We used a vocabulary of 16K tokens for the WSJ only setting and a vocabulary of 32K tokens for the semi-supervised setting."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "6.3", "section": "English Constituency Parsing", "text": "We performed only a small number of experiments to select the dropout, both attention and residual (section 5.4), learning rates and beam size on the Section 22 development set, all other parameters remained unchanged from the English-to-German base translation model. During inference, we increased the maximum output length to input length + 300. We used a beam size of 21 and α = 0.3 for both WSJ only and the semi-supervised setting."}, {"cite_spans": [{"end": 234, "ref_id": "BIBREF7", "start": 231, "text": "[8]"}], "eq_spans": [], "ref_spans": [{"end": 22, "ref_id": "TABREF3", "start": 21, "text": "4"}], "sec_num": "6.3", "section": "English Constituency Parsing", "text": "Our results in Table 4 show that despite the lack of task-specific tuning our model performs surprisingly well, yielding better results than all previously reported models with the exception of the Recurrent Neural Network Grammar [8] ."}, {"cite_spans": [{"end": 51, "ref_id": "BIBREF36", "start": 47, "text": "[37]"}, {"end": 106, "ref_id": "BIBREF28", "start": 102, "text": "[29]"}], "eq_spans": [], "ref_spans": [], "sec_num": "6.3", "section": "English Constituency Parsing", "text": "In contrast to RNN sequence-to-sequence models [37] , the Transformer outperforms the Berkeley-Parser [29] even when training only on the WSJ training set of 40K sentences."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "7", "section": "Conclusion", "text": "In this work, we presented the Transformer, the first sequence transduction model based entirely on attention, replacing the recurrent layers most commonly used in encoder-decoder architectures with multi-headed self-attention."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "7", "section": "Conclusion", "text": "For translation tasks, the Transformer can be trained significantly faster than architectures based on recurrent or convolutional layers. On both WMT 2014 English-to-German and WMT 2014 English-to-French translation tasks, we achieve a new state of the art. In the former task our best model outperforms even all previously reported ensembles."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "7", "section": "Conclusion", "text": "We are excited about the future of attention-based models and plan to apply them to other tasks. We plan to extend the Transformer to problems involving input and output modalities other than text and to investigate local, restricted attention mechanisms to efficiently handle large inputs and outputs such as images, audio and video. Making generation less sequential is another research goals of ours."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": "7", "section": "Conclusion", "text": "The code we used to train and evaluate our models is available at https://github.com/ tensorflow/tensor2tensor. "}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "Input-Input Layer5", "text": "The Full attentions for head 5. Bottom: Isolated attentions from just the word 'its' for attention heads 5 and 6. Note that the attentions are very sharp for this word."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "Input-Input Layer5", "text": "The "}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "", "text": "To illustrate why the dot products get large, assume that the components of q and k are independent random variables with mean 0 and variance 1. Then their dot product, q • k = d k i=1 qiki, has mean 0 and variance d k ."}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "", "text": "We used values of 2.8, 3.7,"}, {"cite_spans": [], "eq_spans": [], "ref_spans": [], "sec_num": null, "section": "", "text": "6.0 and 9.5 TFLOPS for K80, K40, M40 and P100, respectively."}], "paper_id": "Transformer.pdf", "ref_entries": {"FIGREF0": {"fig_num": "1", "num": null, "text": "Figure 1: The Transformer -model architecture.", "type_str": "figure", "uris": null}, "FIGREF1": {"fig_num": "2", "num": null, "text": "Figure 2: (left) Scaled Dot-Product Attention. (right) Multi-Head Attention consists of several attention layers running in parallel.", "type_str": "figure", "uris": null}, "FIGREF2": {"fig_num": "3", "num": null, "text": "Figure 3: An example of the attention mechanism following long-distance dependencies in the encoder self-attention in layer 5 of 6. Many of the attention heads attend to a distant dependency of the verb 'making', completing the phrase 'making...more difficult'. Attentions here shown only for the word 'making'. Different colors represent different heads. Best viewed in color.", "type_str": "figure", "uris": null}, "FIGREF3": {"fig_num": "4", "num": null, "text": "Figure4: Two attention heads, also in layer 5 of 6, apparently involved in anaphora resolution. Top: Full attentions for head 5. Bottom: Isolated attentions from just the word 'its' for attention heads 5 and 6. Note that the attentions are very sharp for this word.", "type_str": "figure", "uris": null}, "FIGREF4": {"fig_num": "5", "num": null, "text": "Figure5: Many of the attention heads exhibit behaviour that seems related to the structure of the sentence. We give two such examples above, from two different heads from the encoder self-attention at layer 5 of 6. The heads clearly learned to perform different tasks.", "type_str": "figure", "uris": null}, "TABREF0": {"content": "<table><tr><td>Layer Type</td><td>Complexity per Layer Sequential Maximum Path Length</td></tr><tr><td/><td>Operations</td></tr><tr><td>Self-Attention</td><td>O</td></tr></table>", "html": null, "num": null, "text": "Maximum path lengths, per-layer complexity and minimum number of sequential operations for different layer types. n is the sequence length, d is the representation dimension, k is the kernel size of convolutions and r the size of the neighborhood in restricted self-attention.", "type_str": "table"}, "TABREF1": {"content": "<table><tr><td>Model</td><td colspan=\"2\">BLEU EN-DE EN-FR</td><td>Training Cost (FLOPs) EN-DE EN-FR</td></tr><tr><td>ByteNet [18]</td><td>23.75</td><td/><td/></tr><tr><td>Deep-Att + PosUnk [39]</td><td/><td>39.2</td><td>1.0 • 10 20</td></tr><tr><td>GNMT + RL [38]</td><td>24.6</td><td>39.92</td><td>2.3 • 10 19 1.4 • 10 20</td></tr><tr><td>ConvS2S [9]</td><td>25.16</td><td>40.46</td><td>9.6 • 10 18 1.5 • 10 20</td></tr><tr><td>MoE [32]</td><td>26.03</td><td>40.56</td><td>2.0 • 10 19 1.2 • 10 20</td></tr><tr><td>Deep-Att + PosUnk Ensemble [39]</td><td/><td>40.4</td><td>8.0 • 10 20</td></tr><tr><td>GNMT + RL Ensemble [38]</td><td>26.30</td><td>41.16</td><td>1.8 • 10 20 1.1 • 10 21</td></tr><tr><td>ConvS2S Ensemble [9]</td><td>26.36</td><td>41.29</td><td>7.7 • 10 19 1.2 • 10 21</td></tr><tr><td>Transformer (base model)</td><td>27.3</td><td>38.1</td><td>3.3 • 10 18</td></tr><tr><td>Transformer (big)</td><td>28.4</td><td>41.8</td><td>2.3 • 10 19</td></tr></table>", "html": null, "num": null, "text": "The Transformer achieves better BLEU scores than previous state-of-the-art models on the English-to-German and English-to-French newstest2014 tests at a fraction of the training cost.", "type_str": "table"}, "TABREF2": {"content": "<table><tr><td/><td colspan=\"2\">N d model</td><td>d ff</td><td>h</td><td>d k</td><td>d v</td><td colspan=\"2\">P drop ϵ ls</td><td>train steps (dev) (dev) PPL BLEU params ×10 6</td></tr><tr><td colspan=\"2\">base 6</td><td>512</td><td colspan=\"2\">2048 8</td><td>64</td><td>64</td><td>0.1</td><td colspan=\"2\">0.1 100K 4.92</td><td>25.8</td><td>65</td></tr><tr><td/><td/><td/><td/><td colspan=\"3\">1 512 512</td><td/><td/><td>5.29</td><td>24.9</td></tr><tr><td>(A)</td><td/><td/><td/><td colspan=\"3\">4 128 128 16 32 32</td><td/><td/><td>5.00 4.91</td><td>25.5 25.8</td></tr><tr><td/><td/><td/><td/><td colspan=\"2\">32 16</td><td>16</td><td/><td/><td>5.01</td><td>25.4</td></tr><tr><td>(B)</td><td/><td/><td/><td/><td>16 32</td><td/><td/><td/><td>5.16 5.01</td><td>25.1 25.4</td><td>58 60</td></tr><tr><td/><td>2</td><td/><td/><td/><td/><td/><td/><td/><td>6.11</td><td>23.7</td><td>36</td></tr><tr><td/><td>4</td><td/><td/><td/><td/><td/><td/><td/><td>5.19</td><td>25.3</td><td>50</td></tr><tr><td/><td>8</td><td/><td/><td/><td/><td/><td/><td/><td>4.88</td><td>25.5</td><td>80</td></tr><tr><td>(C)</td><td/><td>256</td><td/><td/><td>32</td><td>32</td><td/><td/><td>5.75</td><td>24.5</td><td>28</td></tr><tr><td/><td/><td>1024</td><td/><td/><td colspan=\"2\">128 128</td><td/><td/><td>4.66</td><td>26.0</td><td>168</td></tr><tr><td/><td/><td/><td>1024</td><td/><td/><td/><td/><td/><td>5.12</td><td>25.4</td><td>53</td></tr><tr><td/><td/><td/><td>4096</td><td/><td/><td/><td/><td/><td>4.75</td><td>26.2</td><td>90</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td>0.0</td><td/><td>5.77</td><td>24.6</td></tr><tr><td>(D)</td><td/><td/><td/><td/><td/><td/><td>0.2</td><td>0.0</td><td>4.95 4.67</td><td>25.5 25.3</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td>0.2</td><td>5.47</td><td>25.7</td></tr><tr><td>(E)</td><td/><td colspan=\"7\">positional embedding instead of sinusoids</td><td>4.92</td><td>25.7</td></tr><tr><td>big</td><td>6</td><td colspan=\"3\">1024 4096 16</td><td/><td/><td>0.3</td><td/><td>300K 4.33</td><td>26.4</td><td>213</td></tr><tr><td colspan=\"4\">development set, newstest2013</td><td/><td/><td/><td/><td/></tr></table>", "html": null, "num": null, "text": "Variations on the Transformer architecture. Unlisted values are identical to those of the base model. All metrics are on the English-to-German translation development set, newstest2013. Listed perplexities are per-wordpiece, according to our byte-pair encoding, and should not be compared to per-word perplexities.", "type_str": "table"}, "TABREF3": {"content": "<table><tr><td>Parser</td><td>Training</td><td>WSJ 23 F1</td></tr><tr><td colspan=\"2\">Vinyal<PERSON> &amp; <PERSON> el al. (2014) [37] WSJ only, discriminative</td><td>88.3</td></tr><tr><td><PERSON><PERSON> et al. (2006) [29]</td><td>WSJ only, discriminative</td><td>90.4</td></tr><tr><td><PERSON> et al. (2013) [40]</td><td>WSJ only, discriminative</td><td>90.4</td></tr><tr><td><PERSON> et al. (2016) [8]</td><td>WSJ only, discriminative</td><td>91.7</td></tr><tr><td>Transformer (4 layers)</td><td>WSJ only, discriminative</td><td>91.3</td></tr><tr><td><PERSON> et al. (2013) [40]</td><td>semi-supervised</td><td>91.3</td></tr><tr><td>Huang &amp; Harper (2009) [14]</td><td>semi-supervised</td><td>91.3</td></tr><tr><td>McClosky et al. (2006) [26]</td><td>semi-supervised</td><td>92.1</td></tr><tr><td>Vinyals &amp; Kaiser el al. (2014) [37]</td><td>semi-supervised</td><td>92.1</td></tr><tr><td>Transformer (4 layers)</td><td>semi-supervised</td><td>92.7</td></tr><tr><td>Luong et al. (2015) [23]</td><td>multi-task</td><td>93.0</td></tr><tr><td>Dyer et al. (2016) [8]</td><td>generative</td><td>93.3</td></tr></table>", "html": null, "num": null, "text": "The Transformer generalizes well to English constituency parsing (Results are on Section 23 of WSJ)", "type_str": "table"}}}, "title": "Provided proper attribution is provided, Google hereby grants permission to reproduce the tables and figures in this paper solely for use in journalistic or scholarly works. Attention Is All You Need", "venue": null, "year": ""}