# 开发测试环境示例

# 服务配置
HOST=0.0.0.0
PORT=8000

# 数据库配置信息
DATABASE_URL=postgres://postgres:123456@localhost:5432/hi_ideagen
# redis的地址
REDIS_URL=redis://localhost:6379/0
# redis的密码
REDIS_PASSWORD=abc123456
# 安全配置，用于生成JWT令牌，请勿泄露，用于加密和安全处理的密钥，相当于盐值
SECRET_KEY=your_secret_key_here
# 是否开启同一账号仅允许单设备登录
IS_OPEN_SSO=OFF

# 默认管理员配置
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=strong_password_here

# 默认管理员角色，第一次启动时，会自动创建一个超级管理员用户，这是角色代码
DEFAULT_ADMIN_ROLE=super_admin

# API密钥前缀-已废弃，请勿使用
API_KEY_PREFIX=sk-

# 研究配置
# SerpAPI API Key，用于Google搜索
SERPAPI_API_KEY=your_serpapi_api_key
# Jina API Key，用于Jina搜索
JINA_API_KEY=your_jina_api_key
#泛知识平台地址
WZBJ_API_URL=

# GROBID服务配置
GROBID_BASE_URL=http://localhost:8070

# 搜索配置
SEARCH_RESULTS_LIMIT=10
# 研究迭代次数限制（搜索-分析-改进查询的最大循环次数，每次循环系统会根据已获取的信息生成新的搜索查询，直到达到信息充分或达到迭代上限）
RESEARCH_ITERATION_LIMIT=2
# 谷歌搜索相关上下文的时候允许的最大搜索次数
MAX_SEARCH_QUERIES=10
# 不开启文献总结的情况下，允许收集的最大contexts数
MAX_CONTEXTS=5
# 开启文献总结的情况下，允许收集的最大contexts数（contexts + literature的总数）
MAX_LITERATURE_AND_CONTEXTS=35
# 每次生成报告的最大搜索参考文献数
MAX_LITERATURES_COUNT=35

# 幻觉审查的google搜索条目数
MAX_HALLUCINATION_DETECTION_SEARCH=10

#谷歌搜索引擎
SEARCH_ENGINE=google  
#参考文献谷歌搜索引擎
LITERATURE_LIBRARY_SEARCH_ENGINE=google_scholar
#普通的网页搜索引擎（泛化里面的可研报告会用到）
WEB_SEARCH_ENGINE=google

######LLM参数
# 温度
LLM_DEFAULT_TEMPERATURE=1
# 上采样
LLM_DEFAULT_TOP_K=80
# 上采样
LLM_DEFAULT_TOP_P=0.6


# 报告配置
# 报告最小字数
REPORT_MIN_WORDS=1000
# 报告最大字数
REPORT_MAX_WORDS=5000

# 参考资料上传文件数量限制
MAX_BATCH_ATTACHMENT_COUNT=10
    

# 日志配置
# 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
# 日志目录，宿主机挂载目录，用绝对路径表示，跟上面的配置不一样，这个是Docker宿主机挂载目录
LOG_ROOT_DIR=./logs
# 项目的日志文件具体输出的路径目录，本地开发用相对路径./logs，Docker用绝对路径/app/logs
LOG_DIR=./logs
# 日志保留天数，超过此天数的日志文件将被自动删除
LOG_RETENTION_DAYS=30
# 时区设置，Docker容器时区配置，默认东八区
TZ=Asia/Shanghai
# 体验用户允许看到的最大文字（报告和大纲都一样）
TRIAL_USER_MAX_TEXT=10000

# SaaS平台配置
# 机构默认使用次数上限
DEFAULT_ORG_USAGE_COUNT=30
# 用户默认使用次数上限
DEFAULT_USER_USAGE_COUNT=5
# 机构默认模型
DEFAULT_ORG_MODEL=anthropic/claude-sonnet-4

# squid代理服务器的相关配置（用来做serpAPI的转发代理），
# 如果需要开启代理服务，下面三个参数配置上就开启了。
PROXY_URL=
PROXY_USERNAME=
PROXY_PASSWORD=

# 会议纪要的语音识别需要用到的阿里百炼的api_key
ALI_LLM_API_KEY=""
# .env.example 文件中添加以下配置

# 阿里云配置
ALI_LLM_API_KEY=your_alibaba_api_key_here

# 科大讯飞ASR配置
XFYUN_APP_ID=your_xfyun_app_id_here
XFYUN_API_KEY=your_xfyun_api_key_here

# ASR默认提供商 (alibaba 或 xfyun)
DEFAULT_ASR_PROVIDER=alibaba