from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "docgen_feasibility_meta" ADD "risk_assessment" TEXT;
        ALTER TABLE "docgen_feasibility_meta" ADD "investment_amount" DECIMAL(15,2);
        ALTER TABLE "docgen_think_tank_meta" ALTER COLUMN "target" TYPE VARCHAR(500) USING "target"::VARCHAR(500);
        ALTER TABLE "docgen_think_tank_meta" ALTER COLUMN "analysis_method" TYPE VARCHAR(500) USING "analysis_method"::VARCHAR(500);
        ALTER TABLE "literatures" ADD COLUMN paper_type VARCHAR(10) NULL;
        COMMENT ON COLUMN literatures.paper_type IS '文献类型标识';
        """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "docgen_think_tank_meta" ALTER COLUMN "target" TYPE VARCHAR(50) USING "target"::VARCHAR(50);
        ALTER TABLE "docgen_think_tank_meta" ALTER COLUMN "analysis_method" TYPE VARCHAR(50) USING "analysis_method"::VARCHAR(50);
        ALTER TABLE "docgen_feasibility_meta" DROP COLUMN "risk_assessment";
        ALTER TABLE "docgen_feasibility_meta" DROP COLUMN "investment_amount";"""
