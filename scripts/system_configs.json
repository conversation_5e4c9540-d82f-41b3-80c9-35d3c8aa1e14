{"version": "1.0.0", "description": "系统配置初始数据", "configs": [{"key": "defaultSystemSettingJsonMd5", "category": "system", "description": "系统配置JSON的MD5值，用于检测配置变化", "value": "", "defaultValue": "", "orderNo": 0}, {"key": "PROJECT_CATEGORIES", "category": "DOC_GEN", "description": "项目分类配置，包含分类和模块的两层结构", "value": "", "defaultValue": "[{\"key\":\"approval\",\"title\":\"申报审批\",\"modules\":[{\"key\":\"DOC_GEN\",\"title\":\"项目申报书生成\"}]},{\"key\":\"office\",\"title\":\"日常办公\",\"modules\":[{\"key\":\"MEETING_GEN\",\"title\":\"会议纪要\"}]},{\"key\":\"research\",\"title\":\"研究分析\",\"modules\":[{\"key\":\"MARKET_INVESTMENT\",\"title\":\"市场行业投资研习报告\"},{\"key\":\"FEASIBILITY\",\"title\":\"可行性研究报告\"},{\"key\":\"THINK_TANK\",\"title\":\"智库报告\"}]},{\"key\":\"education\",\"title\":\"学术教育\",\"modules\":[{\"key\":\"LITERATURE_REVIEW\",\"title\":\"文献综述\"},{\"key\":\"CLASS_NOTES\",\"title\":\"课堂笔记\"}]},{\"key\":\"business\",\"title\":\"商业文档\",\"modules\":[]},{\"key\":\"technical\",\"title\":\"技术文档\",\"modules\":[]}]", "orderNo": 1}, {"key": "AGENT_PAPER2CODE_API_URL", "category": "PI", "description": "Paper2Code调用地址", "value": "", "defaultValue": "https://openrouter.ai/api/v1", "orderNo": 1}, {"key": "AGENT_PAPER2CODE_API_KEY", "category": "PI", "description": "Paper2Code APIKEY", "value": "", "defaultValue": "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", "orderNo": 2}, {"key": "AGENT_PAPER2CODE_MODEL", "category": "PI", "description": "Paper2Code使用的模型", "value": "", "defaultValue": "openai/o3-mini", "orderNo": 3}]}