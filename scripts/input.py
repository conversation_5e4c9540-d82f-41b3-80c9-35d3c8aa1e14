import os
import sys
from datetime import datetime

print("当前路径:", os.getcwd())
print("sys.path:", sys.path)

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import json
from tortoise import Tortoise
from app.models.dictionary import Dictionary
from dotenv import load_dotenv

load_dotenv()  # 可从 .env 文件加载数据库连接

DATABASE_URL = os.getenv("DATABASE_URL")
print("DATABASE_URL:", DATABASE_URL)

async def run():
    await Tortoise.init(
        db_url=DATABASE_URL,
        modules={"models": ["app.models"]}
    )
    await Tortoise.generate_schemas()

    with open("scripts/dict.json", "r", encoding="utf-8") as f:
        records = json.load(f)
        for r in records:
            r["created_at"] = datetime.now()
            r["updated_at"] = datetime.now()
            is_existed = await Dictionary.filter(value=r["value"]).first()
            if not is_existed:
                await Dictionary.create(**r)

    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(run())
