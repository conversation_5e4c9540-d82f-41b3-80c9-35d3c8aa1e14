# Hi-IdeaGen 后端项目目录结构说明

## 核心配置模块 (app/core/)
- `app/core/config.py`：全局配置，已支持 Redis 相关配置（REDIS_URL）
- `app/core/redis_client.py`：全局 Redis 连接池管理，支持单点登录 token 存储与校验

## 数据模型层 (app/models/)
- `app/models/model_call_log.py`：大模型调用日志表 ORM 定义，记录每次大模型调用的关键信息
- `app/models/project_url_summary.py`：项目网页链接及内容总结表 ORM 定义，记录项目相关网页及其内容摘要
- `app/models/pi_papertox_agent.py`：PaperToX Agent 数据模型，管理 AI 代理广场的代理信息，包括状态、分类、使用统计等

## API 接口层 (app/api/)

### 数据访问层 (app/api/repository/)
- `app/api/repository/voice_text.py`：录音转文字的仓储层，提供创建与更新接口
- `app/api/repository/pi_papertox_agent.py`：PaperToX Agent 数据访问层，提供 CRUD 操作、搜索筛选、统计等功能

### 请求响应模型 (app/api/schemas/)
- `app/api/schemas/model_call_log.py`：大模型调用日志的 Pydantic schema 定义
- `app/api/schemas/project_url_summary.py`：项目网页链接及内容总结的 Pydantic schema 定义
- `app/api/schemas/voice_text.py`：录音转文字的请求与响应模型
- `app/api/schemas/literature/pi_papertox_agent_schemas.py`：PaperToX Agent 的请求响应模型，包括创建、更新、查询、统计等 Schema

### 路由接口 (app/api/routes/)
- `app/api/routes/literature/pi_papertox_agent.py`：PaperToX Agent 广场的 API 接口，提供列表查询、详情获取、创建、更新、删除、恢复、使用统计等功能

## 新增功能说明

### PaperToX Agent 广场模块
基于图片原型设计的 AI 代理管理平台，支持：

1. **代理管理**：创建、编辑、删除 AI 代理
2. **状态管理**：可用、开发中、即将推出三种状态
3. **分类标签**：支持按分类和标签进行筛选搜索
4. **使用统计**：记录和展示代理使用次数
5. **机构隔离**：支持多机构数据隔离
6. **权限控制**：基于角色的访问控制
7. **逻辑删除**：软删除机制，支持恢复

### 数据库设计
- 使用 UUID 作为主键，确保全局唯一性
- 支持机构隔离和权限控制
- 包含完整的审计字段（创建人、更新人、时间戳）
- 支持逻辑删除和恢复功能 